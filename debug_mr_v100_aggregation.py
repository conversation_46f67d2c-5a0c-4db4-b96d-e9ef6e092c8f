#!/usr/bin/env python
import os
import sys
import django

# 设置Django环境
sys.path.append('spug_api')
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'spug.settings')
django.setup()

from apps.model_storage.models import TestTask, GPUDevice, PerformanceTestData

print("=== 调试MR-V100数据聚合问题 ===")

# 1. 检查MR-V100的所有相关数据
print("\n1. 检查MR-V100的所有TestTask数据:")
mr_v100_tasks = TestTask.objects.filter(gpu_model='MR-V100')
print(f"MR-V100相关的TestTask数量: {mr_v100_tasks.count()}")

for task in mr_v100_tasks:
    print(f"  任务ID: {task.id}")
    print(f"    模型名称: {task.model_name}")
    print(f"    tokens_per_second: {task.tokens_per_second} (类型: {type(task.tokens_per_second)})")
    print(f"    ttft_ms: {task.ttft_ms} (类型: {type(task.ttft_ms)})")
    print(f"    创建时间: {task.created_at}")
    print(f"    更新时间: {task.updated_at}")
    print()

# 2. 检查MR-V100的PerformanceTestData数据
print("\n2. 检查MR-V100的PerformanceTestData数据:")
mr_v100_gpu = GPUDevice.objects.filter(name='MR-V100').first()
if mr_v100_gpu:
    # 通过外键关联
    perf_data_fk = PerformanceTestData.objects.filter(gpu_device=mr_v100_gpu)
    print(f"通过外键关联的PerformanceTestData数量: {perf_data_fk.count()}")
    for data in perf_data_fk:
        print(f"  记录ID: {data.id}")
        print(f"    模型名称: {data.model_name}")
        print(f"    output_token_throughput: {data.output_token_throughput}")
        print(f"    avg_ttft: {data.avg_ttft}")
        print(f"    machine_model: {data.machine_model}")
        print()
    
    # 通过machine_model字符串匹配
    perf_data_str = PerformanceTestData.objects.filter(machine_model='MR-V100')
    print(f"通过machine_model字符串匹配的PerformanceTestData数量: {perf_data_str.count()}")
    for data in perf_data_str:
        print(f"  记录ID: {data.id}")
        print(f"    模型名称: {data.model_name}")
        print(f"    output_token_throughput: {data.output_token_throughput}")
        print(f"    avg_ttft: {data.avg_ttft}")
        print()

# 3. 手动计算最佳性能数据
print("\n3. 手动计算最佳性能数据:")

# 从TestTask计算
def _to_pos_float(v):
    try:
        f = float(v)
        return f if f > 0 else None
    except Exception:
        return None

task_qs = TestTask.objects.filter(gpu_model='MR-V100').values('tokens_per_second', 'ttft_ms')
tps_list = [v for v in (_to_pos_float(x['tokens_per_second']) for x in task_qs) if v is not None]
ttft_list = [v for v in (_to_pos_float(x['ttft_ms']) for x in task_qs) if v is not None]

print(f"从TestTask获取的有效tokens_per_second值: {tps_list}")
print(f"从TestTask获取的有效ttft_ms值: {ttft_list}")

best_tps_from_tasks = max(tps_list) if tps_list else None
best_ttft_from_tasks = min(ttft_list) if ttft_list else None

print(f"从TestTask计算的最佳性能: tokens/s={best_tps_from_tasks}, ttft_ms={best_ttft_from_tasks}")

# 从PerformanceTestData计算
if mr_v100_gpu:
    from django.db.models import Max, Min
    from django.db.models import Q
    
    perf_qs = PerformanceTestData.objects.filter(Q(gpu_device=mr_v100_gpu) | Q(machine_model='MR-V100'))
    
    if perf_qs.exists():
        best_tps_from_perf = perf_qs.filter(output_token_throughput__gt=0).aggregate(m=Max('output_token_throughput'))['m']
        best_ttft_from_perf = perf_qs.filter(avg_ttft__gt=0).aggregate(m=Min('avg_ttft'))['m']
        print(f"从PerformanceTestData计算的最佳性能: tokens/s={best_tps_from_perf}, ttft_ms={best_ttft_from_perf}")
    else:
        print("没有找到相关的PerformanceTestData")

# 4. 检查GPUDevice.to_dict()的实际输出
print("\n4. 检查GPUDevice.to_dict()的实际输出:")
if mr_v100_gpu:
    gpu_dict = mr_v100_gpu.to_dict()
    print(f"GPUDevice.to_dict()结果:")
    print(f"  tokens_per_second: {gpu_dict['tokens_per_second']}")
    print(f"  ttft_ms: {gpu_dict['ttft_ms']}")
    print(f"  tested_models_auto: {gpu_dict['tested_models_auto']}")
    print(f"  tested_models_count: {gpu_dict['tested_models_count']}")

# 5. 创建一个更高性能的TestTask来测试
print("\n5. 创建一个更高性能的TestTask来测试:")
try:
    # 创建一个性能更高的测试任务
    high_perf_task = TestTask.objects.create(
        model_name='高性能测试模型',
        tester='测试员',
        gpu_model='MR-V100',
        model_type='inference',
        start_date='2025-08-10',
        end_date='2025-08-10',
        priority='p1',
        test_status='completed',
        tokens_per_second=300.0,  # 更高的吞吐量
        ttft_ms=100.0  # 更低的TTFT
    )
    print(f"创建了高性能测试任务: ID={high_perf_task.id}, tokens/s=300.0, ttft_ms=100.0")
    
    # 重新计算GPU性能
    gpu_dict_after = mr_v100_gpu.to_dict()
    print(f"创建高性能任务后的GPU性能:")
    print(f"  tokens_per_second: {gpu_dict_after['tokens_per_second']}")
    print(f"  ttft_ms: {gpu_dict_after['ttft_ms']}")
    
    # 检查是否取到了最高值
    if gpu_dict_after['tokens_per_second'] == 300.0:
        print("✅ 正确取到了最高的tokens_per_second值")
    else:
        print(f"❌ 没有取到最高值，期望300.0，实际{gpu_dict_after['tokens_per_second']}")
    
    if gpu_dict_after['ttft_ms'] == 100.0:
        print("✅ 正确取到了最低的ttft_ms值")
    else:
        print(f"❌ 没有取到最低值，期望100.0，实际{gpu_dict_after['ttft_ms']}")
    
    # 清理测试数据
    high_perf_task.delete()
    print("已清理测试数据")
    
except Exception as e:
    print(f"创建测试任务失败: {e}")

print("\n=== 调试完成 ===")
print("如果发现问题，可能需要检查GPUDevice.to_dict()方法中的聚合逻辑。")
