import os
import sys
import django

# 设置Django环境
sys.path.append('spug_api')
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'spug.settings')
django.setup()

from apps.model_storage.models import PerformanceTestData, GPUDevice

print("=== PerformanceTestData 数据检查 ===")
print(f"总数据条数: {PerformanceTestData.objects.count()}")
print(f"machine_model非空的数据: {PerformanceTestData.objects.exclude(machine_model='').count()}")
print(f"machine_model为空的数据: {PerformanceTestData.objects.filter(machine_model='').count()}")

print("\n=== 前10条数据的machine_model值 ===")
for data in PerformanceTestData.objects.all()[:10]:
    print(f"ID: {data.id}, machine_model: '{data.machine_model}', model_name: {data.model_name}")

print("\n=== 所有不同的machine_model值 ===")
machine_models = PerformanceTestData.objects.values_list('machine_model', flat=True).distinct()
for model in machine_models:
    count = PerformanceTestData.objects.filter(machine_model=model).count()
    print(f"'{model}': {count}条数据")

print("\n=== GPU设备列表 ===")
for gpu in GPUDevice.objects.all()[:10]:
    print(f"GPU: {gpu.name}, 厂商: {gpu.vendor}")

print("\n=== 检查A100相关数据 ===")
a100_exact = PerformanceTestData.objects.filter(machine_model__icontains='A100').count()
a100_fuzzy = PerformanceTestData.objects.filter(model_name__icontains='A100').count()
print(f"machine_model包含A100的数据: {a100_exact}条")
print(f"model_name包含A100的数据: {a100_fuzzy}条")

print("\n=== 模型名称统计 ===")
model_names = PerformanceTestData.objects.values_list('model_name', flat=True).distinct()
print(f"不同模型数量: {len(model_names)}")
for model in list(model_names)[:10]:
    count = PerformanceTestData.objects.filter(model_name=model).count()
    print(f"模型 '{model}': {count}条数据")