from django.core.management.base import BaseCommand
from apps.model_storage.models import PerformanceTestData, GPUDevice


class Command(BaseCommand):
    help = 'Backfill PerformanceTestData.gpu_device from machine_model matching GPUDevice.name'

    def handle(self, *args, **options):
        updated = 0
        total = 0
        missing = 0
        for p in PerformanceTestData.objects.all().iterator():
            total += 1
            if p.gpu_device_id:
                continue
            name = (p.machine_model or '').strip()
            if not name:
                missing += 1
                continue
            gpu = GPUDevice.objects.filter(name=name).first()
            if gpu:
                p.gpu_device = gpu
                p.save(update_fields=['gpu_device'])
                updated += 1
            else:
                missing += 1

        self.stdout.write(self.style.SUCCESS(
            f'Backfill done. total={total}, updated={updated}, missing_or_unmatched={missing}'
        ))


