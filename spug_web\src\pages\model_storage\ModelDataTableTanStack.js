import React, { useState, useEffect, useMemo, useCallback } from 'react';
import {
  Card,
  Button,
  message,
  Space,
  Modal,
  Input,
  Tooltip,
  Tag,
  Progress,
  Spin,
  Upload,
  Statistic,
  Row,
  Col
} from 'antd';
import {
  EditOutlined,
  ReloadOutlined,
  DeleteOutlined,
  PlusOutlined,
  SaveOutlined,
  DownloadOutlined,
  UploadOutlined,
  UndoOutlined
} from '@ant-design/icons';
import { useParams } from 'react-router-dom';
import { 
  useReactTable, 
  getCoreRowModel, 
  getSortedRowModel, 
  getFilteredRowModel, 
  flexRender, 
  createColumnHelper 
} from '@tanstack/react-table';
import * as XLSX from 'xlsx';
import http from 'libs/http';
import styles from './ModelDataTableTanStack.module.less';

const { Search } = Input;

function ModelDataTableTanStack() {
  const { modelName } = useParams();
  const [data, setData] = useState([]);
  const [loading, setLoading] = useState(true);
  const [globalFilter, setGlobalFilter] = useState('');
  const [sorting, setSorting] = useState([]);
  const [editingCell, setEditingCell] = useState(null); // { rowId, columnId }
  const [editingValue, setEditingValue] = useState('');
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);
  const [averageStats, setAverageStats] = useState({ avgOutputTokenThroughput: 0, avgTTFT: 0 });

  // 获取数据
  const fetchData = useCallback(async () => {
    setLoading(true);
    try {
      const response = await http.get('/api/model-storage/performance-test-data/', {
        params: { model_name: modelName }
      });

      const transformedData = response.map((item, index) => ({
        id: item.id || `temp_${Date.now()}_${index}`,
        filename: item.filename || '',
        success_requests: item.success_requests,
        benchmark_duration: item.benchmark_duration,
        input_tokens: item.input_tokens,
        output_tokens: item.output_tokens,
        request_throughput: item.request_throughput,
        output_token_throughput: item.output_token_throughput,
        total_token_throughput: item.total_token_throughput,
        avg_ttft: item.avg_ttft,
        median_ttft: item.median_ttft,
        p99_ttft: item.p99_ttft,
        avg_tpot: item.avg_tpot,
        median_tpot: item.median_tpot,
        p99_tpot: item.p99_tpot,
        // 模型基础信息部分
        machine_model: item.machine_model || '',
        dataset: item.dataset || '',
        data_type: item.data_type || '',
        framework: item.framework || '',
        framework_version: item.framework_version || '',
        // 系统信息部分
        topology: item.topology || '',
        cpu: item.cpu || '',
        memory: item.memory || '',
        fan_mode: item.fan_mode || '',
        iommu_status: item.iommu_status || '',
        network_card: item.network_card || '',
        cpu_mode: item.cpu_mode || '',
        os_kernel: item.os_kernel || '',
        network_type: item.network_type || '',
        bios_version: item.bios_version || '',
        gpu_firmware_version: item.gpu_firmware_version || '',
        gpu_driver_version: item.gpu_driver_version || '',
        image_version_info: item.image_version_info || '',
        remarks: item.remarks || '',
        _modified: false,
        _isNew: item.id === null || item.id === undefined
      }));

      setData(transformedData);
      setHasUnsavedChanges(false);
      
      // 计算平均值
      calculateAverageStats(transformedData);
    } catch (error) {
      console.error('获取数据失败:', error);
      message.error('获取数据失败');
    } finally {
      setLoading(false);
    }
  }, [modelName]);

  useEffect(() => {
    fetchData();
  }, [fetchData]);

  // 计算平均值统计
  const calculateAverageStats = useCallback((dataArray) => {
    if (!dataArray || dataArray.length === 0) {
      setAverageStats({ avgOutputTokenThroughput: 0, avgTTFT: 0 });
      return;
    }

    const validOutputTokenThroughput = dataArray
      .map(item => item.output_token_throughput)
      .filter(val => val !== null && val !== undefined && !isNaN(val));
    
    const validAvgTTFT = dataArray
      .map(item => item.avg_ttft)
      .filter(val => val !== null && val !== undefined && !isNaN(val));

    const avgOutputTokenThroughput = validOutputTokenThroughput.length > 0 
      ? validOutputTokenThroughput.reduce((sum, val) => sum + parseFloat(val), 0) / validOutputTokenThroughput.length
      : 0;

    const avgTTFT = validAvgTTFT.length > 0
      ? validAvgTTFT.reduce((sum, val) => sum + parseFloat(val), 0) / validAvgTTFT.length
      : 0;

    setAverageStats({
      avgOutputTokenThroughput: Math.round(avgOutputTokenThroughput * 100) / 100,
      avgTTFT: Math.round(avgTTFT * 100) / 100
    });
  }, []);

  // 更新单元格数据
  const updateCell = useCallback((rowId, columnId, value) => {
    setData(prevData => {
      const newData = prevData.map(row => {
        if (row.id === rowId) {
          const column = columns.find(col => col.id === columnId);
          let processedValue = value;
          
          // 数据类型转换
          if (column && column.meta?.type === 'number') {
            if (value === '' || value === null || value === undefined) {
              processedValue = null;
            } else {
              const numValue = parseFloat(value);
              processedValue = isNaN(numValue) ? null : numValue;
            }
          }
          
          return { 
            ...row, 
            [columnId]: processedValue,
            _modified: true 
          };
        }
        return row;
      });
      
      // 如果修改的是关键指标，重新计算平均值
      if (columnId === 'output_token_throughput' || columnId === 'avg_ttft') {
        calculateAverageStats(newData);
      }
      
      return newData;
    });
    setHasUnsavedChanges(true);
  }, [calculateAverageStats]);

  // 开始编辑单元格
  const startEditing = useCallback((rowId, columnId, currentValue) => {
    setEditingCell({ rowId, columnId });
    setEditingValue(currentValue || '');
  }, []);

  // 完成编辑
  const finishEditing = useCallback(() => {
    if (editingCell) {
      updateCell(editingCell.rowId, editingCell.columnId, editingValue);
      setEditingCell(null);
      setEditingValue('');
    }
  }, [editingCell, editingValue, updateCell]);

  // 取消编辑
  const cancelEditing = useCallback(() => {
    setEditingCell(null);
    setEditingValue('');
  }, []);

  // 可编辑单元格组件
  const EditableCell = ({ getValue, row, column, table }) => {
    const initialValue = getValue();
    const isEditing = editingCell?.rowId === row.original.id && editingCell?.columnId === column.id;
    
    if (isEditing) {
      return (
        <Input
          value={editingValue}
          onChange={(e) => setEditingValue(e.target.value)}
          onPressEnter={finishEditing}
          onBlur={finishEditing}
          onKeyDown={(e) => {
            if (e.key === 'Escape') {
              cancelEditing();
            }
          }}
          autoFocus
          size="small"
          style={{ width: '100%' }}
        />
      );
    }

    return (
      <div
        className={styles.editableCell}
        onClick={() => startEditing(row.original.id, column.id, initialValue)}
        title="点击编辑"
      >
        {initialValue || '-'}
      </div>
    );
  };

  // 表格列定义
  const columnHelper = createColumnHelper();
  const columns = useMemo(() => [
    columnHelper.accessor('filename', {
      id: 'filename',
      header: '文件名',
      size: 200,
      cell: EditableCell,
      meta: { type: 'text' }
    }),
    columnHelper.accessor('success_requests', {
      id: 'success_requests',
      header: '成功请求数',
      size: 120,
      cell: EditableCell,
      meta: { type: 'number' }
    }),
    columnHelper.accessor('benchmark_duration', {
      id: 'benchmark_duration',
      header: '基准测试时长(s)',
      size: 140,
      cell: EditableCell,
      meta: { type: 'number' }
    }),
    columnHelper.accessor('input_tokens', {
      id: 'input_tokens',
      header: '输入Token数',
      size: 120,
      cell: EditableCell,
      meta: { type: 'number' }
    }),
    columnHelper.accessor('output_tokens', {
      id: 'output_tokens',
      header: '输出Token数',
      size: 120,
      cell: EditableCell,
      meta: { type: 'number' }
    }),
    columnHelper.accessor('request_throughput', {
      id: 'request_throughput',
      header: '请求吞吐量(req/s)',
      size: 150,
      cell: EditableCell,
      meta: { type: 'number' }
    }),
    columnHelper.accessor('output_token_throughput', {
      id: 'output_token_throughput',
      header: '输出Token吞吐量(tok/s)',
      size: 180,
      cell: ({ getValue, row, column, table }) => {
        const initialValue = getValue();
        const isEditing = editingCell?.rowId === row.original.id && editingCell?.columnId === column.id;
        
        if (isEditing) {
          return (
            <Input
              value={editingValue}
              onChange={(e) => setEditingValue(e.target.value)}
              onPressEnter={finishEditing}
              onBlur={finishEditing}
              onKeyDown={(e) => {
                if (e.key === 'Escape') {
                  cancelEditing();
                }
              }}
              autoFocus
              size="small"
              style={{ width: '100%' }}
            />
          );
        }

        return (
          <div
            className={styles.editableCell}
            onClick={() => startEditing(row.original.id, column.id, initialValue)}
            title="点击编辑"
            style={{ fontWeight: 'bold', color: '#1890ff' }}
          >
            {initialValue || '-'}
          </div>
        );
      },
      meta: { type: 'number', highlighted: true }
    }),
    columnHelper.accessor('total_token_throughput', {
      id: 'total_token_throughput',
      header: '总Token吞吐量(tok/s)',
      size: 170,
      cell: EditableCell,
      meta: { type: 'number' }
    }),
    columnHelper.accessor('avg_ttft', {
      id: 'avg_ttft',
      header: '平均TTFT(ms)',
      size: 120,
      cell: ({ getValue, row, column, table }) => {
        const initialValue = getValue();
        const isEditing = editingCell?.rowId === row.original.id && editingCell?.columnId === column.id;
        
        if (isEditing) {
          return (
            <Input
              value={editingValue}
              onChange={(e) => setEditingValue(e.target.value)}
              onPressEnter={finishEditing}
              onBlur={finishEditing}
              onKeyDown={(e) => {
                if (e.key === 'Escape') {
                  cancelEditing();
                }
              }}
              autoFocus
              size="small"
              style={{ width: '100%' }}
            />
          );
        }

        return (
          <div
            className={styles.editableCell}
            onClick={() => startEditing(row.original.id, column.id, initialValue)}
            title="点击编辑"
            style={{ fontWeight: 'bold', color: '#52c41a' }}
          >
            {initialValue || '-'}
          </div>
        );
      },
      meta: { type: 'number', highlighted: true }
    }),
    columnHelper.accessor('median_ttft', {
      id: 'median_ttft',
      header: '中位TTFT(ms)',
      size: 130,
      cell: EditableCell,
      meta: { type: 'number' }
    }),
    columnHelper.accessor('p99_ttft', {
      id: 'p99_ttft',
      header: 'P99 TTFT(ms)',
      size: 130,
      cell: EditableCell,
      meta: { type: 'number' }
    }),
    columnHelper.accessor('avg_tpot', {
      id: 'avg_tpot',
      header: '平均TPOT(ms)',
      size: 120,
      cell: EditableCell,
      meta: { type: 'number' }
    }),
    columnHelper.accessor('median_tpot', {
      id: 'median_tpot',
      header: '中位TPOT(ms)',
      size: 130,
      cell: EditableCell,
      meta: { type: 'number' }
    }),
    columnHelper.accessor('p99_tpot', {
      id: 'p99_tpot',
      header: 'P99 TPOT(ms)',
      size: 130,
      cell: EditableCell,
      meta: { type: 'number' }
    }),
    // 模型基础信息部分
    columnHelper.accessor('machine_model', {
      id: 'machine_model',
      header: '机型',
      size: 150,
      cell: EditableCell,
      meta: { type: 'text' }
    }),
    columnHelper.accessor('dataset', {
      id: 'dataset',
      header: '数据集',
      size: 150,
      cell: EditableCell,
      meta: { type: 'text' }
    }),
    columnHelper.accessor('data_type', {
      id: 'data_type',
      header: '数据类型',
      size: 120,
      cell: EditableCell,
      meta: { type: 'text' }
    }),
    columnHelper.accessor('framework', {
      id: 'framework',
      header: '框架',
      size: 120,
      cell: EditableCell,
      meta: { type: 'text' }
    }),
    columnHelper.accessor('framework_version', {
      id: 'framework_version',
      header: '框架版本',
      size: 120,
      cell: EditableCell,
      meta: { type: 'text' }
    }),
    // 系统信息部分
    columnHelper.accessor('topology', {
      id: 'topology',
      header: '拓扑',
      size: 150,
      cell: EditableCell,
      meta: { type: 'text' }
    }),
    columnHelper.accessor('cpu', {
      id: 'cpu',
      header: 'CPU',
      size: 200,
      cell: EditableCell,
      meta: { type: 'text' }
    }),
    columnHelper.accessor('memory', {
      id: 'memory',
      header: '内存',
      size: 120,
      cell: EditableCell,
      meta: { type: 'text' }
    }),
    columnHelper.accessor('fan_mode', {
      id: 'fan_mode',
      header: '风扇模式',
      size: 120,
      cell: EditableCell,
      meta: { type: 'text' }
    }),
    columnHelper.accessor('iommu_status', {
      id: 'iommu_status',
      header: 'IOMMU状态',
      size: 120,
      cell: EditableCell,
      meta: { type: 'text' }
    }),
    columnHelper.accessor('network_card', {
      id: 'network_card',
      header: '网卡',
      size: 150,
      cell: EditableCell,
      meta: { type: 'text' }
    }),
    columnHelper.accessor('cpu_mode', {
      id: 'cpu_mode',
      header: 'CPU模式',
      size: 120,
      cell: EditableCell,
      meta: { type: 'text' }
    }),
    columnHelper.accessor('os_kernel', {
      id: 'os_kernel',
      header: 'OS (内核)',
      size: 150,
      cell: EditableCell,
      meta: { type: 'text' }
    }),
    columnHelper.accessor('network_type', {
      id: 'network_type',
      header: '网络类型',
      size: 120,
      cell: EditableCell,
      meta: { type: 'text' }
    }),
    columnHelper.accessor('bios_version', {
      id: 'bios_version',
      header: 'BIOS版本',
      size: 120,
      cell: EditableCell,
      meta: { type: 'text' }
    }),
    columnHelper.accessor('gpu_firmware_version', {
      id: 'gpu_firmware_version',
      header: 'GPU固件版本',
      size: 140,
      cell: EditableCell,
      meta: { type: 'text' }
    }),
    columnHelper.accessor('gpu_driver_version', {
      id: 'gpu_driver_version',
      header: 'GPU驱动版本',
      size: 140,
      cell: EditableCell,
      meta: { type: 'text' }
    }),
    columnHelper.accessor('image_version_info', {
      id: 'image_version_info',
      header: '镜像版本信息',
      size: 150,
      cell: EditableCell,
      meta: { type: 'text' }
    }),
    columnHelper.accessor('remarks', {
      id: 'remarks',
      header: '备注',
      size: 200,
      cell: EditableCell,
      meta: { type: 'text' }
    }),
    columnHelper.display({
      id: 'actions',
      header: '操作',
      size: 120,
      cell: ({ row }) => (
        <Space size="small">
          <Tooltip title="删除记录">
            <Button
              type="link"
              size="small"
              danger
              icon={<DeleteOutlined />}
              onClick={() => deleteRecord(row.original)}
            />
          </Tooltip>
        </Space>
      )
    })
  ], [editingCell, editingValue, startEditing]);

  // 创建表格实例
  const table = useReactTable({
    data,
    columns,
    state: {
      globalFilter,
      sorting,
    },
    onGlobalFilterChange: setGlobalFilter,
    onSortingChange: setSorting,
    getCoreRowModel: getCoreRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    globalFilterFn: 'includesString',
  });

  // 添加空行
  const addEmptyRow = () => {
    const maxId = Math.max(
      ...data.map(row => parseInt(row.id)).filter(id => !isNaN(id)),
      0
    );
    const newId = maxId + 1;

    const newRecord = {
      id: newId,
      filename: '',
      success_requests: null,
      benchmark_duration: null,
      input_tokens: null,
      output_tokens: null,
      request_throughput: null,
      output_token_throughput: null,
      total_token_throughput: null,
      avg_ttft: null,
      median_ttft: null,
      p99_ttft: null,
      avg_tpot: null,
      median_tpot: null,
      p99_tpot: null,
      // 模型基础信息部分
      machine_model: '',
      dataset: '',
      data_type: '',
      framework: '',
      framework_version: '',
      // 系统信息部分
      topology: '',
      cpu: '',
      memory: '',
      fan_mode: '',
      iommu_status: '',
      network_card: '',
      cpu_mode: '',
      os_kernel: '',
      network_type: '',
      bios_version: '',
      gpu_firmware_version: '',
      gpu_driver_version: '',
      image_version_info: '',
      remarks: '',
      _modified: true,
      _isNew: true
    };

    setData([...data, newRecord]);
    setHasUnsavedChanges(true);
    message.success('已添加空行，点击单元格可编辑');
  };

  // 保存所有更改
  const saveAllChanges = async () => {
    const modifiedData = data.filter(item => item._modified);

    if (modifiedData.length === 0) {
      message.warning('没有修改的数据需要保存');
      return;
    }

    try {
      setLoading(true);

      for (const item of modifiedData) {
        // 处理空值，将空字符串和null转换为合适的值
        const saveData = {
          model_name: modelName,
          filename: item.filename || '',
          success_requests: item.success_requests === '' || item.success_requests === null ? null : item.success_requests,
          benchmark_duration: item.benchmark_duration === '' || item.benchmark_duration === null ? null : item.benchmark_duration,
          input_tokens: item.input_tokens === '' || item.input_tokens === null ? null : item.input_tokens,
          output_tokens: item.output_tokens === '' || item.output_tokens === null ? null : item.output_tokens,
          request_throughput: item.request_throughput === '' || item.request_throughput === null ? null : item.request_throughput,
          output_token_throughput: item.output_token_throughput === '' || item.output_token_throughput === null ? null : item.output_token_throughput,
          total_token_throughput: item.total_token_throughput === '' || item.total_token_throughput === null ? null : item.total_token_throughput,
          avg_ttft: item.avg_ttft === '' || item.avg_ttft === null ? null : item.avg_ttft,
          median_ttft: item.median_ttft === '' || item.median_ttft === null ? null : item.median_ttft,
          p99_ttft: item.p99_ttft === '' || item.p99_ttft === null ? null : item.p99_ttft,
          avg_tpot: item.avg_tpot === '' || item.avg_tpot === null ? null : item.avg_tpot,
          median_tpot: item.median_tpot === '' || item.median_tpot === null ? null : item.median_tpot,
          p99_tpot: item.p99_tpot === '' || item.p99_tpot === null ? null : item.p99_tpot,
          // 模型基础信息部分
          machine_model: item.machine_model || '',
          dataset: item.dataset || '',
          data_type: item.data_type || '',
          framework: item.framework || '',
          framework_version: item.framework_version || '',
          // 系统信息部分
          topology: item.topology || '',
          cpu: item.cpu || '',
          memory: item.memory || '',
          fan_mode: item.fan_mode || '',
          iommu_status: item.iommu_status || '',
          network_card: item.network_card || '',
          cpu_mode: item.cpu_mode || '',
          os_kernel: item.os_kernel || '',
          network_type: item.network_type || '',
          bios_version: item.bios_version || '',
          gpu_firmware_version: item.gpu_firmware_version || '',
          gpu_driver_version: item.gpu_driver_version || '',
          image_version_info: item.image_version_info || '',
          remarks: item.remarks || '',
        };

        if (item._isNew) {
          // 新增记录
          await http.post('/api/model-storage/performance-test-data/', saveData);
        } else {
          // 更新记录
          await http.put(`/api/model-storage/performance-test-data/${item.id}/`, saveData);
        }
      }

      message.success(`成功保存 ${modifiedData.length} 条记录`);
      setHasUnsavedChanges(false);

      // 保存平均值统计到后端
      try {
        await http.post('/api/model-storage/average-stats/', {
          model_name: modelName,
          avg_output_token_throughput: averageStats.avgOutputTokenThroughput,
          avg_ttft: averageStats.avgTTFT,
          data_count: data.length
        });
        console.log('平均值统计已保存');
      } catch (statsError) {
        console.warn('保存平均值统计失败:', statsError);
        // 不影响主要功能，只记录警告
      }

      // 重新获取数据以确保同步
      await fetchData();

    } catch (error) {
      console.error('保存失败:', error);
      message.error('保存失败，请检查数据格式');
    } finally {
      setLoading(false);
    }
  };

  // 导出Excel
  const exportToExcel = () => {
    if (!data || data.length === 0) {
      message.warning('暂无数据可导出');
      return;
    }

    // 字段名映射：英文字段名 -> 中文字段名
    const fieldMapping = {
      model_name: '模型名称',
      filename: '文件名',
      success_requests: '成功请求数',
      benchmark_duration: '基准测试时长(s)',
      input_tokens: '输入Token数',
      output_tokens: '输出Token数',
      request_throughput: '请求吞吐量(req/s)',
      output_token_throughput: '输出Token吞吐量(tok/s)',
      total_token_throughput: '总Token吞吐量(tok/s)',
      avg_ttft: '平均TTFT(ms)',
      median_ttft: '中位TTFT(ms)',
      p99_ttft: 'P99 TTFT(ms)',
      avg_tpot: '平均TPOT(ms)',
      median_tpot: '中位TPOT(ms)',
      p99_tpot: 'P99 TPOT(ms)',
      // 模型基础信息部分
      machine_model: '机型',
      dataset: '数据集',
      data_type: '数据类型',
      framework: '框架',
      framework_version: '框架版本',
      // 系统信息部分
      topology: '拓扑',
      cpu: 'CPU',
      memory: '内存',
      fan_mode: '风扇模式',
      iommu_status: 'IOMMU状态',
      network_card: '网卡',
      cpu_mode: 'CPU模式',
      os_kernel: 'OS (内核)',
      network_type: '网络类型',
      bios_version: 'BIOS版本',
      gpu_firmware_version: 'GPU固件版本',
      gpu_driver_version: 'GPU驱动版本',
      image_version_info: '镜像版本信息',
      remarks: '备注',
      created_at: '创建时间',
      updated_at: '更新时间'
    };

    // 准备导出数据，移除内部字段并转换字段名为中文
    const exportData = data.map(item => {
      const { id, _modified, _isNew, ...rest } = item;
      const chineseData = {};

      // 将英文字段名转换为中文字段名
      Object.keys(rest).forEach(key => {
        const chineseKey = fieldMapping[key] || key;
        let value = rest[key];

        // 处理空值：null、undefined、空字符串、数字0都设为空，Excel中显示为空白
        if (value === null || value === undefined || value === '' || value === 0) {
          chineseData[chineseKey] = '';  // 统一设为空字符串，Excel中显示为空白
        } else {
          chineseData[chineseKey] = value;
        }
      });

      return chineseData;
    });

    const ws = XLSX.utils.json_to_sheet(exportData);
    const wb = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(wb, ws, modelName || '模型数据');

    const fileName = `${modelName || '模型数据'}_${new Date().toISOString().split('T')[0]}.xlsx`;
    XLSX.writeFile(wb, fileName);
    message.success('导出成功');
  };

  // 导入Excel
  const importFromExcel = (file) => {
    // 检查文件大小（限制为10MB）
    if (file.size > 10 * 1024 * 1024) {
      message.error('文件大小不能超过10MB');
      return false;
    }

    // 显示加载状态
    const loadingMessage = message.loading('正在导入Excel文件...', 0);

    const reader = new FileReader();
    reader.onload = (e) => {
      try {
        const arrayBuffer = e.target.result;
        const uint8Array = new Uint8Array(arrayBuffer);
        const workbook = XLSX.read(uint8Array, { type: 'array' });

        if (!workbook.SheetNames || workbook.SheetNames.length === 0) {
          loadingMessage();
          message.error('Excel文件中没有工作表');
          return;
        }

        const sheetName = workbook.SheetNames[0];
        const worksheet = workbook.Sheets[sheetName];
        const jsonData = XLSX.utils.sheet_to_json(worksheet);

        if (jsonData.length === 0) {
          loadingMessage();
          message.warning('Excel文件中没有数据');
          return;
        }

        // 限制导入数量
        if (jsonData.length > 1000) {
          loadingMessage();
          message.error('一次最多只能导入1000条数据');
          return;
        }

        // 计算最大ID（优化性能）- 使用当前表格数据
        let maxId = 0;
        if (data.length > 0) {
          for (const row of data) {
            const id = parseInt(row.id);
            if (!isNaN(id) && id > maxId) {
              maxId = id;
            }
          }
        }

        // 使用 setTimeout 异步处理数据转换，避免阻塞UI
        setTimeout(() => {
          try {
            // 中文字段名到英文字段名的映射（用于导入）
            const chineseToEnglishMapping = {
              '模型名称': 'model_name',
              '文件名': 'filename',
              '成功请求数': 'success_requests',
              '基准测试时长(s)': 'benchmark_duration',
              '输入Token数': 'input_tokens',
              '输出Token数': 'output_tokens',
              '请求吞吐量(req/s)': 'request_throughput',
              '输出Token吞吐量(tok/s)': 'output_token_throughput',
              '总Token吞吐量(tok/s)': 'total_token_throughput',
              '平均TTFT(ms)': 'avg_ttft',
              '中位TTFT(ms)': 'median_ttft',
              'P99 TTFT(ms)': 'p99_ttft',
              '平均TPOT(ms)': 'avg_tpot',
              '中位TPOT(ms)': 'median_tpot',
              'P99 TPOT(ms)': 'p99_tpot',
              // 模型基础信息部分
              '机型': 'machine_model',
              '数据集': 'dataset',
              '数据类型': 'data_type',
              '框架': 'framework',
              '框架版本': 'framework_version',
              // 系统信息部分
              '拓扑': 'topology',
              'CPU': 'cpu',
              '内存': 'memory',
              '风扇模式': 'fan_mode',
              'IOMMU状态': 'iommu_status',
              '网卡': 'network_card',
              'CPU模式': 'cpu_mode',
              'OS (内核)': 'os_kernel',
              '网络类型': 'network_type',
              'BIOS版本': 'bios_version',
              'GPU固件版本': 'gpu_firmware_version',
              'GPU驱动版本': 'gpu_driver_version',
              '镜像版本信息': 'image_version_info',
              '备注': 'remarks',
              '创建时间': 'created_at',
              '更新时间': 'updated_at'
            };

            // 转换导入的数据格式并过滤空行
            const importedData = jsonData
              .map((item, index) => {
                // 创建一个标准化的数据对象
                const normalizedItem = {};

                // 处理每个字段，支持中文和英文字段名
                Object.keys(item).forEach(key => {
                  const englishKey = chineseToEnglishMapping[key] || key;
                  let value = item[key];

                  // 清理字符串值
                  if (typeof value === 'string') {
                    value = value.trim();
                    // 将空字符串转换为null（对于数值字段）
                    if (value === '' && ['success_requests', 'benchmark_duration', 'input_tokens', 'output_tokens',
                        'request_throughput', 'output_token_throughput', 'total_token_throughput',
                        'avg_ttft', 'median_ttft', 'p99_ttft', 'avg_tpot', 'median_tpot', 'p99_tpot'].includes(englishKey)) {
                      value = null;
                    }
                  }

                  normalizedItem[englishKey] = value;
                });

                return {
                  id: maxId + index + 1,
                  filename: normalizedItem.filename || '',
                  success_requests: normalizedItem.success_requests || null,
                  benchmark_duration: normalizedItem.benchmark_duration || null,
                  input_tokens: normalizedItem.input_tokens || null,
                  output_tokens: normalizedItem.output_tokens || null,
                  request_throughput: normalizedItem.request_throughput || null,
                  output_token_throughput: normalizedItem.output_token_throughput || null,
                  total_token_throughput: normalizedItem.total_token_throughput || null,
                  avg_ttft: normalizedItem.avg_ttft || null,
                  median_ttft: normalizedItem.median_ttft || null,
                  p99_ttft: normalizedItem.p99_ttft || null,
                  avg_tpot: normalizedItem.avg_tpot || null,
                  median_tpot: normalizedItem.median_tpot || null,
                  p99_tpot: normalizedItem.p99_tpot || null,
                  // 模型基础信息部分
                  machine_model: normalizedItem.machine_model || '',
                  dataset: normalizedItem.dataset || '',
                  data_type: normalizedItem.data_type || '',
                  framework: normalizedItem.framework || '',
                  framework_version: normalizedItem.framework_version || '',
                  // 系统信息部分
                  topology: normalizedItem.topology || '',
                  cpu: normalizedItem.cpu || '',
                  memory: normalizedItem.memory || '',
                  fan_mode: normalizedItem.fan_mode || '',
                  iommu_status: normalizedItem.iommu_status || '',
                  network_card: normalizedItem.network_card || '',
                  cpu_mode: normalizedItem.cpu_mode || '',
                  os_kernel: normalizedItem.os_kernel || '',
                  network_type: normalizedItem.network_type || '',
                  bios_version: normalizedItem.bios_version || '',
                  gpu_firmware_version: normalizedItem.gpu_firmware_version || '',
                  gpu_driver_version: normalizedItem.gpu_driver_version || '',
                  image_version_info: normalizedItem.image_version_info || '',
                  remarks: normalizedItem.remarks || '',
                  _modified: true,
                  _isNew: true
                };
              })
              .filter(item => {
                // 过滤空行：文件名不为空且成功请求数不为空/0
                const filename = (item.filename || '').trim();
                const successRequests = item.success_requests;

                return filename !== '' && successRequests !== null && successRequests !== 0;
              });

            setData(prevData => [...prevData, ...importedData]);
            setHasUnsavedChanges(true);

            // 关闭加载消息并显示成功消息
            loadingMessage();

            const originalCount = jsonData.length;
            const validCount = importedData.length;
            const skippedCount = originalCount - validCount;

            let successMessage = `成功导入 ${validCount} 条有效数据`;
            if (skippedCount > 0) {
              successMessage += `，跳过 ${skippedCount} 条空行`;
            }

            message.success(successMessage);
          } catch (error) {
            console.error('数据转换失败:', error);
            loadingMessage();
            message.error('数据转换失败');
          }
        }, 100);
      } catch (error) {
        console.error('导入失败:', error);
        // 关闭加载消息并显示错误消息
        loadingMessage();
        message.error('Excel文件格式错误，导入失败');
      }
    };

    reader.onerror = () => {
      loadingMessage();
      message.error('文件读取失败');
    };

    reader.readAsArrayBuffer(file);
    return false; // 阻止默认上传行为
  };

  // 删除记录
  const deleteRecord = (record) => {
    Modal.confirm({
      title: '确认删除',
      content: `确定要删除文件 "${record.filename}" 的记录吗？`,
      onOk: async () => {
        try {
          if (!record._isNew && record.id) {
            await http.delete(`/api/model-storage/performance-test-data/${record.id}/`);
          }
          setData(data.filter(item => item.id !== record.id));
          setHasUnsavedChanges(true);
          message.success('删除成功');
        } catch (error) {
          console.error('删除失败:', error);
          message.error('删除失败');
        }
      }
    });
  };

  return (
    <div className={styles.container}>
      <Card 
        title={`模型数据表格 - ${modelName}`}
        extra={
          <Space>
            <Search
              placeholder="搜索..."
              value={globalFilter}
              onChange={(e) => setGlobalFilter(e.target.value)}
              style={{ width: 200 }}
              allowClear
            />
            <Button
              icon={<PlusOutlined />}
              onClick={addEmptyRow}
            >
              添加行
            </Button>
            <Upload
              accept=".xlsx,.xls"
              beforeUpload={importFromExcel}
              showUploadList={false}
            >
              <Button icon={<UploadOutlined />}>
                导入Excel
              </Button>
            </Upload>
            <Button
              icon={<SaveOutlined />}
              type="primary"
              disabled={!hasUnsavedChanges}
              onClick={saveAllChanges}
              loading={loading}
            >
              保存更改 {hasUnsavedChanges && `(${data.filter(item => item._modified).length})`}
            </Button>
            <Button
              icon={<DownloadOutlined />}
              onClick={exportToExcel}
            >
              导出Excel
            </Button>
            <Button 
              icon={<ReloadOutlined />} 
              onClick={fetchData}
              loading={loading}
            >
              刷新
            </Button>
          </Space>
        }
      >
        <Spin spinning={loading}>
          {/* 平均值统计显示 */}
          <Card 
             size="small" 
             className={styles.statsCard}
             style={{ marginBottom: 16, backgroundColor: '#f6ffed', border: '1px solid #b7eb8f' }}
             title={<span style={{ color: '#389e0d', fontWeight: 'bold' }}>关键指标平均值</span>}
           >
            <Row gutter={24}>
              <Col span={12}>
                <Statistic
                  title="平均输出Token吞吐量"
                  value={averageStats.avgOutputTokenThroughput}
                  suffix="tokens/s"
                  precision={2}
                  valueStyle={{ color: '#1890ff', fontWeight: 'bold' }}
                />
              </Col>
              <Col span={12}>
                <Statistic
                  title="平均TTFT"
                  value={averageStats.avgTTFT}
                  suffix="ms"
                  precision={2}
                  valueStyle={{ color: '#52c41a', fontWeight: 'bold' }}
                />
              </Col>
            </Row>
          </Card>
          
          <div className={styles.tableWrapper}>
            <table className={styles.table}>
              <thead>
                {table.getHeaderGroups().map(headerGroup => (
                  <tr key={headerGroup.id}>
                    {headerGroup.headers.map(header => (
                      <th 
                        key={header.id}
                        style={{ width: header.getSize() }}
                        className={`${styles.tableHeader} ${
                          header.column.columnDef.meta?.highlighted ? styles.highlightedColumn : ''
                        }`}
                      >
                        <div
                          className={`${styles.headerContent} ${
                            header.column.getCanSort() ? styles.sortable : ''
                          }`}
                          onClick={header.column.getToggleSortingHandler()}
                        >
                          {flexRender(header.column.columnDef.header, header.getContext())}
                          {header.column.getIsSorted() && (
                            <span className={styles.sortIcon}>
                              {header.column.getIsSorted() === 'desc' ? '↓' : '↑'}
                            </span>
                          )}
                        </div>
                      </th>
                    ))}
                  </tr>
                ))}
              </thead>
              <tbody>
                {table.getRowModel().rows.map(row => (
                  <tr 
                    key={row.id} 
                    className={`${styles.tableRow} ${
                      row.original._modified ? styles.modifiedRow : ''
                    }`}
                  >
                    {row.getVisibleCells().map(cell => (
                      <td 
                        key={cell.id} 
                        className={`${styles.tableCell} ${
                          cell.column.columnDef.meta?.highlighted ? styles.highlightedColumn : ''
                        }`}
                      >
                        {flexRender(cell.column.columnDef.cell, cell.getContext())}
                      </td>
                    ))}
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </Spin>
      </Card>
    </div>
  );
}

export default ModelDataTableTanStack;
