#!/usr/bin/env python
import os
import sys
import django

# 设置Django环境
sys.path.append('spug_api')
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'spug.settings')
django.setup()

from apps.model_storage.models import PerformanceTestData, GPUDevice, TestTask

print("=== 修复GPU设备关联问题 ===")

# 1. 检查当前状态
print("\n1. 检查当前数据状态:")
total_perf_data = PerformanceTestData.objects.count()
null_gpu_device = PerformanceTestData.objects.filter(gpu_device__isnull=True).count()
print(f"PerformanceTestData总数: {total_perf_data}")
print(f"gpu_device_id为null的数量: {null_gpu_device}")

# 2. 检查TestTask数据
print("\n2. 检查TestTask数据:")
mr_v100_tasks = TestTask.objects.filter(gpu_model='MR-V100')
print(f"MR-V100相关的TestTask数量: {mr_v100_tasks.count()}")
for task in mr_v100_tasks:
    print(f"  任务ID: {task.id}, 模型: {task.model_name}, tokens/s: {task.tokens_per_second}, ttft_ms: {task.ttft_ms}")

# 3. 检查GPUDevice数据
print("\n3. 检查GPUDevice数据:")
mr_v100_gpu = GPUDevice.objects.filter(name='MR-V100').first()
if mr_v100_gpu:
    print(f"找到MR-V100 GPU: ID={mr_v100_gpu.id}, 厂商={mr_v100_gpu.vendor}")
    print(f"当前性能数据: tokens/s={mr_v100_gpu.tokens_per_second}, ttft_ms={mr_v100_gpu.ttft_ms}")
else:
    print("未找到MR-V100 GPU设备")

# 4. 修复PerformanceTestData的gpu_device关联
print("\n4. 修复PerformanceTestData的gpu_device关联:")
updated_count = 0
for perf_data in PerformanceTestData.objects.filter(gpu_device__isnull=True):
    machine_model = (perf_data.machine_model or '').strip()
    if machine_model:
        gpu_device = GPUDevice.objects.filter(name=machine_model).first()
        if gpu_device:
            perf_data.gpu_device = gpu_device
            perf_data.save(update_fields=['gpu_device'])
            updated_count += 1
            print(f"  更新记录ID {perf_data.id}: machine_model='{machine_model}' -> gpu_device_id={gpu_device.id}")

print(f"共更新了 {updated_count} 条PerformanceTestData记录")

# 5. 验证修复结果
print("\n5. 验证修复结果:")
if mr_v100_gpu:
    # 重新计算MR-V100的性能数据
    print("重新计算MR-V100性能数据...")
    
    # 从TestTask获取数据
    task_data = TestTask.objects.filter(gpu_model='MR-V100').values('tokens_per_second', 'ttft_ms')
    valid_tps = [float(x['tokens_per_second']) for x in task_data if x['tokens_per_second'] and float(x['tokens_per_second']) > 0]
    valid_ttft = [float(x['ttft_ms']) for x in task_data if x['ttft_ms'] and float(x['ttft_ms']) > 0]
    
    best_tps_from_tasks = max(valid_tps) if valid_tps else 0
    best_ttft_from_tasks = min(valid_ttft) if valid_ttft else 0
    
    print(f"从TestTask计算得到: best_tps={best_tps_from_tasks}, best_ttft={best_ttft_from_tasks}")
    
    # 从PerformanceTestData获取数据
    perf_data = PerformanceTestData.objects.filter(gpu_device=mr_v100_gpu)
    print(f"关联到MR-V100的PerformanceTestData数量: {perf_data.count()}")
    
    if perf_data.exists():
        from django.db.models import Max, Min
        best_tps_from_perf = perf_data.filter(output_token_throughput__gt=0).aggregate(m=Max('output_token_throughput'))['m']
        best_ttft_from_perf = perf_data.filter(avg_ttft__gt=0).aggregate(m=Min('avg_ttft'))['m']
        print(f"从PerformanceTestData计算得到: best_tps={best_tps_from_perf}, best_ttft={best_ttft_from_perf}")

print("\n=== 修复完成 ===")
