.container {
  min-height: 100vh;
  background: transparent;
  padding: 0;
}

.pageHeader {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 12px;
  margin: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);

  :global(.ant-page-header-heading-title) {
    color: #1890ff;
    font-weight: 600;
    font-size: 24px;
  }

  :global(.ant-page-header-heading-sub-title) {
    color: #666;
    font-size: 14px;
  }

  :global(.ant-breadcrumb) {
    margin-bottom: 8px;
  }

  :global(.ant-breadcrumb a) {
    color: #1890ff;
    text-decoration: none;
    transition: all 0.3s;

    &:hover {
      color: #40a9ff;
    }
  }
}

.content {
  padding: 0 16px 16px;
}

.filterCard {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 12px;
  margin-bottom: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);

  :global(.ant-card-body) {
    padding: 20px;
  }
}

.rankingCard {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  overflow: hidden;

  :global(.ant-card-head) {
    background: linear-gradient(135deg, #f6f9fc 0%, #e9f4ff 100%);
    border-bottom: 1px solid #e8f4fd;
  }

  :global(.ant-card-head-title) {
    padding: 16px 0;
  }

  :global(.ant-card-body) {
    padding: 0;
  }
}

.rankingTable {
  :global(.ant-table) {
    background: transparent;
  }

  :global(.ant-table-thead > tr > th) {
    background: #fafbfc;
    border-bottom: 2px solid #e8f4fd;
    font-weight: 600;
    color: #1890ff;
    text-align: center;
  }

  :global(.ant-table-tbody > tr) {
    transition: all 0.3s;

    &:hover {
      background: #f0f8ff;
      transform: translateY(-1px);
      box-shadow: 0 2px 8px rgba(24, 144, 255, 0.1);
    }
  }

  :global(.ant-table-tbody > tr > td) {
    border-bottom: 1px solid #f0f0f0;
    padding: 16px;
  }

  // 排名前三的特殊样式
  :global(.ant-table-tbody > tr:nth-child(1)) {
    background: linear-gradient(135deg, #fff9e6 0%, #fffbe6 100%);
    
    &:hover {
      background: linear-gradient(135deg, #fff7d6 0%, #fff9d6 100%);
    }
  }

  :global(.ant-table-tbody > tr:nth-child(2)) {
    background: linear-gradient(135deg, #f6f6f6 0%, #fafafa 100%);
    
    &:hover {
      background: linear-gradient(135deg, #f0f0f0 0%, #f5f5f5 100%);
    }
  }

  :global(.ant-table-tbody > tr:nth-child(3)) {
    background: linear-gradient(135deg, #fff4e6 0%, #fff7e6 100%);
    
    &:hover {
      background: linear-gradient(135deg, #ffedd6 0%, #fff0d6 100%);
    }
  }
}

.emptyCard {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  text-align: center;
  min-height: 300px;
  display: flex;
  align-items: center;
  justify-content: center;

  :global(.ant-empty) {
    margin: 0;
  }
}

// 响应式设计
@media (max-width: 768px) {
  .container {
    padding: 8px;
  }

  .pageHeader {
    margin: 8px;
    
    :global(.ant-page-header-heading-title) {
      font-size: 20px;
    }
  }

  .content {
    padding: 0 8px 8px;
  }

  .filterCard {
    :global(.ant-card-body) {
      padding: 16px;
    }
  }

  .rankingTable {
    :global(.ant-table-tbody > tr > td) {
      padding: 12px 8px;
    }
  }
}

// 动画效果
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.rankingCard {
  animation: fadeInUp 0.6s ease-out;
}

.filterCard {
  animation: fadeInUp 0.4s ease-out;
}

.pageHeader {
  animation: fadeInUp 0.3s ease-out;
}