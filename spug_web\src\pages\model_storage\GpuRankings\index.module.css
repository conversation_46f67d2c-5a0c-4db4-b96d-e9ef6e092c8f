/* GPU排行榜页面样式 */
.container {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 24px;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

/* 页面头部 */
.header {
  text-align: center;
  margin-bottom: 32px;
  color: white;
}

.header h1 {
  font-size: 2.5rem;
  font-weight: 700;
  margin: 0 0 8px 0;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.header p {
  font-size: 1.1rem;
  opacity: 0.9;
  margin: 0 0 16px 0;
}

.rankingDescription {
  background: rgba(255, 255, 255, 0.2);
  padding: 12px 20px;
  border-radius: 8px;
  font-size: 1rem;
  color: white;
  border-left: 4px solid #ffd700;
  backdrop-filter: blur(5px);
  margin-top: 16px;
}

/* 控制区域 */
.controls {
  display: flex;
  gap: 24px;
  margin-bottom: 32px;
  padding: 20px;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
  flex-wrap: wrap;
  align-items: center;
}

.controlGroup {
  display: flex;
  align-items: center;
  gap: 8px;
}

.controlGroup label {
  font-weight: 600;
  color: #333;
  white-space: nowrap;
}

/* 排行榜容器 */
.rankingsContainer {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 24px;
  max-width: 1400px;
  margin: 0 auto;
}

@media (max-width: 1200px) {
  .rankingsContainer {
    grid-template-columns: 1fr;
  }
}

/* 排行榜面板 */
.rankingPanel {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 16px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
  overflow: hidden;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.rankingPanel:hover {
  transform: translateY(-4px);
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
}

/* 面板头部 */
.panelHeader {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 20px 24px;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border-bottom: 1px solid #dee2e6;
}

.panelIcon {
  font-size: 24px;
  color: #495057;
}

.panelTitle {
  font-size: 1.25rem;
  font-weight: 600;
  color: #212529;
}

/* 指标切换标签 */
.metricTabs {
  display: flex;
  padding: 0 24px;
  background: #f8f9fa;
  border-bottom: 1px solid #dee2e6;
}

.metricTab {
  padding: 12px 20px;
  border: none;
  background: transparent;
  color: #6c757d;
  font-weight: 500;
  cursor: pointer;
  border-bottom: 2px solid transparent;
  transition: all 0.3s ease;
}

.metricTab.active {
  color: #495057;
  border-bottom-color: #007bff;
  background: rgba(0, 123, 255, 0.05);
}

/* 排行榜列表 */
.rankingList {
  padding: 16px;
  max-height: 600px;
  overflow-y: auto;
}

/* 排行榜项目 */
.rankingItem {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 16px;
  margin-bottom: 12px;
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  transition: all 0.3s ease;
  border-left: 4px solid #e9ecef;
}

.rankingItem:hover {
  transform: translateX(4px);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
}

.rankingItem:last-child {
  margin-bottom: 0;
}

/* 前三名特殊样式 */
.rankingItem.rank1 {
  border-left-color: #ffd700;
  background: linear-gradient(135deg, #fff9e6 0%, #fff 100%);
}

.rankingItem.rank2 {
  border-left-color: #c0c0c0;
  background: linear-gradient(135deg, #f8f9fa 0%, #fff 100%);
}

.rankingItem.rank3 {
  border-left-color: #cd7f32;
  background: linear-gradient(135deg, #fdf2e9 0%, #fff 100%);
}

/* 排名数字 */
.rankNumber {
  font-size: 1.5rem;
  font-weight: 700;
  color: #495057;
  min-width: 40px;
  text-align: center;
}

.rank1 .rankNumber {
  color: #ffd700;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.rank2 .rankNumber {
  color: #c0c0c0;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.rank3 .rankNumber {
  color: #cd7f32;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

/* GPU信息 */
.gpuInfo {
  flex: 1;
  min-width: 0;
}

.gpuName {
  font-size: 1.1rem;
  font-weight: 600;
  color: #212529;
  margin-bottom: 4px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.gpuVendor {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 4px;
}

.gpuVendor {
  font-size: 0.9rem;
  color: #6c757d;
  font-weight: 500;
}

.gpuSpecs {
  font-size: 0.8rem;
  color: #868e96;
  line-height: 1.4;
}

/* 性能值 */
.performanceValue {
  text-align: right;
  min-width: 120px;
}

.valuePrimary {
  font-size: 1.25rem;
  font-weight: 700;
  color: #007bff;
  margin-bottom: 2px;
  display: flex;
  align-items: baseline;
  justify-content: flex-end;
  gap: 4px;
}

.valueUnit {
  font-size: 0.8rem;
  color: #6c757d;
  font-weight: 500;
}

.valueSecondary {
  font-size: 0.8rem;
  color: #868e96;
}

/* 厂商分组 */
.vendorSection {
  margin-bottom: 32px;
}

.vendorSection:last-child {
  margin-bottom: 0;
}

.vendorHeader {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 16px 24px;
  background: linear-gradient(135deg, #e3f2fd 0%, #f8f9fa 100%);
  border-bottom: 1px solid #dee2e6;
  margin-bottom: 16px;
}

.vendorLogo {
  font-size: 24px;
}

.vendorName {
  font-size: 1.2rem;
  font-weight: 600;
  color: #212529;
}

.vendorStats {
  font-size: 0.9rem;
  color: #6c757d;
  margin-left: auto;
}

/* 国产标识 */
.domesticBadge {
  background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
  color: white;
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 0.7rem;
  font-weight: 600;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

/* 加载状态 */
.loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 80px 20px;
  color: white;
  text-align: center;
}

.loadingSpinner {
  width: 40px;
  height: 40px;
  border: 4px solid rgba(255, 255, 255, 0.3);
  border-top: 4px solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 16px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 空状态 */
.emptyState {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 80px 20px;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 16px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
  max-width: 600px;
  margin: 0 auto;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .container {
    padding: 16px;
  }
  
  .header h1 {
    font-size: 2rem;
  }
  
  .controls {
    flex-direction: column;
    align-items: stretch;
    gap: 16px;
  }
  
  .controlGroup {
    flex-direction: column;
    align-items: stretch;
    gap: 8px;
  }
  
  .rankingItem {
    flex-direction: column;
    align-items: stretch;
    gap: 12px;
  }
  
  .performanceValue {
    text-align: left;
  }
  
  .valuePrimary {
    justify-content: flex-start;
  }
}

@media (max-width: 480px) {
  .header h1 {
    font-size: 1.75rem;
  }
  
  .panelHeader {
    padding: 16px;
  }
  
  .rankingList {
    padding: 12px;
  }
  
  .rankingItem {
    padding: 12px;
  }
}