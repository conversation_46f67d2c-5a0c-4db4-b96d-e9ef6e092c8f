<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>GPU性能排行榜</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 40px;
            color: white;
        }

        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .header p {
            font-size: 1.2rem;
            opacity: 0.9;
        }

        .controls {
            display: flex;
            justify-content: center;
            gap: 20px;
            margin-bottom: 30px;
            flex-wrap: wrap;
        }

        .control-group {
            background: rgba(255,255,255,0.1);
            padding: 15px;
            border-radius: 10px;
            backdrop-filter: blur(10px);
        }

        .control-group label {
            color: white;
            font-weight: 500;
            margin-right: 10px;
        }

        .control-group select {
            padding: 8px 12px;
            border: none;
            border-radius: 6px;
            background: white;
            font-size: 14px;
        }

        .rankings-container {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            margin-bottom: 40px;
        }

        @media (max-width: 1200px) {
            .rankings-container {
                grid-template-columns: 1fr;
            }
        }

        .ranking-panel {
            background: rgba(255,255,255,0.95);
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            backdrop-filter: blur(10px);
        }

        .panel-header {
            display: flex;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 15px;
            border-bottom: 2px solid #f0f0f0;
        }

        .panel-icon {
            font-size: 1.5rem;
            margin-right: 10px;
            color: #667eea;
        }

        .panel-title {
            font-size: 1.4rem;
            font-weight: 600;
            color: #333;
        }

        .metric-tabs {
            display: flex;
            margin-bottom: 20px;
            background: #f5f5f5;
            border-radius: 8px;
            padding: 4px;
        }

        .metric-tab {
            flex: 1;
            padding: 10px;
            text-align: center;
            border-radius: 6px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-weight: 500;
        }

        .metric-tab.active {
            background: #667eea;
            color: white;
            box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);
        }

        .ranking-list {
            max-height: 600px;
            overflow-y: auto;
        }

        .ranking-item {
            display: flex;
            align-items: center;
            padding: 15px;
            margin-bottom: 10px;
            background: #f8f9fa;
            border-radius: 10px;
            transition: all 0.3s ease;
            border-left: 4px solid transparent;
        }

        .ranking-item:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }

        .ranking-item.rank-1 {
            background: linear-gradient(135deg, #ffd700 0%, #ffed4e 100%);
            border-left-color: #ffd700;
        }

        .ranking-item.rank-2 {
            background: linear-gradient(135deg, #c0c0c0 0%, #e8e8e8 100%);
            border-left-color: #c0c0c0;
        }

        .ranking-item.rank-3 {
            background: linear-gradient(135deg, #cd7f32 0%, #deb887 100%);
            border-left-color: #cd7f32;
        }

        .rank-number {
            font-size: 1.5rem;
            font-weight: bold;
            width: 50px;
            text-align: center;
            color: #667eea;
        }

        .rank-1 .rank-number { color: #b8860b; }
        .rank-2 .rank-number { color: #708090; }
        .rank-3 .rank-number { color: #8b4513; }

        .gpu-info {
            flex: 1;
            margin-left: 15px;
        }

        .gpu-name {
            font-size: 1.1rem;
            font-weight: 600;
            color: #333;
            margin-bottom: 5px;
        }

        .gpu-vendor {
            font-size: 0.9rem;
            color: #666;
            margin-bottom: 5px;
        }

        .gpu-specs {
            font-size: 0.8rem;
            color: #888;
        }

        .performance-value {
            text-align: right;
            min-width: 120px;
        }

        .value-primary {
            font-size: 1.3rem;
            font-weight: bold;
            color: #667eea;
        }

        .value-unit {
            font-size: 0.9rem;
            color: #666;
            margin-left: 5px;
        }

        .value-secondary {
            font-size: 0.8rem;
            color: #888;
            margin-top: 2px;
        }

        .vendor-section {
            margin-bottom: 30px;
        }

        .vendor-header {
            display: flex;
            align-items: center;
            margin-bottom: 15px;
            padding: 10px 15px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 8px;
        }

        .vendor-logo {
            width: 30px;
            height: 30px;
            margin-right: 10px;
            background: rgba(255,255,255,0.2);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .vendor-name {
            font-size: 1.2rem;
            font-weight: 600;
        }

        .vendor-stats {
            margin-left: auto;
            font-size: 0.9rem;
            opacity: 0.9;
        }

        .domestic-badge {
            background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
            color: white;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 0.7rem;
            font-weight: 500;
            margin-left: 10px;
        }

        .loading {
            text-align: center;
            padding: 40px;
            color: #666;
        }

        .loading-spinner {
            width: 40px;
            height: 40px;
            border: 4px solid #f3f3f3;
            border-top: 4px solid #667eea;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin: 0 auto 20px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .empty-state {
            text-align: center;
            padding: 60px 20px;
            color: #666;
        }

        .empty-icon {
            font-size: 3rem;
            color: #ddd;
            margin-bottom: 20px;
        }

        .stats-summary {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .stat-card {
            background: rgba(255,255,255,0.9);
            padding: 20px;
            border-radius: 10px;
            text-align: center;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }

        .stat-value {
            font-size: 2rem;
            font-weight: bold;
            color: #667eea;
            margin-bottom: 5px;
        }

        .stat-label {
            color: #666;
            font-size: 0.9rem;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🏆 GPU性能排行榜</h1>
            <p>基于真实测试数据的GPU性能对比分析</p>
        </div>

        <div class="stats-summary">
            <div class="stat-card">
                <div class="stat-value">15</div>
                <div class="stat-label">GPU型号总数</div>
            </div>
            <div class="stat-card">
                <div class="stat-value">8</div>
                <div class="stat-label">国产加速卡</div>
            </div>
            <div class="stat-card">
                <div class="stat-value">127</div>
                <div class="stat-label">测试任务总数</div>
            </div>
            <div class="stat-card">
                <div class="stat-value">76</div>
                <div class="stat-label">完整测试数据</div>
            </div>
        </div>

        <div class="controls">
            <div class="control-group">
                <label>排行类型：</label>
                <select id="rankingType">
                    <option value="token_throughput">输出Token吞吐量</option>
                    <option value="ttft">首字延时(TTFT)</option>
                </select>
            </div>
            <div class="control-group">
                <label>显示范围：</label>
                <select id="scope">
                    <option value="all">全部GPU</option>
                    <option value="domestic">国产加速卡</option>
                </select>
            </div>
            <div class="control-group">
                <label>分组方式：</label>
                <select id="groupBy">
                    <option value="overall">综合排行</option>
                    <option value="vendor">厂商内排行</option>
                </select>
            </div>
        </div>

        <div class="rankings-container">
            <!-- 输出Token吞吐量排行榜 -->
            <div class="ranking-panel">
                <div class="panel-header">
                    <div class="panel-icon">🚀</div>
                    <div class="panel-title">输出Token吞吐量排行</div>
                </div>
                
                <div class="metric-tabs">
                    <div class="metric-tab active">综合排行</div>
                    <div class="metric-tab">厂商内排行</div>
                </div>

                <div class="ranking-list">
                    <div class="ranking-item rank-1">
                        <div class="rank-number">1</div>
                        <div class="gpu-info">
                            <div class="gpu-name">K100-AI</div>
                            <div class="gpu-vendor">昆仑芯 <span class="domestic-badge">国产</span></div>
                            <div class="gpu-specs">基于13个测试任务的平均数据</div>
                        </div>
                        <div class="performance-value">
                            <div class="value-primary">2,847<span class="value-unit">tok/s</span></div>
                            <div class="value-secondary">平均值</div>
                        </div>
                    </div>

                    <div class="ranking-item rank-2">
                        <div class="rank-number">2</div>
                        <div class="gpu-info">
                            <div class="gpu-name">P800</div>
                            <div class="gpu-vendor">壁仞科技 <span class="domestic-badge">国产</span></div>
                            <div class="gpu-specs">基于6个测试任务的平均数据</div>
                        </div>
                        <div class="performance-value">
                            <div class="value-primary">2,156<span class="value-unit">tok/s</span></div>
                            <div class="value-secondary">平均值</div>
                        </div>
                    </div>

                    <div class="ranking-item rank-3">
                        <div class="rank-number">3</div>
                        <div class="gpu-info">
                            <div class="gpu-name">MXC500</div>
                            <div class="gpu-vendor">摩尔线程 <span class="domestic-badge">国产</span></div>
                            <div class="gpu-specs">基于5个测试任务的平均数据</div>
                        </div>
                        <div class="performance-value">
                            <div class="value-primary">1,923<span class="value-unit">tok/s</span></div>
                            <div class="value-secondary">平均值</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 首字延时排行榜 -->
            <div class="ranking-panel">
                <div class="panel-header">
                    <div class="panel-icon">⚡</div>
                    <div class="panel-title">首字延时(TTFT)排行</div>
                </div>
                
                <div class="metric-tabs">
                    <div class="metric-tab active">综合排行</div>
                    <div class="metric-tab">厂商内排行</div>
                </div>

                <div class="ranking-list">
                    <div class="ranking-item rank-1">
                        <div class="rank-number">1</div>
                        <div class="gpu-info">
                            <div class="gpu-name">BI-V150</div>
                            <div class="gpu-vendor">壁仞科技 <span class="domestic-badge">国产</span></div>
                            <div class="gpu-specs">基于5个测试任务的平均数据</div>
                        </div>
                        <div class="performance-value">
                            <div class="value-primary">45.2<span class="value-unit">ms</span></div>
                            <div class="value-secondary">平均TTFT</div>
                        </div>
                    </div>

                    <div class="ranking-item rank-2">
                        <div class="rank-number">2</div>
                        <div class="gpu-info">
                            <div class="gpu-name">K100-AI</div>
                            <div class="gpu-vendor">昆仑芯 <span class="domestic-badge">国产</span></div>
                            <div class="gpu-specs">基于13个测试任务的平均数据</div>
                        </div>
                        <div class="performance-value">
                            <div class="value-primary">52.8<span class="value-unit">ms</span></div>
                            <div class="value-secondary">平均TTFT</div>
                        </div>
                    </div>

                    <div class="ranking-item rank-3">
                        <div class="rank-number">3</div>
                        <div class="gpu-info">
                            <div class="gpu-name">P800-PCIe</div>
                            <div class="gpu-vendor">壁仞科技 <span class="domestic-badge">国产</span></div>
                            <div class="gpu-specs">基于1个测试任务的平均数据</div>
                        </div>
                        <div class="performance-value">
                            <div class="value-primary">67.1<span class="value-unit">ms</span></div>
                            <div class="value-secondary">平均TTFT</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 厂商分组展示 -->
        <div class="ranking-panel" style="margin-top: 30px;">
            <div class="panel-header">
                <div class="panel-icon">🏢</div>
                <div class="panel-title">厂商内排行</div>
            </div>

            <div class="vendor-section">
                <div class="vendor-header">
                    <div class="vendor-logo">🔥</div>
                    <div class="vendor-name">昆仑芯</div>
                    <div class="vendor-stats">1个型号 | 13个测试任务</div>
                </div>
                <div class="ranking-list">
                    <div class="ranking-item">
                        <div class="rank-number">1</div>
                        <div class="gpu-info">
                            <div class="gpu-name">K100-AI</div>
                            <div class="gpu-specs">输出吞吐量: 2,847 tok/s | TTFT: 52.8ms</div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="vendor-section">
                <div class="vendor-header">
                    <div class="vendor-logo">⚡</div>
                    <div class="vendor-name">壁仞科技</div>
                    <div class="vendor-stats">2个型号 | 11个测试任务</div>
                </div>
                <div class="ranking-list">
                    <div class="ranking-item">
                        <div class="rank-number">1</div>
                        <div class="gpu-info">
                            <div class="gpu-name">P800</div>
                            <div class="gpu-specs">输出吞吐量: 2,156 tok/s | TTFT: 78.5ms</div>
                        </div>
                    </div>
                    <div class="ranking-item">
                        <div class="rank-number">2</div>
                        <div class="gpu-info">
                            <div class="gpu-name">BI-V150</div>
                            <div class="gpu-specs">输出吞吐量: 1,834 tok/s | TTFT: 45.2ms</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 交互逻辑
        document.addEventListener('DOMContentLoaded', function() {
            const rankingTypeSelect = document.getElementById('rankingType');
            const scopeSelect = document.getElementById('scope');
            const groupBySelect = document.getElementById('groupBy');

            // 监听控制器变化
            [rankingTypeSelect, scopeSelect, groupBySelect].forEach(select => {
                select.addEventListener('change', updateRankings);
            });

            // 标签页切换
            document.querySelectorAll('.metric-tab').forEach(tab => {
                tab.addEventListener('click', function() {
                    const parent = this.parentElement;
                    parent.querySelectorAll('.metric-tab').forEach(t => t.classList.remove('active'));
                    this.classList.add('active');
                    updateRankings();
                });
            });

            function updateRankings() {
                // 这里会调用后端API获取最新的排行数据
                console.log('更新排行榜数据...');
                // 实际实现中会发送AJAX请求到后端API
            }
        });
    </script>
</body>
</html>