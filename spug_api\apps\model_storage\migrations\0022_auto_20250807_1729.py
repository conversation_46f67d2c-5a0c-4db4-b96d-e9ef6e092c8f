# Generated by Django 2.2.28 on 2025-08-07 17:29

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('model_storage', '0021_auto_20250807_1714'),
    ]

    operations = [
        migrations.AddField(
            model_name='testtask',
            name='tokens_per_second',
            field=models.FloatField(blank=True, help_text='输出吞吐量，单位：tokens/s', null=True, verbose_name='吞吐量(tokens/s)'),
        ),
        migrations.AddField(
            model_name='testtask',
            name='ttft_ms',
            field=models.FloatField(blank=True, help_text='首字符时间，单位：毫秒', null=True, verbose_name='首字符时间(ms)'),
        ),
    ]
