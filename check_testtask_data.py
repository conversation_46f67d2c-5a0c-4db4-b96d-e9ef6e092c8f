import os
import sys
import django

# 添加项目路径
sys.path.append('spug_api')
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'spug.settings')
django.setup()

from apps.model_storage.models import TestTask, GPUDevice

print("=== 检查TestTask数据 ===")
testtasks = TestTask.objects.all()
print(f"TestTask总数: {testtasks.count()}")

if testtasks.exists():
    print("\n前5条TestTask数据:")
    for task in testtasks[:5]:
        print(f"ID: {task.id}, GPU模型: {task.gpu_model}, 模型名称: {task.model_name}, tokens_per_second: {task.tokens_per_second}, ttft_ms: {task.ttft_ms}")
    
    print("\n所有GPU模型统计:")
    gpu_models = testtasks.values_list('gpu_model', flat=True).distinct()
    for gpu_model in gpu_models:
        count = testtasks.filter(gpu_model=gpu_model).count()
        print(f"GPU模型: '{gpu_model}' - 数量: {count}")
    
    print("\n有效数据统计 (tokens_per_second > 0 且 ttft_ms > 0):")
    valid_tasks = testtasks.filter(tokens_per_second__gt=0, ttft_ms__gt=0)
    print(f"有效TestTask数量: {valid_tasks.count()}")
    
    print("\n非空数据统计:")
    non_null_tokens = testtasks.filter(tokens_per_second__isnull=False).exclude(tokens_per_second=0)
    non_null_ttft = testtasks.filter(ttft_ms__isnull=False).exclude(ttft_ms=0)
    print(f"tokens_per_second非空且非0的数量: {non_null_tokens.count()}")
    print(f"ttft_ms非空且非0的数量: {non_null_ttft.count()}")
    
    if non_null_tokens.exists():
        print("\n有tokens_per_second数据的前5条:")
        for task in non_null_tokens[:5]:
            print(f"GPU: {task.gpu_model}, 模型: {task.model_name}, tokens_per_second: {task.tokens_per_second}, ttft_ms: {task.ttft_ms}")
    
    if valid_tasks.exists():
        print("\n有效数据的GPU模型:")
        valid_gpu_models = valid_tasks.values_list('gpu_model', flat=True).distinct()
        for gpu_model in valid_gpu_models:
            count = valid_tasks.filter(gpu_model=gpu_model).count()
            print(f"GPU模型: '{gpu_model}' - 有效数量: {count}")
else:
    print("没有找到TestTask数据")

print("\n=== 检查GPUDevice数据 ===")
gpu_devices = GPUDevice.objects.all()
print(f"GPUDevice总数: {gpu_devices.count()}")

if gpu_devices.exists():
    print("\n前5条GPUDevice数据:")
    for device in gpu_devices[:5]:
        print(f"ID: {device.id}, 名称: {device.name}, 厂商: {device.vendor}")
        
    print("\n所有GPUDevice名称:")
    for device in gpu_devices:
        print(f"GPU设备: {device.name} (厂商: {device.vendor})")
else:
    print("没有找到GPUDevice数据")