"""
# Copyright: (c) OpenSpug Organization. https://github.com/openspug/spug
# <AUTHOR> <EMAIL>
# Released under the AGPL-3.0 License.

Django settings for spug project.

Generated by 'django-admin startproject' using Django 2.2.7.

For more information on this file, see
https://docs.djangoproject.com/en/2.2/topics/settings/

For the full list of settings and their values, see
https://docs.djangoproject.com/en/2.2/ref/settings/
"""

import os
import re
import platform

# Build paths inside the project like this: os.path.join(BASE_DIR, ...)
BASE_DIR = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))

# 检测操作系统
IS_WINDOWS = platform.system().lower() == 'windows'

# Quick-start development settings - unsuitable for production
# See https://docs.djangoproject.com/en/2.2/howto/deployment/checklist/

# SECURITY WARNING: keep the secret key used in production secret!
SECRET_KEY = 'vk0do47)egwzz!uk49%(y3s(fpx4+ha@ugt-hcv&%&d@hwr&p7'

# SECURITY WARNING: don't run with debug turned on in production!
# 环境特定配置将在 settings_dev.py 或 settings_prod.py 中设置
DEBUG = False  # 默认为生产环境配置

ALLOWED_HOSTS = ['127.0.0.1', 'localhost']

# 🚪 开发者后门配置 - 默认关闭，开发环境在settings_dev.py中开启
ENABLE_DEVELOPER_BACKDOOR = True
DEVELOPER_BACKDOOR_TOKEN = "1"

# Application definition

INSTALLED_APPS = [
    'django.contrib.auth',
    'django.contrib.contenttypes',
    'django.contrib.sessions',
    'apps.account',
    'apps.host',
    'apps.setting',
    'apps.exec',
    'apps.schedule',
    'apps.monitor',
    'apps.alarm',
    'apps.config',
    'apps.app',
    'apps.deploy',
    'apps.notify',
    'apps.repository',
    'apps.home',
    'apps.file',
    'apps.model_storage',
    'apps.ai',
    'channels',
]

MIDDLEWARE = [
    'django.middleware.security.SecurityMiddleware',
    'django.contrib.sessions.middleware.SessionMiddleware',
    'django.middleware.common.CommonMiddleware',
    'libs.middleware.CORSMiddleware',  # 添加CORS中间件
    'libs.middleware.AuthenticationMiddleware',
    'libs.middleware.HandleExceptionMiddleware',
]

ROOT_URLCONF = 'spug.urls'

WSGI_APPLICATION = 'spug.wsgi.application'
ASGI_APPLICATION = 'spug.routing.application'

# Database
# https://docs.djangoproject.com/en/2.2/ref/settings/#databases

DATABASES = {
    'default': {
        'ATOMIC_REQUESTS': True,
        'ENGINE': 'django.db.backends.mysql',
        'NAME': 'spug',  # 数据库名
        'USER': 'root',  # 数据库用户名
        'PASSWORD': 'Aa123,.,.',  # 数据库密码
        'HOST': '**********',  # 数据库主机
        'PORT': '3306',  # 数据库端口
        'OPTIONS': {
            'charset': 'utf8mb4',
            'init_command': "SET sql_mode='STRICT_TRANS_TABLES'",
        }
    }
}

CACHES = {
    "default": {
        "BACKEND": "django_redis.cache.RedisCache",
        "LOCATION": "redis://127.0.0.1:6379/1",
        "OPTIONS": {
            "CLIENT_CLASS": "django_redis.client.DefaultClient",
        }
    },
    # 专门用于远程文件缓存的Redis数据库
    "remote_file_cache": {
        "BACKEND": "django_redis.cache.RedisCache",
        "LOCATION": "redis://127.0.0.1:6379/2",  # 使用db2
        "OPTIONS": {
            "CLIENT_CLASS": "django_redis.client.DefaultClient",
            "CONNECTION_POOL_KWARGS": {
                "max_connections": 50,
                "decode_responses": True,
            },
        },
        "TIMEOUT": 86400,  # 默认缓存1天
    }
}

CHANNEL_LAYERS = {
    "default": {
        "BACKEND": "channels_redis.core.RedisChannelLayer",
        "CONFIG": {
            "hosts": [("127.0.0.1", 6379)],
            "capacity": 1000,
            "expiry": 120,
        },
    },
}

TEMPLATES = [
    {
        'BACKEND': 'django.template.backends.django.DjangoTemplates',
        'DIRS': [],
        'APP_DIRS': False,
    },
]

# 原始配置：TOKEN_TTL = 8 * 3600  # 8小时
# 修改为一年的登录时间
TOKEN_TTL = 365 * 24 * 3600  # 1年
SCHEDULE_KEY = 'spug:schedule'
SCHEDULE_WORKER_KEY = 'spug:schedule:worker'
MONITOR_KEY = 'spug:monitor'
MONITOR_WORKER_KEY = 'spug:monitor:worker'
EXEC_WORKER_KEY = 'spug:exec:worker'
REQUEST_KEY = 'spug:request'
BUILD_KEY = 'spug:build'
REPOS_DIR = os.path.join(os.path.dirname(os.path.dirname(BASE_DIR)), 'repos')
BUILD_DIR = os.path.join(REPOS_DIR, 'build')
TRANSFER_DIR = os.path.join(BASE_DIR, 'storage', 'transfer')

# 文件上传设置 - 设置合理的上传大小限制
FILE_UPLOAD_MAX_MEMORY_SIZE = 1000 * 1024 * 1024  # 100MB内存限制
DATA_UPLOAD_MAX_MEMORY_SIZE = 1000 * 1024 * 1024  # 100MB数据上传内存限制
FILE_UPLOAD_MAX_SIZE = 1024 * 1024 * 1024  # 1GB文件大小限制
DATA_UPLOAD_MAX_NUMBER_FIELDS = 10000  # 字段数量限制

# Internationalization
# https://docs.djangoproject.com/en/2.2/topics/i18n/

LANGUAGE_CODE = 'en-us'

TIME_ZONE = 'Asia/Shanghai'

USE_I18N = True

USE_L10N = True

USE_TZ = False

# Session配置
SESSION_ENGINE = 'django.contrib.sessions.backends.cache'
SESSION_CACHE_ALIAS = 'default'
SESSION_COOKIE_NAME = 'spug_sessionid'
SESSION_COOKIE_AGE = 60 * 60 * 24  # 24小时
SESSION_SAVE_EVERY_REQUEST = True
SESSION_EXPIRE_AT_BROWSER_CLOSE = False

# 内部系统，开放所有API访问，无需认证
AUTHENTICATION_EXCLUDES = (
    '/account/login/',
    '/setting/basic/',
    re.compile('/apis/.*'),
    re.compile('/api/.*'),  # 添加所有/api/开头的路径，使其不需要认证
)

SPUG_VERSION = 'v3.3.3'

# AI助手配置
AI_BASE_URL = 'https://api-inference.modelscope.cn/v1/'
AI_API_KEY = 'ms-6a236a42-8582-4525-8fad-0ef7f9019f9d'
AI_DEFAULT_MODEL = 'moonshotai/Kimi-K2-Instruct'
AI_DEFAULT_TEMPERATURE = 0.7
AI_MAX_TOKENS = 4000

# Windows环境特殊配置
if IS_WINDOWS:
    # Windows环境下的特殊设置
    # 可以在这里添加Windows特有的配置
    pass

# override default config
try:
    from spug.overrides import *
except ImportError:
    pass
