"""
GPU性能数据自动更新信号处理器
当PerformanceTestData、TestTask或AverageStats数据更新时，自动触发相关GPU设备的性能数据重新计算
"""

from django.db.models.signals import post_save, post_delete
from django.dispatch import receiver
from django.core.cache import cache
import logging

logger = logging.getLogger(__name__)


def clear_gpu_performance_cache(gpu_name=None):
    """清除GPU性能数据缓存"""
    try:
        if gpu_name:
            # 清除特定GPU的缓存
            cache_keys = [
                f'gpu_performance_{gpu_name}',
                f'gpu_rankings_cache',
                f'gpu_statistics_cache',
            ]
            cache.delete_many(cache_keys)
            logger.info(f"已清除GPU {gpu_name} 的性能数据缓存")
        else:
            # 清除所有GPU相关缓存
            cache_keys = [
                'gpu_rankings_cache',
                'gpu_statistics_cache',
                'all_gpu_performance_cache',
            ]
            cache.delete_many(cache_keys)
            logger.info("已清除所有GPU性能数据缓存")
    except Exception as e:
        logger.error(f"清除GPU性能缓存失败: {e}")


def get_affected_gpu_names(instance, model_name):
    """获取受影响的GPU名称列表"""
    affected_gpus = set()
    
    try:
        if model_name == 'TestTask':
            # TestTask更新影响对应的GPU
            if hasattr(instance, 'gpu_model') and instance.gpu_model:
                affected_gpus.add(instance.gpu_model)
                
        elif model_name == 'PerformanceTestData':
            # PerformanceTestData更新影响关联的GPU
            if hasattr(instance, 'gpu_device') and instance.gpu_device:
                affected_gpus.add(instance.gpu_device.name)
            elif hasattr(instance, 'machine_model') and instance.machine_model:
                affected_gpus.add(instance.machine_model)
                
        elif model_name == 'AverageStats':
            # AverageStats更新需要找到使用该模型的所有GPU
            from .models import TestTask
            if hasattr(instance, 'model_name') and instance.model_name:
                related_tasks = TestTask.objects.filter(model_name=instance.model_name)
                for task in related_tasks:
                    if task.gpu_model:
                        affected_gpus.add(task.gpu_model)
                        
    except Exception as e:
        logger.error(f"获取受影响GPU名称失败: {e}")
        
    return list(affected_gpus)


@receiver(post_save, sender='model_storage.TestTask')
def update_gpu_performance_on_testtask_save(sender, instance, created, **kwargs):
    """TestTask保存时更新GPU性能数据"""
    try:
        affected_gpus = get_affected_gpu_names(instance, 'TestTask')
        
        for gpu_name in affected_gpus:
            clear_gpu_performance_cache(gpu_name)
            
        # 如果有性能数据更新，也清除排行榜缓存
        if hasattr(instance, 'tokens_per_second') or hasattr(instance, 'ttft_ms'):
            if instance.tokens_per_second is not None or instance.ttft_ms is not None:
                clear_gpu_performance_cache()  # 清除全局缓存
                
        logger.info(f"TestTask {instance.id} 更新，已触发GPU性能数据刷新: {affected_gpus}")
        
    except Exception as e:
        logger.error(f"TestTask保存信号处理失败: {e}")


@receiver(post_delete, sender='model_storage.TestTask')
def update_gpu_performance_on_testtask_delete(sender, instance, **kwargs):
    """TestTask删除时更新GPU性能数据"""
    try:
        affected_gpus = get_affected_gpu_names(instance, 'TestTask')
        
        for gpu_name in affected_gpus:
            clear_gpu_performance_cache(gpu_name)
            
        clear_gpu_performance_cache()  # 清除全局缓存
        logger.info(f"TestTask {instance.id} 删除，已触发GPU性能数据刷新: {affected_gpus}")
        
    except Exception as e:
        logger.error(f"TestTask删除信号处理失败: {e}")


@receiver(post_save, sender='model_storage.PerformanceTestData')
def update_gpu_performance_on_perfdata_save(sender, instance, created, **kwargs):
    """PerformanceTestData保存时更新GPU性能数据"""
    try:
        affected_gpus = get_affected_gpu_names(instance, 'PerformanceTestData')
        
        for gpu_name in affected_gpus:
            clear_gpu_performance_cache(gpu_name)
            
        # 性能测试数据更新总是影响排行榜
        clear_gpu_performance_cache()  # 清除全局缓存
        
        action = "创建" if created else "更新"
        logger.info(f"PerformanceTestData {instance.id} {action}，已触发GPU性能数据刷新: {affected_gpus}")
        
    except Exception as e:
        logger.error(f"PerformanceTestData保存信号处理失败: {e}")


@receiver(post_delete, sender='model_storage.PerformanceTestData')
def update_gpu_performance_on_perfdata_delete(sender, instance, **kwargs):
    """PerformanceTestData删除时更新GPU性能数据"""
    try:
        affected_gpus = get_affected_gpu_names(instance, 'PerformanceTestData')
        
        for gpu_name in affected_gpus:
            clear_gpu_performance_cache(gpu_name)
            
        clear_gpu_performance_cache()  # 清除全局缓存
        logger.info(f"PerformanceTestData {instance.id} 删除，已触发GPU性能数据刷新: {affected_gpus}")
        
    except Exception as e:
        logger.error(f"PerformanceTestData删除信号处理失败: {e}")


@receiver(post_save, sender='model_storage.AverageStats')
def update_gpu_performance_on_avgstats_save(sender, instance, created, **kwargs):
    """AverageStats保存时更新GPU性能数据"""
    try:
        affected_gpus = get_affected_gpu_names(instance, 'AverageStats')
        
        for gpu_name in affected_gpus:
            clear_gpu_performance_cache(gpu_name)
            
        # AverageStats更新可能影响多个GPU的排行
        clear_gpu_performance_cache()  # 清除全局缓存
        
        action = "创建" if created else "更新"
        logger.info(f"AverageStats {instance.id} {action}，已触发GPU性能数据刷新: {affected_gpus}")
        
    except Exception as e:
        logger.error(f"AverageStats保存信号处理失败: {e}")


@receiver(post_delete, sender='model_storage.AverageStats')
def update_gpu_performance_on_avgstats_delete(sender, instance, **kwargs):
    """AverageStats删除时更新GPU性能数据"""
    try:
        affected_gpus = get_affected_gpu_names(instance, 'AverageStats')
        
        for gpu_name in affected_gpus:
            clear_gpu_performance_cache(gpu_name)
            
        clear_gpu_performance_cache()  # 清除全局缓存
        logger.info(f"AverageStats {instance.id} 删除，已触发GPU性能数据刷新: {affected_gpus}")
        
    except Exception as e:
        logger.error(f"AverageStats删除信号处理失败: {e}")


def trigger_gpu_performance_refresh(gpu_names=None):
    """手动触发GPU性能数据刷新的工具函数"""
    try:
        if gpu_names:
            if isinstance(gpu_names, str):
                gpu_names = [gpu_names]
            for gpu_name in gpu_names:
                clear_gpu_performance_cache(gpu_name)
        else:
            clear_gpu_performance_cache()
            
        logger.info(f"手动触发GPU性能数据刷新完成: {gpu_names or '全部'}")
        return True
        
    except Exception as e:
        logger.error(f"手动触发GPU性能数据刷新失败: {e}")
        return False
