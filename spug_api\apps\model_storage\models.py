from django.db import models
from django.utils import timezone
from libs.mixins import ModelMixin


class ReleasePlan(models.Model, ModelMixin):
    """发布计划模型"""
    CARD_CHOICES = [
        ('P800', 'P800'),
        ('P800-PCIe', 'P800-PCIe'),
        ('RG800', 'RG800'),
    ]
    
    STATUS_CHOICES = [
        ('complete', '已完成'),
        ('partial', '部分缺失'),
        ('missing', '缺失'),
        ('inProgress', '进行中'),
        ('released', '已发布'),
        ('delayed', '延期'),
        ('preparing', '准备中')
    ]
    
    card_model = models.CharField('卡型号', max_length=64, default='')
    model_name = models.CharField('模型名称', max_length=128)
    release_date = models.DateField('计划发布时间')
    model_status = models.CharField('Model状态', max_length=32, choices=STATUS_CHOICES, default='preparing')
    vendor_status = models.CharField('Vendor状态', max_length=32, choices=STATUS_CHOICES, default='preparing')
    overall_status = models.CharField('总体状态', max_length=32, choices=STATUS_CHOICES, default='preparing')
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        db_table = 'model_storage_release_plans'
        verbose_name = '发布计划'
        verbose_name_plural = '发布计划'
        ordering = ('-created_at',)
    
    def __str__(self):
        return f"{self.card_model} - {self.model_name}"



class FileStatus(models.Model, ModelMixin):
    """文件状态模型"""
    STATUS_CHOICES = [
        ('synced', '已同步'),
        ('missing', '缺失'),
        ('outdated', '过期'),
    ]
    
    TYPE_CHOICES = [
        ('folder', '文件夹'),
        ('file', '文件'),
    ]
    
    file_path = models.CharField('文件路径', max_length=512)
    file_type = models.CharField('文件类型', max_length=32, choices=TYPE_CHOICES, default='file')
    status = models.CharField('状态', max_length=32, choices=STATUS_CHOICES, default='missing')
    size = models.BigIntegerField('文件大小', null=True, blank=True)
    md5_hash = models.CharField('MD5哈希', max_length=32, null=True, blank=True)
    last_modified = models.DateTimeField('最后修改时间', null=True, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        db_table = 'model_storage_file_status'
        verbose_name = '文件状态'
        verbose_name_plural = '文件状态'
        ordering = ('file_path',)
    
    def __str__(self):
        return self.file_path


class NewReleasePlan(models.Model, ModelMixin):
    """新版发布计划模型"""
    STATUS_CHOICES = [
        ('planning', '计划中'),
        ('running', '进行中'),
        ('completed', '已完成'),
        ('cancelled', '已取消'),
        ('paused', '已暂停'),
    ]
    
    RISK_LEVEL_CHOICES = [
        ('low', '低风险'),
        ('medium', '中风险'),
        ('high', '高风险'),
    ]
    
    name = models.CharField('计划名称', max_length=256)
    description = models.TextField('计划描述', blank=True, default='')
    start_date = models.DateField('开始时间')
    end_date = models.DateField('结束时间')
    status = models.CharField('状态', max_length=32, choices=STATUS_CHOICES, default='planning')
    risk_level = models.CharField('风险等级', max_length=32, choices=RISK_LEVEL_CHOICES, default='low')
    created_by = models.CharField('创建人', max_length=128)
    created_at = models.DateTimeField('创建时间', auto_now_add=True)
    updated_at = models.DateTimeField('更新时间', auto_now=True)
    
    class Meta:
        db_table = 'model_storage_new_release_plans'
        verbose_name = '新版发布计划'
        verbose_name_plural = '新版发布计划'
        ordering = ('-created_at',)
    
    def __str__(self):
        return self.name
    
    @property
    def status_display(self):
        return dict(self.STATUS_CHOICES).get(self.status, self.status)
    
    @property
    def risk_level_display(self):
        return dict(self.RISK_LEVEL_CHOICES).get(self.risk_level, self.risk_level)
    
    @property
    def task_count(self):
        return self.test_tasks.count()
    
    @property
    def completed_tasks(self):
        return self.test_tasks.filter(test_status='completed').count()


class TestTask(models.Model, ModelMixin):
    """测试任务模型"""
    MODEL_TYPE_CHOICES = [
        ('inference', '推理'),
        ('training', '训练'),
        ('fine_tuning', '微调'),
        ('pre-training', '预训练'),
        ('optimization', '优化'),
    ]
    
    # GPU型号选择现在从GPU管理中动态获取，不再使用硬编码选项
    # GPU_MODEL_CHOICES 保留用于向后兼容，但不再限制字段值
    
    PRIORITY_CHOICES = [
        ('p1', 'P1-高'),
        ('p2', 'P2-中'),
        ('p3', 'P3-低'),
    ]
    
    TEST_STATUS_CHOICES = [
        ('pending', '待开始'),
        ('in_progress', '进行中'),
        ('completed', '已完成'),
        ('cancelled', '已取消'),
        ('blocked', '阻塞中'),
        ('delayed', '已延期'),
    ]
    
    PROVIDER_STATUS_CHOICES = [
        ('waiting', '等待中'),
        ('provided', '已提供'),
        ('partial', '部分提供'),
        ('rejected', '已拒绝'),
    ]
    
    release_plan = models.ForeignKey(NewReleasePlan, on_delete=models.CASCADE, related_name='test_tasks', verbose_name='发布计划', null=True, blank=True)
    model_name = models.CharField('模型名称', max_length=256)
    tester = models.CharField('人员名称', max_length=128)
    model_type = models.CharField('模型类型', max_length=32, choices=MODEL_TYPE_CHOICES, default='inference', null=True, blank=True)
    gpu_model = models.CharField('GPU型号', max_length=128, null=True, blank=True)
    start_date = models.DateField('开始时间', null=True, blank=True)
    end_date = models.DateField('结束时间', null=True, blank=True)
    priority = models.CharField('优先级', max_length=32, choices=PRIORITY_CHOICES, default='p2', null=True, blank=True)
    test_status = models.CharField('测试状态', max_length=32, choices=TEST_STATUS_CHOICES, default='pending', null=True, blank=True)
    provider_status = models.CharField('模型提供状态', max_length=32, choices=PROVIDER_STATUS_CHOICES, default='waiting', null=True, blank=True)
    progress = models.IntegerField('进度百分比', default=0, null=True, blank=True)
    notes = models.TextField('备注', blank=True, default='')

    # 新增字段：资料输出
    document_output = models.BooleanField('资料输出', default=False, help_text='是否已输出测试资料文档')

    # 关联测试用例集
    test_case_set_id = models.IntegerField('关联测试用例集ID', null=True, blank=True, help_text='关联的测试用例集ID')

    # 测试步骤完成状态（任务级别的进度跟踪）
    step_completion_status = models.TextField('测试步骤完成状态JSON', default='{}', help_text='存储任务级别的测试步骤完成状态')
    
    # 模型详细信息
    model_version = models.CharField('模型版本', max_length=64, blank=True, default='')
    model_size = models.CharField('模型大小', max_length=64, blank=True, default='')
    gpu_requirements = models.CharField('GPU需求', max_length=128, blank=True, default='')
    memory_requirements = models.CharField('内存需求', max_length=64, blank=True, default='')
    test_dataset = models.CharField('测试数据集', max_length=256, blank=True, default='')
    
    # 性能指标
    accuracy_target = models.FloatField('目标准确率', null=True, blank=True)
    actual_accuracy = models.FloatField('实际准确率', null=True, blank=True)
    inference_speed = models.CharField('推理速度', max_length=64, blank=True, default='')
    tokens_per_second = models.FloatField('吞吐量(tokens/s)', null=True, blank=True, help_text='输出吞吐量，单位：tokens/s')
    ttft_ms = models.FloatField('首字符时间(ms)', null=True, blank=True, help_text='首字符时间，单位：毫秒')
    
    # 时间跟踪
    actual_start_date = models.DateField('实际开始时间', null=True, blank=True)
    actual_end_date = models.DateField('实际结束时间', null=True, blank=True)
    estimated_hours = models.IntegerField('预计工时', default=0)
    actual_hours = models.IntegerField('实际工时', default=0)
    
    # 甘特图显示颜色
    color = models.CharField('显示颜色', max_length=32, default='#1890ff')
    
    created_at = models.DateTimeField('创建时间', auto_now_add=True)
    updated_at = models.DateTimeField('更新时间', auto_now=True)
    
    class Meta:
        db_table = 'model_storage_test_tasks_new'
        verbose_name = '测试任务'
        verbose_name_plural = '测试任务'
        ordering = ('-created_at',)
    
    def __str__(self):
        return f"{self.model_name} - {self.tester}"

    def get_test_case_set_info(self):
        """获取关联的测试用例集信息"""
        if not self.test_case_set_id:
            return None

        try:
            # 导入TestCaseSet模型
            from apps.exec.models import TestCaseSet
            import json
            test_case_set = TestCaseSet.objects.get(id=self.test_case_set_id)

            # 解析测试用例JSON
            try:
                test_cases = json.loads(test_case_set.test_cases) if test_case_set.test_cases else []
            except (json.JSONDecodeError, TypeError):
                test_cases = []

            return {
                'id': test_case_set.id,
                'name': test_case_set.name,
                'description': test_case_set.description,
                'category': test_case_set.category,
                'test_cases_count': len(test_cases)
            }
        except TestCaseSet.DoesNotExist:
            return None
        except Exception as e:
            print(f"[WARNING] 获取测试用例集信息失败: {e}")
            return None
    
    def to_dict(self):
        """转换为字典格式"""
        import json

        # 获取关联的测试用例集信息
        test_case_set_info = self.get_test_case_set_info()

        # 解析测试步骤完成状态
        try:
            step_completion_status = json.loads(self.step_completion_status) if self.step_completion_status else {}
        except (json.JSONDecodeError, TypeError):
            step_completion_status = {}

        # 获取性能数据
        tokens_per_second = self.tokens_per_second
        ttft_ms = self.ttft_ms

        # 如果当前记录的性能数据为空，优先从AverageStats表获取平均值数据
        if tokens_per_second is None or ttft_ms is None:
            try:
                # 首先尝试从AverageStats表获取平均值统计
                avg_stats = AverageStats.objects.filter(
                    model_name=self.model_name
                ).first()

                if avg_stats:
                    if tokens_per_second is None:
                        tokens_per_second = avg_stats.avg_output_token_throughput
                    if ttft_ms is None:
                        ttft_ms = avg_stats.avg_ttft
                else:
                    # 如果AverageStats中没有数据，再从PerformanceTestData表获取
                    # 首先尝试精确匹配
                    latest_performance = PerformanceTestData.objects.filter(
                        model_name=self.model_name
                    ).order_by('-created_at').first()

                    # 如果精确匹配失败，尝试模糊匹配
                    if not latest_performance:
                        # 提取模型的基础名称进行匹配
                        model_base_name = self.model_name.lower().replace('-', '').replace('_', '')
                        for perf_data in PerformanceTestData.objects.all():
                            perf_model_name = perf_data.model_name.lower().replace('-', '').replace('_', '')
                            if model_base_name in perf_model_name or perf_model_name in model_base_name:
                                latest_performance = perf_data
                                break

                    if latest_performance:
                        if tokens_per_second is None:
                            tokens_per_second = latest_performance.output_token_throughput
                        if ttft_ms is None:
                            ttft_ms = latest_performance.ttft_ms
            except Exception as e:
                # 如果查询失败，保持原值
                pass

        return {
            'id': self.id,
            'model_name': self.model_name,
            'tester': self.tester,
            'model_type': self.model_type,
            'gpu_model': self.gpu_model,
            'start_date': self.start_date.strftime('%Y-%m-%d') if self.start_date else None,
            'end_date': self.end_date.strftime('%Y-%m-%d') if self.end_date else None,
            'priority': self.priority,
            'test_status': self.test_status,
            'provider_status': self.provider_status,
            'progress': self.progress,
            'notes': self.notes,
            'document_output': self.document_output,
            'test_case_set_id': self.test_case_set_id,
            'test_case_set_info': test_case_set_info,
            'step_completion_status': step_completion_status,
            'model_type_display': self.model_type_display,
            'gpu_model_display': self.gpu_model_display,
            'priority_display': self.priority_display,
            'test_status_display': self.test_status_display,
            'provider_status_display': self.provider_status_display,
            'document_output_display': '是' if self.document_output else '否',
            'tokens_per_second': tokens_per_second,
            'ttft_ms': ttft_ms,
            'created_at': self.created_at.strftime('%Y-%m-%d %H:%M:%S') if self.created_at else None,
            'updated_at': self.updated_at.strftime('%Y-%m-%d %H:%M:%S') if self.updated_at else None,
        }
    
    @property
    def model_type_display(self):
        return dict(self.MODEL_TYPE_CHOICES).get(self.model_type, self.model_type)
    
    @property
    def gpu_model_display(self):
        # GPU型号现在直接显示，不再使用choices映射
        return self.gpu_model or ''
    
    @property
    def priority_display(self):
        return dict(self.PRIORITY_CHOICES).get(self.priority, self.priority)
    
    @property
    def test_status_display(self):
        return dict(self.TEST_STATUS_CHOICES).get(self.test_status, self.test_status)
    
    @property
    def provider_status_display(self):
        return dict(self.PROVIDER_STATUS_CHOICES).get(self.provider_status, self.provider_status)
    
    @property
    def document_output_display(self):
        return '是' if self.document_output else '否'
    
    @property
    def release_plan_name(self):
        return self.release_plan.name if self.release_plan else ''


class RiskAlert(models.Model, ModelMixin):
    """风险预警模型"""
    RISK_TYPE_CHOICES = [
        ('resource', '资源风险'),
        ('schedule', '进度风险'),
        ('technical', '技术风险'),
        ('quality', '质量风险'),
        ('dependency', '依赖风险'),
        ('other', '其他风险'),
    ]
    
    SEVERITY_CHOICES = [
        ('low', '低'),
        ('medium', '中'),
        ('high', '高'),
        ('critical', '严重'),
    ]
    
    STATUS_CHOICES = [
        ('active', '活跃'),
        ('resolved', '已解决'),
        ('ignored', '已忽略'),
        ('monitoring', '监控中'),
    ]
    
    release_plan = models.ForeignKey(NewReleasePlan, on_delete=models.CASCADE, related_name='risk_alerts', verbose_name='发布计划')
    title = models.CharField('风险标题', max_length=256)
    description = models.TextField('风险描述')
    risk_type = models.CharField('风险类型', max_length=32, choices=RISK_TYPE_CHOICES)
    severity = models.CharField('严重程度', max_length=32, choices=SEVERITY_CHOICES)
    status = models.CharField('状态', max_length=32, choices=STATUS_CHOICES, default='active')
    affected_tasks = models.TextField('影响的任务ID列表', blank=True, default='[]', help_text='JSON格式的任务ID数组')
    mitigation_plan = models.TextField('缓解计划', blank=True, default='')
    reporter = models.CharField('报告人', max_length=128)
    assignee = models.CharField('负责人', max_length=128, blank=True, default='')
    due_date = models.DateField('截止日期', null=True, blank=True)
    resolved_at = models.DateTimeField('解决时间', null=True, blank=True)
    created_at = models.DateTimeField('创建时间', auto_now_add=True)
    updated_at = models.DateTimeField('更新时间', auto_now=True)
    
    class Meta:
        db_table = 'model_storage_risk_alerts_new'
        verbose_name = '风险预警'
        verbose_name_plural = '风险预警'
        ordering = ('-created_at',)
    
    def __str__(self):
        return self.title
    
    @property
    def risk_type_display(self):
        return dict(self.RISK_TYPE_CHOICES).get(self.risk_type, self.risk_type)
    
    @property
    def severity_display(self):
        return dict(self.SEVERITY_CHOICES).get(self.severity, self.severity)
    
    @property
    def status_display(self):
        return dict(self.STATUS_CHOICES).get(self.status, self.status)


class GPUDevice(models.Model, ModelMixin):
    """GPU 设备信息"""
    name = models.CharField('GPU名称', max_length=128, unique=True)
    vendor = models.CharField('厂商', max_length=128)
    description = models.TextField('描述', blank=True, default='')
    manual_models = models.TextField(
        '手动录入模型',
        blank=True,
        default='',
        help_text='用于记录历史或无法自动关联的模型，多个模型请用英文逗号分隔'
    )
    # 关键性能指标
    tokens_per_second = models.FloatField('吞吐量(tokens/s)', null=True, blank=True, help_text='输出吞吐量，单位：tokens/s')
    ttft_ms = models.FloatField('首字符时间(ms)', null=True, blank=True, help_text='首字符时间，单位：毫秒')
    created_at = models.DateTimeField('创建时间', auto_now_add=True)
    updated_at = models.DateTimeField('更新时间', auto_now=True)

    class Meta:
        db_table = 'model_storage_gpu_devices'
        verbose_name = 'GPU'
        verbose_name_plural = 'GPU管理'
        ordering = ('name',)

    def __str__(self):
        return f"{self.name} ({self.vendor})"

    @property
    def tested_models_auto(self):
        """返回在此GPU上测试过的模型列表（自动从测试任务获取）"""
        try:
            from .models import TestTask  # 避免循环引用
            models_qs = TestTask.objects.filter(gpu_model=self.name).values_list('model_name', flat=True).distinct()
            return list(models_qs)
        except Exception:
            return []

    def get_manual_models_list(self):
        if not self.manual_models:
            return []
        return [model.strip() for model in self.manual_models.split(',') if model.strip()]

    def to_dict(self):
        from django.db.models import Max, Min
        from django.db.models import Q

        auto_models = self.tested_models_auto
        manual_models_list = self.get_manual_models_list()
        all_models = sorted(list(set(auto_models) | set(manual_models_list)))

        # 实时计算：以该GPU的最佳模型数据作为性能指标
        try:
            best_tps = None
            best_ttft = None

            # 优先从 TestTask 聚合（兼容性：用 Python 端求 max/min，并做宽松类型转换）
            try:
                def _to_pos_float(v):
                    try:
                        f = float(v)
                        return f if f > 0 else None
                    except Exception:
                        return None
                task_qs = TestTask.objects.filter(gpu_model=self.name).values('tokens_per_second', 'ttft_ms')
                if task_qs:
                    tps_list = [v for v in (_to_pos_float(x['tokens_per_second']) for x in task_qs) if v is not None]
                    ttft_list = [v for v in (_to_pos_float(x['ttft_ms']) for x in task_qs) if v is not None]
                    best_tps = max(tps_list) if tps_list else best_tps
                    best_ttft = min(ttft_list) if ttft_list else best_ttft
            except Exception:
                pass

            # 回退到 PerformanceTestData（优先外键gpu_device，其次machine_model字符串）
            if best_tps in (None, 0) or best_ttft in (None, 0):
                try:
                    perf_qs = PerformanceTestData.objects.filter(Q(gpu_device=self) | Q(machine_model=self.name))
                    if perf_qs.exists():
                        if best_tps in (None, 0):
                            best_tps = perf_qs.filter(output_token_throughput__gt=0).aggregate(m=Max('output_token_throughput'))['m']
                        if best_ttft in (None, 0):
                            best_ttft = perf_qs.filter(avg_ttft__gt=0).aggregate(m=Min('avg_ttft'))['m']
                except Exception:
                    pass

            # 最后回退到 ModelPerformanceData.test_results（按 machine_model 字符串匹配）
            if best_tps in (None, 0) or best_ttft in (None, 0):
                try:
                    def _norm(s: str) -> str:
                        return ''.join(ch for ch in (s or '').lower() if ch.isalnum())
                    target = _norm(self.name)
                    from .models import ModelPerformanceData  # 本模块后定义，运行时可用
                    for mpd in ModelPerformanceData.objects.all().only('test_results'):
                        for row in mpd.get_test_results() or []:
                            if _norm(str(row.get('machine_model', ''))) == target:
                                # 计算最大吞吐与最小TTFT
                                tps = row.get('output_token_throughput')
                                ttft = row.get('avg_ttft')
                                if isinstance(tps, (int, float)) and tps > 0:
                                    best_tps = max(best_tps or 0, float(tps))
                                if isinstance(ttft, (int, float)) and ttft > 0:
                                    best_ttft = min(best_ttft or float(ttft), float(ttft)) if best_ttft else float(ttft)
                except Exception:
                    pass

            tokens_per_second = round(float(best_tps or 0), 2)
            ttft_ms = round(float(best_ttft or 0), 2)
        except Exception:
            # 任意异常时返回0，避免伪数据
            tokens_per_second = 0
            ttft_ms = 0

        return {
            'id': self.id,
            'name': self.name,
            'vendor': self.vendor,
            'description': self.description,
            'manual_models': self.manual_models,
            'tested_models_auto': auto_models,
            'tested_models_manual': manual_models_list,
            'tested_models': all_models,
            'tested_models_count': len(all_models),
            'tokens_per_second': tokens_per_second,
            'ttft_ms': ttft_ms,
            'created_at': self.created_at.strftime('%Y-%m-%d %H:%M:%S') if self.created_at else None,
            'updated_at': self.updated_at.strftime('%Y-%m-%d %H:%M:%S') if self.updated_at else None,
        }


class WordTemplate(models.Model, ModelMixin):
    """Word文档模板模型"""
    TEMPLATE_STATUS_CHOICES = [
        ('active', '启用'),
        ('inactive', '禁用'),
        ('draft', '草稿'),
    ]

    name = models.CharField('模板名称', max_length=128)
    description = models.TextField('模板描述', blank=True, default='')
    template_file = models.CharField('模板文件路径', max_length=512)
    original_filename = models.CharField('原始文件名', max_length=256, blank=True, default='')
    template_type = models.CharField('模板类型', max_length=64, default='test_guide')
    status = models.CharField('状态', max_length=32, choices=TEMPLATE_STATUS_CHOICES, default='active')

    # 模板变量配置
    variables_config = models.TextField('变量配置JSON', default='{}', help_text='存储模板中的变量定义和默认值')

    # 使用统计
    usage_count = models.IntegerField('使用次数', default=0)
    last_used_at = models.DateTimeField('最后使用时间', null=True, blank=True)

    # 创建者信息
    created_by = models.CharField('创建者', max_length=64, default='')
    created_at = models.DateTimeField('创建时间', auto_now_add=True)
    updated_at = models.DateTimeField('更新时间', auto_now=True)

    class Meta:
        db_table = 'model_storage_word_templates'
        verbose_name = 'Word模板'
        verbose_name_plural = 'Word模板'
        ordering = ('-created_at',)

    def __str__(self):
        return f"{self.name} ({self.template_type})"

    def to_dict(self):
        import json
        try:
            variables_config = json.loads(self.variables_config) if self.variables_config else {}
        except:
            variables_config = {}

        return {
            'id': self.id,
            'name': self.name,
            'description': self.description,
            'template_file': self.template_file,
            'original_filename': self.original_filename,
            'template_type': self.template_type,
            'status': self.status,
            'variables_config': variables_config,
            'usage_count': self.usage_count,
            'last_used_at': self.last_used_at.strftime('%Y-%m-%d %H:%M:%S') if self.last_used_at else None,
            'created_by': self.created_by,
            'created_at': self.created_at.strftime('%Y-%m-%d %H:%M:%S') if self.created_at else None,
            'updated_at': self.updated_at.strftime('%Y-%m-%d %H:%M:%S') if self.updated_at else None,
        }


class PerformanceTestData(models.Model, ModelMixin):
    """性能测试数据表"""
    model_name = models.CharField('模型名称', max_length=256, default='', blank=True)
    filename = models.CharField('文件名', max_length=256)
    success_requests = models.IntegerField('成功请求数', default=0)
    benchmark_duration = models.FloatField('基准时长(s)', default=0.0)
    input_tokens = models.IntegerField('输入Token总数', default=0)
    output_tokens = models.IntegerField('生成Token总数', default=0)
    request_throughput = models.FloatField('请求吞吐量(req/s)', default=0.0)
    output_token_throughput = models.FloatField('输出Token吞吐量(tok/s)', default=0.0)
    total_token_throughput = models.FloatField('总Token吞吐量(tok/s)', default=0.0)
    avg_ttft = models.FloatField('平均TTFT(ms)', default=0.0)
    median_ttft = models.FloatField('中位TTFT(ms)', default=0.0)
    p99_ttft = models.FloatField('P99 TTFT(ms)', default=0.0)
    avg_tpot = models.FloatField('平均TPOT(ms)', default=0.0)
    median_tpot = models.FloatField('中位TPOT(ms)', default=0.0)
    p99_tpot = models.FloatField('P99 TPOT(ms)', default=0.0)

    # 模型基础信息部分
    machine_model = models.CharField('机型', max_length=128, blank=True, default='')
    # 新增：标准化的GPU外键，避免仅靠字符串匹配
    gpu_device = models.ForeignKey('GPUDevice', on_delete=models.SET_NULL, null=True, blank=True,
                                   related_name='performance_test_data', verbose_name='关联GPU')
    dataset = models.CharField('数据集', max_length=256, blank=True, default='')
    data_type = models.CharField('数据类型', max_length=128, blank=True, default='')
    framework = models.CharField('框架', max_length=128, blank=True, default='')
    framework_version = models.CharField('框架版本', max_length=64, blank=True, default='')

    # 系统信息部分
    topology = models.CharField('拓扑', max_length=256, blank=True, default='')
    cpu = models.CharField('CPU', max_length=256, blank=True, default='')
    memory = models.CharField('内存', max_length=128, blank=True, default='')
    fan_mode = models.CharField('风扇模式', max_length=64, blank=True, default='')
    iommu_status = models.CharField('IOMMU状态', max_length=64, blank=True, default='')
    network_card = models.CharField('网卡', max_length=256, blank=True, default='')
    cpu_mode = models.CharField('CPU模式', max_length=128, blank=True, default='')
    os_kernel = models.CharField('OS (内核)', max_length=256, blank=True, default='')
    network_type = models.CharField('网络类型', max_length=128, blank=True, default='')
    bios_version = models.CharField('BIOS版本', max_length=128, blank=True, default='')
    gpu_firmware_version = models.CharField('GPU固件版本', max_length=128, blank=True, default='')
    gpu_driver_version = models.CharField('GPU驱动版本', max_length=128, blank=True, default='')
    image_version_info = models.TextField('镜像版本信息', blank=True, default='')
    remarks = models.TextField('备注', blank=True, default='')

    created_at = models.DateTimeField('创建时间', auto_now_add=True)
    updated_at = models.DateTimeField('更新时间', auto_now=True)

    class Meta:
        db_table = 'model_storage_performance_test_data'
        verbose_name = '性能测试数据'
        verbose_name_plural = '性能测试数据'
        ordering = ('-created_at',)

    def __str__(self):
        return f'{self.filename} - {self.success_requests}请求'

    def to_dict(self):
        """转换为字典格式，确保数据类型正确"""
        return {
            'id': self.id,
            'model_name': self.model_name or '',
            'filename': self.filename or '',
            'success_requests': int(self.success_requests) if self.success_requests is not None else 0,
            'benchmark_duration': float(self.benchmark_duration) if self.benchmark_duration is not None else 0.0,
            'input_tokens': int(self.input_tokens) if self.input_tokens is not None else 0,
            'output_tokens': int(self.output_tokens) if self.output_tokens is not None else 0,
            'request_throughput': float(self.request_throughput) if self.request_throughput is not None else 0.0,
            'output_token_throughput': float(self.output_token_throughput) if self.output_token_throughput is not None else 0.0,
            'total_token_throughput': float(self.total_token_throughput) if self.total_token_throughput is not None else 0.0,
            'avg_ttft': float(self.avg_ttft) if self.avg_ttft is not None else 0.0,
            'median_ttft': float(self.median_ttft) if self.median_ttft is not None else 0.0,
            'p99_ttft': float(self.p99_ttft) if self.p99_ttft is not None else 0.0,
            'avg_tpot': float(self.avg_tpot) if self.avg_tpot is not None else 0.0,
            'median_tpot': float(self.median_tpot) if self.median_tpot is not None else 0.0,
            'p99_tpot': float(self.p99_tpot) if self.p99_tpot is not None else 0.0,
            # 模型基础信息部分
            'machine_model': self.machine_model or '',
            'dataset': self.dataset or '',
            'data_type': self.data_type or '',
            'framework': self.framework or '',
            'framework_version': self.framework_version or '',
            # 系统信息部分
            'topology': self.topology or '',
            'cpu': self.cpu or '',
            'memory': self.memory or '',
            'fan_mode': self.fan_mode or '',
            'iommu_status': self.iommu_status or '',
            'network_card': self.network_card or '',
            'cpu_mode': self.cpu_mode or '',
            'os_kernel': self.os_kernel or '',
            'network_type': self.network_type or '',
            'bios_version': self.bios_version or '',
            'gpu_firmware_version': self.gpu_firmware_version or '',
            'gpu_driver_version': self.gpu_driver_version or '',
            'image_version_info': self.image_version_info or '',
            'remarks': self.remarks or '',
            'created_at': self.created_at.strftime('%Y-%m-%d %H:%M:%S') if self.created_at else None,
            'updated_at': self.updated_at.strftime('%Y-%m-%d %H:%M:%S') if self.updated_at else None,
            # 关联GPU（若有）
            'gpu_device_id': self.gpu_device.id if getattr(self, 'gpu_device', None) else None,
            'gpu_device_name': self.gpu_device.name if getattr(self, 'gpu_device', None) else None,
        }


class AverageStats(models.Model, ModelMixin):
    """平均值统计模型"""
    model_name = models.CharField('模型名称', max_length=255, unique=True)
    avg_output_token_throughput = models.FloatField('平均输出Token吞吐量', help_text='平均输出Token吞吐量，单位：tokens/s')
    avg_ttft = models.FloatField('平均TTFT', help_text='平均首字符时间，单位：毫秒')
    data_count = models.IntegerField('数据条数', help_text='用于计算平均值的数据条数')
    created_at = models.DateTimeField('创建时间', auto_now_add=True)
    updated_at = models.DateTimeField('更新时间', auto_now=True)

    class Meta:
        db_table = 'model_storage_average_stats'
        verbose_name = '平均值统计'
        verbose_name_plural = '平均值统计'
        ordering = ('-updated_at',)

    def __str__(self):
        return f'{self.model_name} - 平均吞吐量: {self.avg_output_token_throughput}, 平均TTFT: {self.avg_ttft}'

    def to_dict(self):
        """转换为字典格式"""
        return {
            'id': self.id,
            'model_name': self.model_name,
            'avg_output_token_throughput': self.avg_output_token_throughput,
            'avg_ttft': self.avg_ttft,
            'data_count': self.data_count,
            'created_at': self.created_at.strftime('%Y-%m-%d %H:%M:%S') if self.created_at else None,
            'updated_at': self.updated_at.strftime('%Y-%m-%d %H:%M:%S') if self.updated_at else None,
        }


class ModelPerformanceData(models.Model, ModelMixin):
    """模型性能数据表 - 一个模型一条记录，用JSON存储表格数据"""
    model_name = models.CharField('模型名称', max_length=255, unique=True)

    # 模型基础信息
    framework = models.CharField('框架', max_length=128, blank=True, default='')
    framework_version = models.CharField('框架版本', max_length=64, blank=True, default='')
    model_version = models.CharField('模型版本', max_length=64, blank=True, default='')

    # 核心：用JSON字段存储完整的表格数据（Django 2.2使用TextField存储JSON）
    test_results = models.TextField('测试结果数据', default='[]', help_text='存储完整的测试结果表格，每行一个测试文件的数据，JSON格式')

    # JSON数据结构示例：
    # [
    #   {
    #     "filename": "test1.log",
    #     "success_requests": 100,
    #     "benchmark_duration": 10.5,
    #     "input_tokens": 1000,
    #     "output_tokens": 500,
    #     "request_throughput": 9.52,
    #     "output_token_throughput": 47.6,
    #     "total_token_throughput": 142.8,
    #     "avg_ttft": 105.2,
    #     "median_ttft": 98.7,
    #     "p99_ttft": 234.5,
    #     "avg_tpot": 21.3,
    #     "median_tpot": 19.8,
    #     "p99_tpot": 45.6,
    #     "machine_model": "A100",
    #     "dataset": "test_dataset",
    #     "data_type": "fp16",
    #     "framework": "pytorch",
    #     "framework_version": "1.13.1",
    #     "topology": "单机单卡",
    #     "cpu": "Intel Xeon",
    #     "memory": "256GB",
    #     "fan_mode": "auto",
    #     "iommu_status": "enabled",
    #     "network_card": "Mellanox",
    #     "cpu_mode": "performance",
    #     "os_kernel": "Ubuntu 20.04",
    #     "network_type": "InfiniBand",
    #     "bios_version": "2.4.3",
    #     "gpu_firmware_version": "94.02.71.00.03",
    #     "gpu_driver_version": "520.61.05",
    #     "image_version_info": "pytorch:1.13.1-cuda11.6-cudnn8-runtime",
    #     "remarks": "测试备注"
    #   },
    #   {
    #     "filename": "test2.log",
    #     "success_requests": 200,
    #     ...
    #   }
    # ]

    # 统计信息（用于快速查询和显示）
    total_test_files = models.IntegerField('测试文件总数', default=0)
    last_test_date = models.DateTimeField('最后测试时间', null=True, blank=True)

    created_at = models.DateTimeField('创建时间', auto_now_add=True)
    updated_at = models.DateTimeField('更新时间', auto_now=True)

    class Meta:
        db_table = 'model_storage_model_performance_data'
        verbose_name = '模型性能数据'
        verbose_name_plural = '模型性能数据'
        ordering = ('-updated_at',)

    def __str__(self):
        return f'{self.model_name} - {self.total_test_files}个测试文件'

    def get_test_results(self):
        """获取测试结果（解析JSON）"""
        import json
        try:
            return json.loads(self.test_results) if self.test_results else []
        except (json.JSONDecodeError, TypeError):
            return []

    def set_test_results(self, results):
        """设置测试结果（序列化为JSON）"""
        import json
        self.test_results = json.dumps(results, ensure_ascii=False)

    def to_dict(self):
        """转换为字典格式"""
        return {
            'id': self.id,
            'model_name': self.model_name,
            'framework': self.framework,
            'framework_version': self.framework_version,
            'model_version': self.model_version,
            'test_results': self.get_test_results(),
            'total_test_files': self.total_test_files,
            'last_test_date': self.last_test_date.strftime('%Y-%m-%d %H:%M:%S') if self.last_test_date else None,
            'created_at': self.created_at.strftime('%Y-%m-%d %H:%M:%S') if self.created_at else None,
            'updated_at': self.updated_at.strftime('%Y-%m-%d %H:%M:%S') if self.updated_at else None,
        }

    def update_statistics(self):
        """更新统计信息"""
        results = self.get_test_results()
        self.total_test_files = len(results)
        self.last_test_date = timezone.now()
        self.save()

    def add_test_result(self, test_data):
        """添加测试结果"""
        results = self.get_test_results()

        # 检查是否已存在相同文件名的测试结果
        existing_index = None
        for i, result in enumerate(results):
            if result.get('filename') == test_data.get('filename'):
                existing_index = i
                break

        if existing_index is not None:
            # 覆盖现有结果
            results[existing_index] = test_data
        else:
            # 添加新结果
            results.append(test_data)

        self.set_test_results(results)
        self.update_statistics()

    def replace_all_results(self, test_results_list):
        """替换所有测试结果（用于导入覆盖）"""
        self.set_test_results(test_results_list)
        self.update_statistics()


class ModelData(models.Model, ModelMixin):
    """模型数据表"""
    FRAMEWORK_CHOICES = [
        ('pytorch', 'PyTorch'),
        ('tensorflow', 'TensorFlow'),
        ('onnx', 'ONNX'),
        ('huggingface', 'Hugging Face'),
        ('other', '其他'),
    ]

    STATUS_CHOICES = [
        ('development', '开发中'),
        ('testing', '测试中'),
        ('production', '生产环境'),
        ('deprecated', '已废弃'),
    ]

    model_name = models.CharField('模型名称', max_length=256)
    model_version = models.CharField('模型版本', max_length=64, default='v1.0')
    model_size = models.CharField('模型大小', max_length=64, blank=True, default='')
    framework = models.CharField('框架', max_length=32, choices=FRAMEWORK_CHOICES, default='pytorch')
    accuracy = models.FloatField('准确率', null=True, blank=True, help_text='准确率百分比')
    inference_speed = models.CharField('推理速度', max_length=128, blank=True, default='')
    gpu_memory = models.CharField('GPU内存需求', max_length=64, blank=True, default='')
    dataset = models.CharField('训练数据集', max_length=256, blank=True, default='')
    status = models.CharField('状态', max_length=32, choices=STATUS_CHOICES, default='development')

    # 扩展字段
    description = models.TextField('模型描述', blank=True, default='')
    parameters_count = models.BigIntegerField('参数量', null=True, blank=True)
    training_time = models.CharField('训练时长', max_length=64, blank=True, default='')
    model_file_path = models.CharField('模型文件路径', max_length=512, blank=True, default='')
    config_file_path = models.CharField('配置文件路径', max_length=512, blank=True, default='')

    # 性能指标
    latency = models.FloatField('延迟(ms)', null=True, blank=True)
    throughput = models.FloatField('吞吐量(req/s)', null=True, blank=True)
    memory_usage = models.CharField('内存使用量', max_length=64, blank=True, default='')

    # 元数据
    created_by = models.CharField('创建者', max_length=128, blank=True, default='')
    tags = models.TextField('标签', blank=True, default='', help_text='JSON格式存储标签列表')

    created_at = models.DateTimeField('创建时间', auto_now_add=True)
    updated_at = models.DateTimeField('更新时间', auto_now=True)

    class Meta:
        db_table = 'model_storage_model_data'
        verbose_name = '模型数据'
        verbose_name_plural = '模型数据'
        ordering = ('-created_at',)
        unique_together = ['model_name', 'model_version']

    def __str__(self):
        return f"{self.model_name} - {self.model_version}"

    def to_dict(self):
        import json
        try:
            tags = json.loads(self.tags) if self.tags else []
        except:
            tags = []

        return {
            'id': self.id,
            'model_name': self.model_name,
            'model_version': self.model_version,
            'model_size': self.model_size,
            'framework': self.framework,
            'framework_display': self.get_framework_display(),
            'accuracy': self.accuracy,
            'inference_speed': self.inference_speed,
            'gpu_memory': self.gpu_memory,
            'dataset': self.dataset,
            'status': self.status,
            'status_display': self.get_status_display(),
            'description': self.description,
            'parameters_count': self.parameters_count,
            'training_time': self.training_time,
            'model_file_path': self.model_file_path,
            'config_file_path': self.config_file_path,
            'latency': self.latency,
            'throughput': self.throughput,
            'memory_usage': self.memory_usage,
            'created_by': self.created_by,
            'tags': tags,
            'created_at': self.created_at.strftime('%Y-%m-%d %H:%M:%S') if self.created_at else None,
            'updated_at': self.updated_at.strftime('%Y-%m-%d %H:%M:%S') if self.updated_at else None,
        }


class DocumentInstance(models.Model, ModelMixin):
    """文档实例模型"""
    DOCUMENT_STATUS_CHOICES = [
        ('draft', '草稿'),
        ('generated', '已生成'),
        ('exported', '已导出'),
        ('archived', '已归档'),
    ]

    title = models.CharField('文档标题', max_length=256)
    template = models.ForeignKey(WordTemplate, on_delete=models.CASCADE, verbose_name='关联模板')

    # 变量值配置
    variables_values = models.TextField('变量值JSON', default='{}', help_text='存储用户输入的变量值')

    # 富文本内容
    rich_content = models.TextField('富文本内容', blank=True, default='', help_text='用户编辑的富文本内容')

    # 生成的文档信息
    generated_file_path = models.CharField('生成文件路径', max_length=512, blank=True, default='')
    file_size = models.BigIntegerField('文件大小(字节)', default=0)
    status = models.CharField('状态', max_length=32, choices=DOCUMENT_STATUS_CHOICES, default='draft')

    # 版本信息
    version = models.CharField('版本号', max_length=32, default='1.0')
    parent_document = models.ForeignKey('self', on_delete=models.SET_NULL, null=True, blank=True, verbose_name='父文档')

    # 创建者信息
    created_by = models.CharField('创建者', max_length=64, default='')
    created_at = models.DateTimeField('创建时间', auto_now_add=True)
    updated_at = models.DateTimeField('更新时间', auto_now=True)

    # 导出记录
    export_count = models.IntegerField('导出次数', default=0)
    last_exported_at = models.DateTimeField('最后导出时间', null=True, blank=True)

    class Meta:
        db_table = 'model_storage_document_instances'
        verbose_name = '文档实例'
        verbose_name_plural = '文档实例'
        ordering = ('-created_at',)

    def __str__(self):
        return f"{self.title} (v{self.version})"

    def to_dict(self):
        import json
        try:
            variables_values = json.loads(self.variables_values) if self.variables_values else {}
        except:
            variables_values = {}

        return {
            'id': self.id,
            'title': self.title,
            'template_id': self.template.id if self.template else None,
            'template_name': self.template.name if self.template else '',
            'variables_values': variables_values,
            'rich_content': self.rich_content,
            'generated_file_path': self.generated_file_path,
            'file_size': self.file_size,
            'status': self.status,
            'version': self.version,
            'parent_document_id': self.parent_document.id if self.parent_document else None,
            'created_by': self.created_by,
            'created_at': self.created_at.strftime('%Y-%m-%d %H:%M:%S') if self.created_at else None,
            'updated_at': self.updated_at.strftime('%Y-%m-%d %H:%M:%S') if self.updated_at else None,
            'export_count': self.export_count,
            'last_exported_at': self.last_exported_at.strftime('%Y-%m-%d %H:%M:%S') if self.last_exported_at else None,
        }


class TemplateVariable(models.Model, ModelMixin):
    """模板变量定义模型"""
    VARIABLE_TYPE_CHOICES = [
        ('text', '文本'),
        ('select', '选择'),
        ('multiselect', '多选'),
        ('date', '日期'),
        ('number', '数字'),
        ('textarea', '多行文本'),
        ('gpu_model', 'GPU型号'),
        ('vendor', '厂商'),
    ]

    template = models.ForeignKey(WordTemplate, on_delete=models.CASCADE, verbose_name='关联模板')
    variable_name = models.CharField('变量名', max_length=128)
    display_name = models.CharField('显示名称', max_length=128)
    variable_type = models.CharField('变量类型', max_length=32, choices=VARIABLE_TYPE_CHOICES, default='text')

    # 变量配置
    default_value = models.TextField('默认值', blank=True, default='')
    options = models.TextField('选项配置JSON', blank=True, default='[]', help_text='用于select类型的选项')
    is_required = models.BooleanField('是否必填', default=True)
    validation_rule = models.CharField('验证规则', max_length=256, blank=True, default='')

    # 排序和分组
    sort_order = models.IntegerField('排序', default=0)
    group_name = models.CharField('分组名称', max_length=64, blank=True, default='')

    # 帮助信息
    help_text = models.TextField('帮助文本', blank=True, default='')
    placeholder = models.CharField('占位符', max_length=128, blank=True, default='')

    created_at = models.DateTimeField('创建时间', auto_now_add=True)
    updated_at = models.DateTimeField('更新时间', auto_now=True)

    class Meta:
        db_table = 'model_storage_template_variables'
        verbose_name = '模板变量'
        verbose_name_plural = '模板变量'
        ordering = ('sort_order', 'id')
        unique_together = ('template', 'variable_name')

    def __str__(self):
        return f"{self.template.name} - {self.display_name}"

    def to_dict(self):
        import json
        try:
            options = json.loads(self.options) if self.options else []
        except:
            options = []

        return {
            'id': self.id,
            'template_id': self.template.id,
            'variable_name': self.variable_name,
            'display_name': self.display_name,
            'variable_type': self.variable_type,
            'default_value': self.default_value,
            'options': options,
            'is_required': self.is_required,
            'validation_rule': self.validation_rule,
            'sort_order': self.sort_order,
            'group_name': self.group_name,
            'help_text': self.help_text,
            'placeholder': self.placeholder,
            'created_at': self.created_at.strftime('%Y-%m-%d %H:%M:%S') if self.created_at else None,
            'updated_at': self.updated_at.strftime('%Y-%m-%d %H:%M:%S') if self.updated_at else None,
        }