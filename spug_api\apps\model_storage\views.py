import os
import time
import datetime
import subprocess
import psutil
import json
import logging
import os
import json
import urllib.parse
from pathlib import Path
from django.conf import settings
from django.http import JsonResponse, FileResponse, HttpResponse
from django.views import View
from django.utils.decorators import method_decorator
from django.views.decorators.csrf import csrf_exempt
from django.db.models import Q
from django.utils import timezone
from datetime import datetime, timedelta
from libs.decorators import auth
from libs.utils import json_response
# 移除rest_framework依赖
from .models import ReleasePlan, FileStatus, NewReleasePlan, TestTask, RiskAlert, GPUDevice, ModelData, PerformanceTestData, ModelPerformanceData, AverageStats, WordTemplate, TemplateVariable, DocumentInstance
from .exceptions import (
    ModelStorageException, NetworkException, DatabaseException,
    FileSystemException, TimeoutException, ValidationException,
    log_exception, handle_api_exception, exception_handler,
    network_retry, database_retry, safe_execute
)

# 自定义异常类
class SecurityError(Exception):
    """安全相关异常"""
    pass
from .file_utils import (
    safe_file_ops, network_io_tracker, FileOperationError
)
from .config import (
    network_config, database_config, monitoring_config,
    scan_config, security_config
)
import openpyxl
from django.db import transaction
import urllib.parse
from openpyxl.styles import Font, Alignment, PatternFill
from io import BytesIO

# 配置logger
logger = logging.getLogger('model_storage')

@method_decorator(csrf_exempt, name='dispatch')
class ServerMetricsView(View):
    """远程服务器指标API"""
    # 添加类级变量用于存储上次的网络IO数据
    _last_net_io = None
    _last_net_time = None

    @auth('model_storage.storage.view')
    def get(self, request):
        """获取远程服务器实时指标"""
        # 从请求参数或配置中获取目标服务器信息
        target_host = request.GET.get('host', '**********')

        try:
            # 尝试获取远程服务器指标
            remote_metrics = self.get_remote_metrics(target_host)
            if remote_metrics:
                return JsonResponse({'data': remote_metrics})
            else:
                # 如果远程获取失败，返回错误信息
                return JsonResponse({
                    'data': {
                        'cpu_usage': 0,
                        'memory_usage': 0,
                        'disk_usage': 0,
                        'network_upload': '0MB/s',
                        'network_download': '0MB/s',
                        'network_total': '0MB/s',
                        'timestamp': timezone.now().isoformat(),
                        'error': f'无法连接到远程服务器 {target_host}'
                    }
                })
        except Exception as e:
            log_exception(e, {'operation': 'get_remote_metrics', 'target_host': target_host})
            return handle_api_exception(e, "获取远程服务器指标失败")

    def get_remote_metrics(self, target_host):
        """获取远程服务器指标"""
        try:
            from apps.host.models import Host
            from libs.ssh import SSH

            # 查找对应的主机配置
            host_obj = Host.objects.filter(
                Q(hostname=target_host) | Q(name__icontains=target_host)
            ).first()

            if not host_obj:
                logger.warning(f"未找到主机配置: {target_host}")
                return None

            # 建立SSH连接
            ssh = host_obj.get_ssh()

            with ssh:
                # 获取CPU使用率
                cpu_usage = self.get_remote_cpu_usage(ssh)

                # 获取内存使用率
                memory_usage = self.get_remote_memory_usage(ssh)

                # 获取磁盘使用率
                disk_usage = self.get_remote_disk_usage(ssh)

                # 获取网络IO
                network_stats = self.get_remote_network_stats(ssh, target_host)

                metrics = {
                    'cpu_usage': cpu_usage,
                    'memory_usage': memory_usage,
                    'disk_usage': disk_usage,
                    'network_upload': f"{network_stats['upload']:.2f}MB/s",
                    'network_download': f"{network_stats['download']:.2f}MB/s",
                    'network_total': f"{(network_stats['upload'] + network_stats['download']):.2f}MB/s",
                    'timestamp': timezone.now().isoformat(),
                    'target_host': target_host
                }

                # 优化：不再保存到数据库，直接返回实时数据
                # 这样可以减少数据库存储压力，提高响应速度，避免SQLite锁定问题
                logger.debug(f"获取远程服务器 {target_host} 指标成功，返回实时数据")

                return metrics

        except Exception as e:
            logger.error(f"获取远程服务器 {target_host} 指标失败: {e}")
            return None

    def get_remote_cpu_usage(self, ssh):
        """获取远程服务器CPU使用率"""
        try:
            # 使用top命令获取CPU使用率
            exit_code, output = ssh.exec_command_raw("top -bn1 | grep 'Cpu(s)' | awk '{print $2}' | cut -d'%' -f1")
            if exit_code == 0 and output.strip():
                return round(float(output.strip()), 1)

            # 备用方法：使用vmstat
            exit_code, output = ssh.exec_command_raw("vmstat 1 2 | tail -1 | awk '{print 100-$15}'")
            if exit_code == 0 and output.strip():
                return round(float(output.strip()), 1)

        except Exception as e:
            logger.warning(f"获取远程CPU使用率失败: {e}")

        return 0.0

    def get_remote_memory_usage(self, ssh):
        """获取远程服务器内存使用率"""
        try:
            # 使用free命令获取内存信息
            exit_code, output = ssh.exec_command_raw("free -m")
            if exit_code == 0 and output.strip():
                for line in output.split('\n'):
                    if line.startswith('Mem:'):
                        parts = line.split()
                        if len(parts) >= 7:
                            total = float(parts[1])
                            available = float(parts[6])
                            usage_percent = round((total - available) / total * 100, 1)
                            logger.debug(f"远程内存信息 - 总计: {total}MB, 可用: {available}MB, 使用率: {usage_percent}%")
                            return usage_percent
        except Exception as e:
            logger.warning(f"获取远程内存使用率失败: {e}")

        return 0.0

    def get_remote_disk_usage(self, ssh):
        """获取远程服务器磁盘使用率"""
        try:
            # 使用df命令获取/HDD_Raid分区使用率
            exit_code, output = ssh.exec_command_raw("df -h /HDD_Raid | tail -1 | awk '{print $5}' | cut -d'%' -f1")
            if exit_code == 0 and output.strip():
                return round(float(output.strip()), 1)
                
            # 如果上面的命令失败，尝试获取所有分区信息并查找/HDD_Raid
            exit_code, output = ssh.exec_command_raw("df -h | grep '/HDD_Raid' | awk '{print $5}' | cut -d'%' -f1")
            if exit_code == 0 and output.strip():
                return round(float(output.strip()), 1)
        except Exception as e:
            logger.warning(f"获取远程磁盘使用率失败: {e}")
        return 0.0

    def get_remote_network_stats(self, ssh, target_host):
        """获取远程服务器网络统计"""
        network_stats = {'upload': 0.0, 'download': 0.0}

        try:
            # 获取当前网络IO统计
            exit_code, output = ssh.exec_command_raw("cat /proc/net/dev | grep -E 'eth0|ens|enp' | head -1")
            if exit_code == 0 and output.strip():
                parts = output.split()
                if len(parts) >= 10:
                    current_rx_bytes = int(parts[1])  # 接收字节数
                    current_tx_bytes = int(parts[9])  # 发送字节数
                    current_time = time.time()

                    # 从缓存中获取上次的数据
                    cache_key = f"network_io_{target_host.replace('.', '_')}"
                    try:
                        cache_file = safe_file_ops.get_safe_file_path(f'{cache_key}.json', 'netio')
                        last_data = safe_file_ops.safe_json_read(cache_file, default=None)

                        if last_data:
                            time_delta = current_time - last_data['time']
                            if time_delta > 0:
                                upload_speed = (current_tx_bytes - last_data['tx_bytes']) / time_delta / (1024 * 1024)
                                download_speed = (current_rx_bytes - last_data['rx_bytes']) / time_delta / (1024 * 1024)
                                network_stats['upload'] = round(max(0, upload_speed), 2)
                                network_stats['download'] = round(max(0, download_speed), 2)

                        # 保存当前数据
                        current_data = {
                            'rx_bytes': current_rx_bytes,
                            'tx_bytes': current_tx_bytes,
                            'time': current_time
                        }
                        safe_file_ops.safe_json_write(cache_file, current_data)

                    except Exception as e:
                        # 第一次运行时缓存文件不存在是正常的，降低日志级别
                        if "文件不存在" in str(e) or "No such file" in str(e):
                            logger.debug(f"网络IO缓存文件不存在（首次运行正常）: {e}")
                        else:
                            logger.warning(f"处理网络IO缓存失败: {e}")

        except Exception as e:
            logger.warning(f"获取远程网络统计失败: {e}")

        return network_stats

@method_decorator(csrf_exempt, name='dispatch')
class FileCompareView(View):
    @auth('model_storage.file_compare.view')
    def get(self, request):
        """获取文件对比数据（Git风格）"""
        try:
            # 模拟SVN文件状态检查
            file_compare_data = self.check_file_differences()
            return JsonResponse({'data': file_compare_data})
        except Exception as e:
            return JsonResponse({'data': [], 'error': str(e)})
    
    def check_file_differences(self):
        """检查本地与远程文件差异"""
        # 使用真实数据：本地tree扫描 + 远程HTTP获取
        base_path = "/HDD_Raid/SVN_MODEL_REPO"
        
        try:
            # 获取本地文件树
            local_files = self.scan_local_tree(base_path)
            # 获取远程文件列表
            remote_files = self.scan_remote_files()
            # 对比差异
            return self.compare_files(local_files, remote_files)
        except Exception as e:
            print(f"获取真实数据失败，使用模拟数据: {e}")
            return self.get_mock_data(base_path)
    
    def scan_local_tree(self, base_path, max_depth=3):
        """使用tree命令扫描本地目录（限制深度避免过慢）"""
        import subprocess
        import os

        # 安全检查：验证路径是否被允许
        if not security_config.is_path_allowed(base_path):
            logger.warning(f"路径不被允许访问: {base_path}")
            raise FileSystemException(f"路径不被允许访问: {base_path}", base_path, "scan")

        if not os.path.exists(base_path):
            logger.warning(f"本地路径不存在: {base_path}")
            return []
        
        try:
            # 使用tree命令，限制深度和文件类型
            cmd = [
                'tree', base_path,
                '-L', str(max_depth),  # 限制深度
                '-I', '*.log|*.tmp|*.cache|*~',  # 忽略临时文件
                '-F',  # 显示文件类型标识
                '--dirsfirst',  # 目录优先
                '-a',  # 显示隐藏文件
                '--charset=utf-8'  # 使用UTF-8编码
            ]
            
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=30)
            
            if result.returncode == 0:
                return self.parse_tree_output(result.stdout, base_path)
            else:
                print(f"tree命令执行失败: {result.stderr}")
                return self.scan_local_python(base_path)
                
        except subprocess.TimeoutExpired:
            print("tree命令超时，切换到Python扫描")
            return self.scan_local_python(base_path)
        except Exception as e:
            print(f"tree命令出错: {e}")
            return self.scan_local_python(base_path)
    
    def scan_local_python(self, base_path, max_files=1000):
        """Python方式扫描本地目录（作为fallback）"""
        import os
        files = []
        file_count = 0
        
        try:
            for root, dirs, filenames in os.walk(base_path):
                if file_count >= max_files:
                    break
                    
                # 跳过隐藏目录和临时目录
                dirs[:] = [d for d in dirs if not d.startswith('.') and 'cache' not in d.lower()]
                
                # 限制深度
                depth = root.replace(base_path, '').count(os.sep)
                if depth > 3:
                    continue
                
                # 添加目录
                rel_path = os.path.relpath(root, base_path)
                if rel_path != '.':
                    files.append({
                        'path': root,
                        'name': os.path.basename(root),
                        'type': 'folder',
                        'size': self.get_dir_size(root),
                        'lastModified': self.get_file_time(root)
                    })
                
                # 添加重要文件
                for filename in filenames:
                    if file_count >= max_files:
                        break
                    
                    # 只关注重要文件类型
                    if self.is_important_file(filename):
                        file_path = os.path.join(root, filename)
                        files.append({
                            'path': file_path,
                            'name': filename,
                            'type': 'file',
                            'size': self.format_file_size(os.path.getsize(file_path)),
                            'lastModified': self.get_file_time(file_path)
                        })
                        file_count += 1
                        
        except Exception as e:
            print(f"Python扫描出错: {e}")
            
        return files
    
    def parse_tree_output(self, tree_output, base_path):
        """解析tree命令输出"""
        files = []
        lines = tree_output.split('\n')
        
        for line in lines:
            if not line.strip() or '├──' not in line and '└──' not in line:
                continue
                
            # 提取文件名和类型
            clean_line = line.replace('├──', '').replace('└──', '').strip()
            if not clean_line:
                continue
                
            # 判断是文件还是目录
            is_dir = clean_line.endswith('/') or not '.' in clean_line
            name = clean_line.rstrip('/')
            
            # 构造完整路径（简化处理）
            full_path = f"{base_path}/{name}"
            
            files.append({
                'path': full_path,
                'name': name,
                'type': 'folder' if is_dir else 'file',
                'size': 'DIR' if is_dir else 'Unknown',
                'lastModified': 'Unknown'
            })
            
        return files
    
    def scan_remote_files(self):
        """扫描远程仓库文件列表"""
        import requests
        from urllib.parse import urljoin
        
        remote_url = network_config.get_remote_url()
        # 使用配置的认证信息
        auth = network_config.get_auth_credentials()
        
        def fetch_remote_files():
            import requests
            # 获取Model目录列表
            model_url = urljoin(remote_url, "Model/")
            timeout = network_config.get_timeout()
            response = requests.get(model_url, timeout=timeout, auth=auth)
            response.raise_for_status()
            return response

        try:
            response = network_retry.execute_with_retry(fetch_remote_files)

            # 解析HTML获取模型列表
            remote_models = self.parse_remote_html(response.text, urljoin(remote_url, "Model/"))

            # 获取每个模型的详细信息
            detailed_files = []
            for model in remote_models:
                if model['type'] == 'folder':
                    model_details = safe_execute(
                        self.get_model_details,
                        remote_url, model['name'], auth,
                        default_return=[],
                        context={'model_name': model['name'], 'operation': 'get_model_details'}
                    )
                    if model_details:
                        detailed_files.extend(model_details)

            return detailed_files

        except (NetworkException, TimeoutException) as e:
            logger.error(f"获取远程文件列表失败: {e.message}", extra=e.details)
            return []
        except Exception as e:
            log_exception(NetworkException(f"远程文件扫描异常: {str(e)}", remote_url))
            return []
    
    def parse_remote_html(self, html_content, base_url):
        """解析远程目录的HTML内容"""
        import re
        import html
        from urllib.parse import urljoin, unquote
        files = []

        try:
            # 使用多种正则表达式模式来匹配不同格式的HTML
            patterns = [
                # 标准的<a href="...">...</a>格式
                r'<a\s+href="([^"]+)"[^>]*>([^<]+)</a>',
                # 可能有其他属性的格式
                r'<a\s+[^>]*href="([^"]+)"[^>]*>([^<]+)</a>',
                # 单引号格式
                r"<a\s+href='([^']+)'[^>]*>([^<]+)</a>",
            ]

            all_matches = []
            for pattern in patterns:
                matches = re.findall(pattern, html_content, re.IGNORECASE | re.DOTALL)
                all_matches.extend(matches)

            # 去重
            seen = set()
            unique_matches = []
            for href, text in all_matches:
                # HTML解码，处理特殊字符
                href = html.unescape(href.strip())
                text = html.unescape(text.strip())
                
                # 处理URL编码
                href = unquote(href)
                
                key = (href, text)
                if key not in seen:
                    seen.add(key)
                    unique_matches.append(key)

            logger.info(f"找到 {len(unique_matches)} 个链接")

            for href, text in unique_matches:
                # 跳过父目录链接和绝对路径
                if (href.startswith('..') or href.startswith('/') or
                    href.startswith('http') or href.startswith('mailto:')):
                    continue

                # 跳过空链接
                if not href or not text:
                    continue

                # 跳过一些常见的非目录链接
                if text.lower() in ['parent directory', 'up', 'back', '..', 'name', 'last modified', 'size', 'description']:
                    continue

                # 跳过包含HTML标签的文本
                if '<' in text or '>' in text:
                    continue

                # 判断是否为目录（通常以/结尾）
                is_dir = href.endswith('/')
                name = text
                if is_dir:
                    name = name.rstrip('/')

                # 确保名称不为空
                if not name:
                    continue

                # 使用urljoin正确处理相对路径
                full_path = urljoin(base_url, href)
                
                files.append({
                    'path': full_path,
                    'name': name,
                    'type': 'folder' if is_dir else 'file',
                    'size': 'DIR' if is_dir else 'Unknown',
                    'lastModified': 'Unknown',
                    'source': 'remote'
                })

            return files

        except Exception as e:
            logger.error(f"HTML解析失败: {e}")
            return []
    
    def get_model_details(self, base_url, model_name, auth):
        """获取单个模型的详细文件信息"""
        import requests
        from urllib.parse import urljoin
        
        model_files = []
        try:
            model_url = urljoin(base_url, f"Model/{model_name}/")
            response = requests.get(model_url, timeout=10, auth=auth)
            response.raise_for_status()
            
            # 解析模型目录内容
            files_in_model = self.parse_remote_html(response.text, model_url)
            
            # 判断模型目录状态
            if len(files_in_model) == 0:
                # 空目录
                status = 'missing'
                diff_summary = '远程模型目录为空'
            elif any(f['name'] == 'md5sums.txt' for f in files_in_model):
                # 包含校验文件，认为是完整的
                status = 'synced'
                diff_summary = '远程模型文件完整'
            else:
                # 没有校验文件，可能不完整
                status = 'partial'
                diff_summary = '远程模型文件可能不完整（缺少md5sums.txt）'
            
            # 添加模型目录信息
            model_files.append({
                'path': model_url,
                'name': model_name,
                'type': 'folder',
                'size': f"{len(files_in_model)} items",
                'lastModified': 'Unknown',
                'status': status,
                'diff_summary': diff_summary,
                'source': 'remote',
                'file_count': len(files_in_model)
            })
            
        except Exception as e:
            print(f"获取模型 {model_name} 详情失败: {e}")
            # 添加错误信息
            model_files.append({
                'path': f"{base_url}Model/{model_name}/",
                'name': model_name,
                'type': 'folder',
                'size': 'Error',
                'lastModified': 'Unknown',
                'status': 'missing',
                'diff_summary': f'无法访问远程模型目录: {str(e)}',
                'source': 'remote',
                'file_count': 0
            })
            
        return model_files
    
    def compare_files(self, local_files, remote_files):
        """对比本地和远程文件差异"""
        # 创建文件路径映射
        local_map = {f['name']: f for f in local_files}
        remote_map = {f['name']: f for f in remote_files}
        
        compared_files = []
        
        # 检查本地文件状态
        for name, local_file in local_map.items():
            if name in remote_map:
                # 文件在两边都存在，检查是否有差异
                status = 'synced'  # 简化处理，实际需要比较修改时间或hash
                diff_summary = '文件已同步'
            else:
                # 本地有，远程没有
                status = 'added'
                diff_summary = '本地新增，远程尚未同步'
            
            compared_files.append({
                **local_file,
                'status': status,
                'diff_summary': diff_summary
            })
        
        # 检查远程独有的文件
        for name, remote_file in remote_map.items():
            if name not in local_map:
                compared_files.append({
                    **remote_file,
                    'status': 'missing',
                    'diff_summary': '远程有此文件，本地缺失'
                })
        
        return compared_files
    
    def is_important_file(self, filename):
        """判断是否为重要文件"""
        important_extensions = [
            '.tar.gz', '.zip', '.tar', '.7z',  # 压缩文件
            '.bin', '.safetensors', '.pt', '.pth',  # 模型文件
            '.txt', '.md', '.json', '.yaml', '.yml',  # 配置文件
            '.sh', '.bat', '.py', '.dockerfile'  # 脚本文件
        ]
        
        filename_lower = filename.lower()
        return any(filename_lower.endswith(ext) for ext in important_extensions) or \
               filename_lower in ['readme', 'license', 'changelog', 'md5sums.txt']
    
    def get_dir_size(self, path):
        """获取目录大小（简化实现）"""
        try:
            import os
            total = 0
            for dirpath, dirnames, filenames in os.walk(path):
                for filename in filenames:
                    try:
                        fp = os.path.join(dirpath, filename)
                        total += os.path.getsize(fp)
                    except:
                        continue
            return self.format_file_size(total)
        except:
            return 'Unknown'
    
    def format_file_size(self, size_bytes):
        """格式化文件大小"""
        if size_bytes == 0:
            return "0B"
        
        size_names = ["B", "KB", "MB", "GB", "TB"]
        import math
        i = int(math.floor(math.log(size_bytes, 1024)))
        p = math.pow(1024, i)
        s = round(size_bytes / p, 2)
        return f"{s}{size_names[i]}"
    
    def get_file_time(self, path):
        """获取文件修改时间"""
        try:
            import os
            import datetime
            timestamp = os.path.getmtime(path)
            return datetime.datetime.fromtimestamp(timestamp).strftime('%Y-%m-%d %H:%M:%S')
        except:
            return 'Unknown'
    
    def get_mock_data(self, base_path):
        """获取模拟数据（fallback）"""
        
        files = [
            {
                'path': f"{base_path}/Model/Baichuan2-13B/model.bin",
                'filename': 'model.bin',
                'status': 'synced',
                'type': 'file',
                'size': '2.5GB',
                'lastModified': '2024-01-15 10:30:00',
                'diff_summary': '文件已同步'
            },
            {
                'path': f"{base_path}/Model/Baichuan2-13B/md5sums.txt",
                'filename': 'md5sums.txt',
                'status': 'modified',
                'type': 'file',
                'size': '1.2KB',
                'lastModified': '2024-01-16 14:25:00',
                'diff_summary': '本地文件已修改，需要同步'
            },
            {
                'path': f"{base_path}/Model/DeepSeek-V3-0324-P800/tokenizer.tar.gz",
                'filename': 'tokenizer.tar.gz',
                'status': 'missing',
                'type': 'file',
                'size': '150MB',
                'lastModified': '2024-01-14 09:15:00',
                'diff_summary': '远程有此文件，本地缺失'
            },
            {
                'path': f"{base_path}/Model/Qwen2.5-7B",
                'filename': 'Qwen2.5-7B',
                'status': 'added',
                'type': 'folder',
                'size': '7.2GB',
                'lastModified': '2024-01-17 11:00:00',
                'diff_summary': '本地新增文件夹，远程尚未同步'
            },
            {
                'path': f"{base_path}/Vendor/Kunlunxin/P800/Bert-base/Inference/v1.0/doc",
                'filename': 'doc',
                'status': 'conflict',
                'type': 'folder',
                'size': '25MB',
                'lastModified': '2024-01-16 16:45:00',
                'diff_summary': '本地和远程都有修改，存在冲突'
            },
            {
                'path': f"{base_path}/Vendor/AMD/RX7900/YOLOv5/Training/v2.1",
                'filename': 'v2.1',
                'status': 'deleted',
                'type': 'folder',
                'size': '1.8GB',
                'lastModified': '2024-01-10 08:20:00',
                'diff_summary': '远程已删除，本地仍存在'
            },
            {
                'path': f"{base_path}/Model/Qwen3-4B/config.json",
                'filename': 'config.json',
                'status': 'synced',
                'type': 'file',
                'size': '2.1KB',
                'lastModified': '2024-01-15 12:00:00',
                'diff_summary': '文件已同步'
            },
            {
                'path': f"{base_path}/Vendor/Biren/BR100/ResNet50/Inference/v1.5/modelzoo.tar.gz",
                'filename': 'modelzoo.tar.gz',
                'status': 'modified',
                'type': 'file',
                'size': '890MB',
                'lastModified': '2024-01-16 13:30:00',
                'diff_summary': '文件已更新，建议同步'
            }
        ]
        
        return files

@method_decorator(csrf_exempt, name='dispatch')
class CheckDifferencesView(View):
    @auth('model_storage.file_compare.view')
    def get(self, request):
        """检查文件差异"""
        try:
            # 这里可以调用SVN或其他版本控制命令来检查差异
            differences = self.scan_differences()
            
            return JsonResponse({
                'data': {
                    'total_differences': differences['total'],
                    'modified': differences['modified'],
                    'missing': differences['missing'],
                    'conflicts': differences['conflicts'],
                    'scan_time': timezone.now().isoformat()
                }
            })
        except Exception as e:
            return JsonResponse({'data': {'error': str(e)}})
    
    def scan_differences(self):
        """扫描文件差异"""
        # 模拟差异扫描结果
        return {
            'total': 15,
            'modified': 8,
            'missing': 4,
            'conflicts': 2,
            'added': 1
        }

@method_decorator(csrf_exempt, name='dispatch')
class SyncSingleFileView(View):
    @auth('model_storage.file_compare.edit')
    def post(self, request):
        """同步单个文件"""
        try:
            data = json.loads(request.body)
            file_path = data.get('file_path')
            
            if not file_path:
                return JsonResponse({'error': '文件路径不能为空'}, status=400)
            
            # 模拟文件同步过程
            result = self.sync_file(file_path)
            
            return JsonResponse({'data': result})
        except Exception as e:
            return JsonResponse({'error': str(e)}, status=500)
    
    def sync_file(self, file_path):
        """执行文件同步"""
        # 这里应该调用实际的SVN或rsync命令
        # 模拟同步过程
        import time
        time.sleep(1)  # 模拟同步时间
        
        return {
            'file_path': file_path,
            'status': 'success',
            'message': '文件同步成功',
            'sync_time': timezone.now().isoformat()
        }

@method_decorator(csrf_exempt, name='dispatch')
class BatchSyncView(View):
    @auth('model_storage.file_compare.edit')
    def post(self, request):
        """批量同步文件"""
        try:
            data = json.loads(request.body)
            files = data.get('files', [])
            
            if not files:
                return JsonResponse({'error': '没有要同步的文件'}, status=400)
            
            results = []
            for file_path in files:
                try:
                    result = self.sync_file(file_path)
                    results.append(result)
                except Exception as e:
                    results.append({
                        'file_path': file_path,
                        'status': 'error',
                        'message': str(e)
                    })
            
            return JsonResponse({
                'data': {
                    'total_files': len(files),
                    'successful': len([r for r in results if r['status'] == 'success']),
                    'failed': len([r for r in results if r['status'] == 'error']),
                    'results': results,
                    'sync_time': timezone.now().isoformat()
                }
            })
        except Exception as e:
            return JsonResponse({'error': str(e)}, status=500)
    
    def sync_file(self, file_path):
        """执行单个文件同步"""
        # 模拟同步过程
        import time
        import random
        time.sleep(random.uniform(0.5, 2))  # 模拟同步时间
        
        # 90%的成功率
        if random.random() < 0.9:
            return {
                'file_path': file_path,
                'status': 'success',
                'message': '同步成功'
            }
        else:
            return {
                'file_path': file_path, 
                'status': 'error',
                'message': '同步失败：网络连接超时'
            }

@method_decorator(csrf_exempt, name='dispatch')
class FileTreeView(View):
    """文件树API"""
    @auth('model_storage.storage.view')
    def get(self, request):
        """获取文件树结构（保持兼容性）"""
        try:
            # 为了兼容性，保留原有的文件树API
            tree_data = [
                {
                    'name': 'Model',
                    'type': 'folder',
                    'status': 'partial',
                    'children': [
                        {
                            'name': 'Baichuan2-13B',
                            'type': 'folder', 
                            'status': 'synced',
                            'children': [
                                {'name': 'model.bin', 'type': 'file', 'status': 'synced', 'size': '2.5GB'},
                                {'name': 'md5sums.txt', 'type': 'file', 'status': 'modified', 'size': '1.2KB'}
                            ]
                        },
                        {
                            'name': 'DeepSeek-V3-0324-P800',
                            'type': 'folder',
                            'status': 'missing',
                            'children': []
                        }
                    ]
                },
                {
                    'name': 'Vendor',
                    'type': 'folder',
                    'status': 'partial',
                    'children': [
                        {
                            'name': 'Kunlunxin',
                            'type': 'folder',
                            'status': 'synced',
                            'children': [
                                {
                                    'name': 'P800',
                                    'type': 'folder',
                                    'status': 'synced',
                                    'children': [
                                        {'name': 'Bert-base', 'type': 'folder', 'status': 'synced'},
                                        {'name': 'ResNet50', 'type': 'folder', 'status': 'synced'},
                                        {'name': 'Yolov5', 'type': 'folder', 'status': 'missing'}
                                    ]
                                }
                            ]
                        }
                    ]
                }
            ]
            
            return JsonResponse({'data': tree_data})
        except Exception as e:
            return JsonResponse({'data': [], 'error': str(e)})

@method_decorator(csrf_exempt, name='dispatch')
class LazyLoadTreeView(View):
    """分层懒加载树结构"""

    @auth('model_storage.storage.view')
    def get(self, request):
        """获取指定路径的子节点（用于本地目录树懒加载）"""
        path = request.GET.get('path', '/HDD_Raid/SVN_MODEL_REPO')
        is_root = request.GET.get('root', 'false').lower() == 'true'

        try:
            if is_root:
                # 返回根目录结构
                children = self.get_local_root_structure()
                return JsonResponse({'data': {'children': children}})
            else:
                # 获取指定路径的子节点
                children = self.get_local_children(path)
                return JsonResponse({'data': children})

        except Exception as e:
            logger.error(f"本地目录懒加载失败: {e}")
            return JsonResponse({'data': {'children': [{
                'title': '📁 本地仓库 (加载失败)',
                'key': 'local_error',
                'icon': '❌',
                'children': [{
                    'title': f'❌ {str(e)}',
                    'key': 'local_error_msg',
                    'isLeaf': True
                }]
            }]}})

    def get_local_root_structure(self):
        """获取远程服务器上的本地仓库根目录结构"""
        base_path = '/HDD_Raid/SVN_MODEL_REPO'
        target_host = '**********'

        try:
            # 通过SSH连接到远程服务器获取目录结构
            children = self.get_remote_directory_via_ssh_simple(target_host, base_path)
            return children
        except Exception as e:
            logger.error(f"通过SSH获取远程目录失败: {e}")
            return [{
                'title': '📁 本地仓库 (连接失败)',
                'key': 'local_connection_error',
                'icon': '❌',
                'isLeaf': True,
                'data': {
                    'name': '本地仓库',
                    'path': base_path,
                    'type': 'error',
                    'status': 'connection_failed',
                    'error': str(e)
                }
            }]

    def get_remote_directory_via_ssh_simple(self, host, path):
        """通过SSH连接获取远程目录结构 - 支持深层级懒加载"""
        from apps.host.models import Host

        try:
            # 获取主机配置
            host_obj = Host.objects.filter(hostname=host).first()
            if not host_obj:
                raise Exception(f"未找到主机 {host} 的配置")

            # 使用spug的SSH类
            with host_obj.get_ssh() as ssh:
                # 执行ls命令获取目录列表，为路径加上引号以处理空格
                exit_code, output = ssh.exec_command_raw(f'ls -la "{path}"')

                if exit_code != 0:
                    raise Exception(f"SSH命令执行失败: {output}")

                # 解析ls输出
                children = []
                lines = output.strip().split('\n')

                for line in lines:
                    if not line.strip() or line.startswith('total'):
                        continue

                    parts = line.split()
                    if len(parts) < 9:
                        continue

                    permissions = parts[0]
                    size = parts[4] if len(parts) > 4 else '0'
                    name = ' '.join(parts[8:])  # 文件名可能包含空格

                    # 跳过 . 和 .. 目录
                    if name in ['.', '..']:
                        continue

                    is_dir = permissions.startswith('d')
                    item_path = f"{path}/{name}" if path != '/' else f"/{name}"

                    # 生成唯一的key，使用完整路径确保唯一性
                    safe_key = item_path.replace('/', '_').replace('-', '_').replace('.', '_')

                    if is_dir:
                        # 目录节点
                        children.append({
                            'title': f"📁 {name}",
                            'key': f"local_{safe_key}",
                            'icon': '📁',
                            'isLeaf': False,  # 目录总是可展开的
                            'data': {
                                'name': name,
                                'path': item_path,
                                'type': 'folder',
                                'status': 'synced',
                                'source': 'local'
                            }
                        })
                    else:
                        # 文件节点
                        file_icon = self.get_file_icon(name)
                        children.append({
                            'title': f"{file_icon} {name}",
                            'key': f"local_file_{safe_key}",
                            'icon': file_icon,
                            'isLeaf': True,  # 文件是叶子节点
                            'data': {
                                'name': name,
                                'path': item_path,
                                'type': 'file',
                                'status': 'synced',
                                'source': 'local',
                                'size': size
                            }
                        })

                return children

        except Exception as e:
            logger.error(f"SSH连接失败: {e}")
            raise

    def get_file_icon(self, filename):
        """根据文件名获取合适的图标"""
        filename_lower = filename.lower()

        if filename_lower.endswith(('.txt', '.md', '.doc', '.docx')):
            return '📄'
        elif filename_lower.endswith(('.tar.gz', '.zip', '.rar', '.7z')):
            return '📦'
        elif filename_lower.endswith(('.json', '.yaml', '.yml', '.xml')):
            return '⚙️'
        elif filename_lower.endswith(('.py', '.js', '.sh', '.bat')):
            return '📜'
        elif filename_lower.endswith(('.jpg', '.jpeg', '.png', '.gif', '.bmp')):
            return '🖼️'
        elif filename_lower.endswith(('.mp4', '.avi', '.mov', '.mkv')):
            return '🎬'
        elif filename_lower.endswith(('.bin', '.so', '.dll', '.exe')):
            return '⚙️'
        elif 'md5' in filename_lower:
            return '🔐'
        else:
            return '📄'

    def get_remote_directory_via_ssh(self, host, path):
        """通过SSH连接获取远程目录结构"""
        from apps.host.models import Host

        try:
            # 获取主机配置
            host_obj = Host.objects.filter(hostname=host).first()
            if not host_obj:
                raise Exception(f"未找到主机 {host} 的配置")

            # 使用spug的SSH类建立连接
            ssh = host_obj.get_ssh()

            # 执行ls命令获取目录列表
            exit_code, output = ssh.exec_command_raw(f'ls -la {path}')

            if exit_code != 0:
                logger.error(f"SSH命令执行失败，退出码: {exit_code}, 输出: {output}")
                raise Exception(f"SSH命令执行失败: {output}")

            # 解析ls输出
            children = []
            lines = output.strip().split('\n')

            for line in lines:
                if not line.strip() or line.startswith('total'):
                    continue

                parts = line.split()
                if len(parts) < 9:
                    continue

                permissions = parts[0]
                name = ' '.join(parts[8:])  # 文件名可能包含空格

                # 跳过 . 和 .. 目录
                if name in ['.', '..']:
                    continue

                is_dir = permissions.startswith('d')
                if is_dir:
                    item_path = f"{path}/{name}" if path != '/' else f"/{name}"

                    # 检查是否有子目录
                    has_children = self.check_remote_subdirectories_via_ssh(ssh, item_path)

                    children.append({
                        'title': f"📁 {name}",
                        'key': f"local_{name}",
                        'icon': '📁',
                        'isLeaf': not has_children,
                        'data': {
                            'name': name,
                            'path': item_path,
                            'type': 'folder',
                            'status': 'synced',
                            'source': 'local'
                        }
                    })

            return children

        except Exception as e:
            logger.error(f"SSH连接失败: {e}")
            raise

    def check_remote_subdirectories_via_ssh(self, ssh, path):
        """检查远程目录是否有子目录"""
        try:
            exit_code, output = ssh.exec_command_raw(f'find {path} -maxdepth 1 -type d | wc -l')
            if exit_code == 0:
                # 如果有子目录，count会大于1（因为包含自身）
                return int(output.strip()) > 1
            return False
        except:
            return False

    def get_local_children(self, parent_path):
        """获取远程服务器上指定目录的子节点"""
        target_host = '**********'

        try:
            # 通过SSH连接到远程服务器获取子目录
            children = self.get_remote_directory_via_ssh_simple(target_host, parent_path)
            return children
        except Exception as e:
            logger.error(f"通过SSH获取远程目录 {parent_path} 失败: {e}")
            return []

    def get_remote_directory_children_via_ssh(self, host, path):
        """通过SSH连接获取远程目录的子节点"""
        from apps.host.models import Host

        try:
            # 获取主机配置
            host_obj = Host.objects.filter(hostname=host).first()
            if not host_obj:
                raise Exception(f"未找到主机 {host} 的配置")

            # 使用spug的SSH类建立连接
            ssh = host_obj.get_ssh()

            # 执行ls命令获取目录列表
            exit_code, output = ssh.exec_command_raw(f'ls -la {path}')

            if exit_code != 0:
                logger.error(f"SSH命令执行失败，退出码: {exit_code}, 输出: {output}")
                raise Exception(f"SSH命令执行失败: {output}")

            # 解析ls输出
            children = []
            lines = output.strip().split('\n')

            for line in lines:
                if not line.strip() or line.startswith('total'):
                    continue

                parts = line.split()
                if len(parts) < 9:
                    continue

                permissions = parts[0]
                size = parts[4]
                name = ' '.join(parts[8:])  # 文件名可能包含空格

                # 跳过 . 和 .. 目录
                if name in ['.', '..']:
                    continue

                is_dir = permissions.startswith('d')
                item_path = f"{path}/{name}" if path != '/' else f"/{name}"

                # 生成安全的key
                safe_key = item_path.replace('/', '_').replace('\\', '_')

                if is_dir:
                    # 检查是否有子目录
                    has_children = self.check_remote_subdirectories_via_ssh(ssh, item_path)

                    children.append({
                        'title': f"📁 {name}",
                        'key': f"local_{safe_key}",
                        'icon': '📁',
                        'isLeaf': not has_children,
                        'data': {
                            'name': name,
                            'path': item_path,
                            'type': 'folder',
                            'status': 'synced',
                            'source': 'local'
                        }
                    })
                else:
                    # 文件节点
                    children.append({
                        'title': f"📄 {name}",
                        'key': f"local_file_{safe_key}",
                        'icon': '📄',
                        'isLeaf': True,
                        'data': {
                            'name': name,
                            'path': item_path,
                            'type': 'file',
                            'status': 'synced',
                            'source': 'local',
                            'size': size
                        }
                    })

            return children

        except Exception as e:
            logger.error(f"SSH连接失败: {e}")
            raise

    def get_children(self, parent_path, depth=1):
        """获取指定目录的直接子节点"""
        import os
        
        if not os.path.exists(parent_path):
            return []
        
        children = []
        
        try:
            # 只扫描一层子目录
            for item in os.listdir(parent_path):
                item_path = os.path.join(parent_path, item)
                
                if os.path.isdir(item_path):
                    # 检查是否有子目录（用于显示展开按钮）
                    has_children = self.has_subdirectories(item_path)
                    
                    children.append({
                        'key': item_path,
                        'title': item,
                        'type': 'folder',
                        'isLeaf': not has_children,
                        'size': self.get_quick_dir_size(item_path),
                        'lastModified': self.get_file_time(item_path),
                        'status': 'synced',  # 简化处理
                        'children': [] if not has_children else None  # None表示未加载
                    })
                elif self.is_important_file(item):
                    # 只添加重要文件
                    children.append({
                        'key': item_path,
                        'title': item,
                        'type': 'file',
                        'isLeaf': True,
                        'size': self.format_file_size(os.path.getsize(item_path)),
                        'lastModified': self.get_file_time(item_path),
                        'status': 'synced'
                    })
                    
            # 按类型和名称排序：目录在前，文件在后
            children.sort(key=lambda x: (x['type'] == 'file', x['title'].lower()))
            
        except Exception as e:
            print(f"扫描目录失败: {e}")
            
        return children
    
    def has_subdirectories(self, path):
        """快速检查目录是否有子目录"""
        try:
            for item in os.listdir(path):
                if os.path.isdir(os.path.join(path, item)):
                    return True
        except:
            pass
        return False
    
    def get_quick_dir_size(self, path):
        """快速获取目录大小（只计算直接文件）"""
        try:
            import os
            total = 0
            for item in os.listdir(path):
                item_path = os.path.join(path, item)
                if os.path.isfile(item_path):
                    try:
                        total += os.path.getsize(item_path)
                    except:
                        continue
            return self.format_file_size(total) if total > 0 else 'DIR'
        except:
            return 'DIR'
    
    def is_important_file(self, filename):
        """判断是否为重要文件"""
        important_extensions = [
            '.tar.gz', '.zip', '.tar', '.7z',  # 压缩文件
            '.bin', '.safetensors', '.pt', '.pth',  # 模型文件
            '.txt', '.md', '.json', '.yaml', '.yml',  # 配置文件
            '.sh', '.bat', '.py', '.dockerfile'  # 脚本文件
        ]
        
        filename_lower = filename.lower()
        return any(filename_lower.endswith(ext) for ext in important_extensions) or \
               filename_lower in ['readme', 'license', 'changelog', 'md5sums.txt']
    
    def format_file_size(self, size_bytes):
        """格式化文件大小"""
        if size_bytes == 0:
            return "0B"
        
        size_names = ["B", "KB", "MB", "GB", "TB"]
        import math
        i = int(math.floor(math.log(size_bytes, 1024)))
        p = math.pow(1024, i)
        s = round(size_bytes / p, 2)
        return f"{s}{size_names[i]}"
    
    def get_file_time(self, path):
        """获取文件修改时间"""
        try:
            import os
            import datetime
            timestamp = os.path.getmtime(path)
            return datetime.datetime.fromtimestamp(timestamp).strftime('%Y-%m-%d %H:%M:%S')
        except:
            return 'Unknown'

@method_decorator(csrf_exempt, name='dispatch')
class AsyncScanView(View):
    """异步扫描文件树"""
    @auth('model_storage.storage.view')
    def post(self, request):
        """启动异步扫描任务"""
        try:
            data = json.loads(request.body)
            base_path = data.get('path', '/HDD_Raid/SVN_MODEL_REPO')
            scan_type = data.get('type', 'local')  # local, remote, diff
            
            # 这里可以集成异步任务队列（如Celery）
            # 现在简化为同步处理，但限制扫描范围
            
            if scan_type == 'local':
                result = self.scan_local_limited(base_path)
            elif scan_type == 'remote':
                result = self.scan_remote_limited()
            else:
                result = {'status': 'error', 'message': '不支持的扫描类型'}
            
            return JsonResponse({'data': result})
            
        except Exception as e:
            return JsonResponse({'error': str(e)}, status=500)
    
    def scan_local_limited(self, base_path, max_items=500):
        """限制数量的本地扫描"""
        import os
        import time
        
        start_time = time.time()
        items = []
        count = 0
        
        try:
            for root, dirs, files in os.walk(base_path):
                if count >= max_items or (time.time() - start_time) > 30:  # 30秒超时
                    break
                    
                # 限制深度
                depth = root.replace(base_path, '').count(os.sep)
                if depth > 2:
                    dirs.clear()  # 停止deeper搜索
                    continue
                
                # 添加重要文件
                for filename in files:
                    if count >= max_items:
                        break
                    if self.is_important_file(filename):
                        items.append({
                            'path': os.path.join(root, filename),
                            'name': filename,
                            'type': 'file'
                        })
                        count += 1
                        
        except Exception as e:
            print(f"异步扫描出错: {e}")
            
        return {
            'status': 'completed',
            'items': items,
            'count': count,
            'duration': round(time.time() - start_time, 2)
        }
    
    def scan_remote_limited(self):
        """限制数量的远程扫描"""
        import requests
        import time
        
        start_time = time.time()
        remote_url = "http://***********/GPU_MODEL_REPO/01.DEV/"
        
        try:
            response = requests.get(remote_url, timeout=15)
            if response.status_code == 200:
                items = self.parse_remote_html(response.text, remote_url)
                return {
                    'status': 'completed',
                    'items': items[:100],  # 限制返回数量
                    'count': len(items),
                    'duration': round(time.time() - start_time, 2)
                }
        except Exception as e:
            print(f"远程扫描失败: {e}")
            
        return {
            'status': 'error',
            'message': '远程扫描失败',
            'duration': round(time.time() - start_time, 2)
        }
    
    def parse_remote_html(self, html_content, base_url):
        """解析远程目录的HTML内容"""
        import re
        files = []

        try:
            # 使用多种正则表达式模式来匹配不同格式的HTML
            patterns = [
                # 标准的<a href="...">...</a>格式
                r'<a\s+href="([^"]+)"[^>]*>([^<]+)</a>',
                # 可能有其他属性的格式
                r'<a\s+[^>]*href="([^"]+)"[^>]*>([^<]+)</a>',
                # 单引号格式
                r"<a\s+href='([^']+)'[^>]*>([^<]+)</a>",
            ]

            all_matches = []
            for pattern in patterns:
                matches = re.findall(pattern, html_content, re.IGNORECASE | re.DOTALL)
                all_matches.extend(matches)

            # 去重
            seen = set()
            unique_matches = []
            for href, text in all_matches:
                key = (href.strip(), text.strip())
                if key not in seen:
                    seen.add(key)
                    unique_matches.append(key)

            logger.info(f"找到 {len(unique_matches)} 个链接")

            for href, text in unique_matches:
                href = href.strip()
                text = text.strip()

                # 跳过父目录链接和绝对路径
                if (href.startswith('..') or href.startswith('/') or
                    href.startswith('http') or href.startswith('mailto:')):
                    continue

                # 跳过空链接
                if not href or not text:
                    continue

                # 跳过一些常见的非目录链接
                if text.lower() in ['parent directory', 'up', 'back', '..', 'name', 'last modified', 'size', 'description']:
                    continue

                # 跳过包含HTML标签的文本
                if '<' in text or '>' in text:
                    continue

                # 判断是否为目录（通常以/结尾）
                is_dir = href.endswith('/')
                name = text.strip()
                if is_dir:
                    name = name.rstrip('/')

                # 确保名称不为空
                if not name:
                    continue

                files.append({
                    'path': f"{base_url.rstrip('/')}/{href}",
                    'name': name,
                    'type': 'folder' if is_dir else 'file',
                    'size': 'DIR' if is_dir else 'Unknown',
                    'lastModified': 'Unknown',
                    'source': 'remote'
                })

            return files

        except Exception as e:
            logger.error(f"HTML解析失败: {e}")
            return []
    
    def is_important_file(self, filename):
        """判断是否为重要文件"""
        important_extensions = [
            '.tar.gz', '.zip', '.tar', '.7z',  # 压缩文件
            '.bin', '.safetensors', '.pt', '.pth',  # 模型文件
            '.txt', '.md', '.json', '.yaml', '.yml',  # 配置文件
            '.sh', '.bat', '.py', '.dockerfile'  # 脚本文件
        ]
        
        filename_lower = filename.lower()
        return any(filename_lower.endswith(ext) for ext in important_extensions) or \
               filename_lower in ['readme', 'license', 'changelog', 'md5sums.txt']

@method_decorator(csrf_exempt, name='dispatch')
class ReleasePlanView(View):
    """发布计划API"""
    @auth('model_storage.release_plan.view')
    def get(self, request):
        """获取发布计划列表"""
        try:
            plans = ReleasePlan.objects.all().order_by('-release_date')
            data = [{
                'id': plan.id,
                'card_model': plan.card_model,
                'model_name': plan.model_name,
                'release_date': plan.release_date.strftime('%Y-%m-%d'),
                'model_status': plan.model_status,
                'vendor_status': plan.vendor_status,
                'overall_status': plan.overall_status,
                'created_at': plan.created_at.isoformat() if plan.created_at else None
            } for plan in plans]
            
            return JsonResponse({'data': data})
        except Exception as e:
            return JsonResponse({'data': [], 'error': str(e)})
    def post(self, request):
        """创建发布计划"""
        try:
            data = json.loads(request.body)
            plan = ReleasePlan.objects.create(
                card_model=data['card_model'],
                model_name=data['model_name'],
                release_date=data['release_date'],
                model_status=data.get('model_status', 'inProgress'),
                vendor_status=data.get('vendor_status', 'inProgress'),
                overall_status=data.get('overall_status', 'preparing')
            )
            
            return JsonResponse({
                'data': {
                    'id': plan.id,
                    'message': '发布计划创建成功'
                }
            })
        except Exception as e:
            return JsonResponse({'error': str(e)}, status=500)
    @auth('model_storage.release_plan.edit')
    def put(self, request):
        """更新发布计划"""
        try:
            data = json.loads(request.body)
            plan = ReleasePlan.objects.get(id=data['id'])
            
            plan.card_model = data['card_model']
            plan.model_name = data['model_name']
            plan.release_date = data['release_date']
            plan.model_status = data.get('model_status', plan.model_status)
            plan.vendor_status = data.get('vendor_status', plan.vendor_status)
            plan.overall_status = data.get('overall_status', plan.overall_status)
            plan.save()
            
            return JsonResponse({
                'data': {
                    'id': plan.id,
                    'message': '发布计划更新成功'
                }
            })
        except ReleasePlan.DoesNotExist:
            return JsonResponse({'error': '发布计划不存在'}, status=404)
        except Exception as e:
            return JsonResponse({'error': str(e)}, status=500)
    @auth('model_storage.release_plan.del')
    def delete(self, request):
        """删除发布计划"""
        try:
            data = json.loads(request.body)
            plan = ReleasePlan.objects.get(id=data['id'])
            plan.delete()
            
            return JsonResponse({
                'data': {
                    'message': '发布计划删除成功'
                }
            })
        except ReleasePlan.DoesNotExist:
            return JsonResponse({'error': '发布计划不存在'}, status=404)
        except Exception as e:
            return JsonResponse({'error': str(e)}, status=500)

@method_decorator(csrf_exempt, name='dispatch')
class SvnCompareView(View):
    """SVN对比视图"""
    @auth('model_storage.file_compare.view')
    def get(self, request):
        """SVN远程对比"""
        try:
            compare_data = [
                {
                    'path': '/HDD_Raid/SVN_MODEL_REPO/Model/Baichuan2-13B/model.bin',
                    'local_status': 'synced',
                    'remote_status': 'synced',
                    'diff': '无差异'
                },
                {
                    'path': '/HDD_Raid/SVN_MODEL_REPO/Model/DeepSeek-V3/tokenizer.json',
                    'local_status': 'missing',
                    'remote_status': 'synced',
                    'diff': '本地缺失'
                },
                {
                    'path': '/HDD_Raid/SVN_MODEL_REPO/Vendor/Kunlunxin/P800/config.xml',
                    'local_status': 'modified',
                    'remote_status': 'synced',
                    'diff': '本地已修改'
                }
            ]
            
            return JsonResponse({'data': compare_data})
        except Exception as e:
            return JsonResponse({'data': [], 'error': str(e)})

@method_decorator(csrf_exempt, name='dispatch')
class CheckMissingView(View):
    """检查缺失文件视图"""
    @auth('model_storage.storage.view')
    def get(self, request):
        """检测缺失文件（保持兼容性）"""
        try:
            missing_data = {
                'missing_count': 5,
                'missing_files': [
                    '/Model/DeepSeek-V3/config.json',
                    '/Vendor/AMD/RX7900/benchmark.bin',
                    '/Vendor/Biren/BR100/driver.so'
                ],
                'check_time': timezone.now().isoformat()
            }
            
            return JsonResponse({'data': missing_data})
        except Exception as e:
            return JsonResponse({'data': {'error': str(e)}})

@method_decorator(csrf_exempt, name='dispatch')
class RemoteTreeDetailView(View):
    """远程树详情视图"""
    @auth('model_storage.storage.view')
    def get(self, request):
        """获取远程目录的详细文件列表（用于懒加载）"""
        # 处理/api/model-storage/remote-lazy-load/请求
        if request.path.endswith('/remote-lazy-load/'):
            path = request.GET.get('path', '')
            try:
                # 获取远程目录的子节点
                remote_children = self.get_remote_directory_children(path)
                return JsonResponse({'data': remote_children})
            except Exception as e:
                logger.error(f"获取远程目录详情失败: {e}")
                return JsonResponse({'data': [], 'error': str(e)})
        else:
            # 原有的处理逻辑
            path = request.GET.get('path', '')
            try:
                # 获取远程目录的子节点
                remote_children = self.get_remote_directory_children(path)
                return JsonResponse({'data': remote_children})
            except Exception as e:
                logger.error(f"获取远程目录详情失败: {e}")
                return JsonResponse({'data': [], 'error': str(e)})

    def get_remote_directory_children(self, path):
        """获取远程目录的子节点（树形结构）"""
        import requests
        from urllib.parse import urljoin

        remote_url = network_config.get_remote_url()
        auth = network_config.get_auth_credentials()

        try:
            # 构建完整的远程URL
            if path.startswith('/'):
                path = path[1:]  # 移除开头的斜杠

            full_url = urljoin(remote_url, path + '/' if path else '')

            response = network_retry.execute_with_retry(
                lambda: requests.get(full_url, timeout=15, auth=auth)
            )
            response.raise_for_status()

            # 解析HTML获取文件列表
            files = self.parse_remote_html(response.text, full_url)

            # 转换为树形结构
            children = []
            for file_info in files:
                if file_info['type'] == 'folder':
                    children.append({
                        'title': f"📁 {file_info['name']}",
                        'key': f"remote_{file_info['name']}_{hash(file_info['path'])}",
                        'icon': '📁',
                        'isLeaf': False,
                        'data': {
                            'name': file_info['name'],
                            'path': f"{path}/{file_info['name']}" if path else file_info['name'],
                            'type': 'folder',
                            'status': 'synced',
                            'source': 'remote'
                        }
                    })
                else:
                    children.append({
                        'title': f"📄 {file_info['name']}",
                        'key': f"remote_file_{file_info['name']}_{hash(file_info['path'])}",
                        'icon': '📄',
                        'isLeaf': True,
                        'data': {
                            'name': file_info['name'],
                            'path': f"{path}/{file_info['name']}" if path else file_info['name'],
                            'type': 'file',
                            'status': 'synced',
                            'source': 'remote',
                            'size': file_info.get('size', 'Unknown')
                        }
                    })

            return children

        except Exception as e:
            logger.error(f"获取远程目录 {path} 的子节点失败: {e}")
            raise
    
    def parse_remote_html(self, html_content, base_url):
        """解析远程目录的HTML内容"""
        import re
        import html
        from urllib.parse import urljoin, unquote
        files = []

        try:
            # 使用多种正则表达式模式来匹配不同格式的HTML
            patterns = [
                # 标准的<a href="...">...</a>格式
                r'<a\s+href="([^"]+)"[^>]*>([^<]+)</a>',
                # 可能有其他属性的格式
                r'<a\s+[^>]*href="([^"]+)"[^>]*>([^<]+)</a>',
                # 单引号格式
                r"<a\s+href='([^']+)'[^>]*>([^<]+)</a>",
            ]

            all_matches = []
            for pattern in patterns:
                matches = re.findall(pattern, html_content, re.IGNORECASE | re.DOTALL)
                all_matches.extend(matches)

            # 去重
            seen = set()
            unique_matches = []
            for href, text in all_matches:
                # HTML解码，处理特殊字符
                href = html.unescape(href.strip())
                text = html.unescape(text.strip())
                
                # 处理URL编码
                href = unquote(href)
                
                key = (href, text)
                if key not in seen:
                    seen.add(key)
                    unique_matches.append(key)

            logger.info(f"找到 {len(unique_matches)} 个链接")

            for href, text in unique_matches:
                # 跳过父目录链接和绝对路径
                if (href.startswith('..') or href.startswith('/') or
                    href.startswith('http') or href.startswith('mailto:')):
                    continue

                # 跳过空链接
                if not href or not text:
                    continue

                # 跳过一些常见的非目录链接
                if text.lower() in ['parent directory', 'up', 'back', '..', 'name', 'last modified', 'size', 'description']:
                    continue

                # 跳过包含HTML标签的文本
                if '<' in text or '>' in text:
                    continue

                # 判断是否为目录（通常以/结尾）
                is_dir = href.endswith('/')
                name = text
                if is_dir:
                    name = name.rstrip('/')

                # 确保名称不为空
                if not name:
                    continue

                # 使用urljoin正确处理相对路径
                full_path = urljoin(base_url, href)
                
                files.append({
                    'path': full_path,
                    'name': name,
                    'type': 'folder' if is_dir else 'file',
                    'size': 'DIR' if is_dir else 'Unknown',
                    'lastModified': 'Unknown',
                    'source': 'remote'
                })

            return files

        except Exception as e:
            logger.error(f"HTML解析失败: {e}")
            return []
    
    def get_remote_directory_details(self, path):
        """获取远程目录的详细信息"""
        import requests
        from urllib.parse import urljoin
        
        # SVN远程仓库配置
        base_url = "http://***********/GPU_MODEL_REPO/01.DEV/"
        auth = ('sys49169', 'Aa123,.,.') 
        
        try:
            # 根据路径类型构建远程URL
            if '/Model/' in path:
                # 模型路径处理
                model_name = path.split('/Model/')[-1].strip('/')
                remote_url = urljoin(base_url, f"Model/{model_name}/")
            elif '/Vendor/' in path:
                # 厂商路径处理  
                vendor_path = path.split('/Vendor/')[-1].strip('/')
                remote_url = urljoin(base_url, f"Vendor/{vendor_path}/")
            else:
                # 根目录处理
                remote_url = base_url
            
            print(f"正在访问远程URL: {remote_url}")
            
            response = requests.get(remote_url, timeout=15, auth=auth)
            response.raise_for_status()
            
            # 解析HTML内容
            files = self.parse_directory_html(response.text, remote_url)
            
            return files
            
        except Exception as e:
            print(f"获取远程目录失败: {e}")
            return []
    
    def parse_directory_html(self, html_content, base_url):
        """解析目录HTML，提取文件信息"""
        import re
        from datetime import datetime
        
        files = []
        
        # 更精确的正则表达式，提取文件信息
        # 匹配格式：<a href="filename">filename</a> 以及可能的日期和大小信息
        link_pattern = r'<a href="([^"]+)"[^>]*>([^<]+)</a>\s*([^<]*)'
        matches = re.findall(link_pattern, html_content)
        
        for href, name, extra_info in matches:
            # 跳过父目录链接
            if href.startswith('..') or href.startswith('/'):
                continue
            
            # 清理文件名
            clean_name = name.strip()
            is_directory = href.endswith('/')
            
            if is_directory:
                clean_name = clean_name.rstrip('/')
            
            # 解析额外信息（日期、大小等）
            file_size = 'DIR' if is_directory else self.extract_size_from_info(extra_info)
            last_modified = self.extract_date_from_info(extra_info)
            
            # 判断文件状态
            status = self.determine_file_status(clean_name, is_directory)
            
            files.append({
                'name': clean_name,
                'path': f"{base_url}{href}",
                'type': 'folder' if is_directory else 'file',
                'size': file_size,
                'lastModified': last_modified,
                'status': status,
                'source': 'remote'
            })
        
        return files
    
    def extract_size_from_info(self, info_text):
        """从额外信息中提取文件大小"""
        import re
        
        # 匹配文件大小格式: 123K, 456M, 789G等
        size_pattern = r'(\d+(?:\.\d+)?)\s*([KMGT]?B?)'
        match = re.search(size_pattern, info_text, re.IGNORECASE)
        
        if match:
            size, unit = match.groups()
            return f"{size}{unit.upper()}"
        
        # 如果没有匹配到大小，检查是否有字节数
        byte_pattern = r'(\d+)\s*bytes?'
        byte_match = re.search(byte_pattern, info_text, re.IGNORECASE)
        
        if byte_match:
            bytes_size = int(byte_match.group(1))
            return self.format_file_size(bytes_size)
        
        return 'Unknown'
    
    def extract_date_from_info(self, info_text):
        """从额外信息中提取修改日期"""
        import re
        from datetime import datetime
        
        # 匹配日期格式: DD-Mon-YYYY HH:MM
        date_pattern = r'(\d{1,2}-\w{3}-\d{4}\s+\d{1,2}:\d{2})'
        match = re.search(date_pattern, info_text)
        
        if match:
            return match.group(1)
        
        # 匹配其他可能的日期格式
        alt_date_pattern = r'(\d{4}-\d{2}-\d{2}\s+\d{2}:\d{2})'
        alt_match = re.search(alt_date_pattern, info_text)
        
        if alt_match:
            return alt_match.group(1)
        
        return 'Unknown'
    
    def determine_file_status(self, filename, is_directory):
        """根据文件名和类型判断状态"""
        # 重要文件检查
        important_files = ['md5sums.txt', 'readme', 'license', 'config.json']
        
        if is_directory:
            # 目录状态逻辑
            if any(important in filename.lower() for important in ['model', 'vendor']):
                return 'synced'
            return 'synced'
        else:
            # 文件状态逻辑
            if filename.lower() in important_files:
                return 'synced'
            elif filename.lower().endswith(('.tar.gz', '.zip', '.bin', '.safetensors')):
                return 'synced'
            else:
                return 'synced'
    
    def format_file_size(self, size_bytes):
        """格式化文件大小"""
        if size_bytes == 0:
            return "0B"
        
        size_names = ["B", "KB", "MB", "GB", "TB"]
        import math
        i = int(math.floor(math.log(size_bytes, 1024)))
        p = math.pow(1024, i)
        s = round(size_bytes / p, 2)
        return f"{s}{size_names[i]}"

@method_decorator(csrf_exempt, name='dispatch')
class LocalFullTreeView(View):
    """获取完整的本地文件树"""
    @auth('model_storage.storage.view')
    def get(self, request):
        try:
            base_path = request.GET.get('base_path', '/HDD_Raid/SVN_MODEL_REPO')
            tree_data = self.scan_local_tree(base_path)
            return JsonResponse({'data': tree_data})
        except Exception as e:
            logger.error(f"获取本地文件树失败: {e}")
            return JsonResponse({
                'error': str(e),
                'data': []
            })

    def scan_local_tree(self, base_path):
        """递归扫描本地目录树"""
        result = []
        try:
            for root, dirs, files in os.walk(base_path):
                # 跳过隐藏文件夹
                dirs[:] = [d for d in dirs if not d.startswith('.')]
                files = [f for f in files if not f.startswith('.')]
                
                rel_path = os.path.relpath(root, base_path)
                if rel_path == '.':
                    rel_path = ''
                    
                # 添加目录
                for dir_name in dirs:
                    dir_path = os.path.join(rel_path, dir_name)
                    dir_full_path = os.path.join(root, dir_name)
                    dir_stat = os.stat(dir_full_path)
                    
                    result.append({
                        'key': f'local_{dir_path.replace("/", "_")}',
                        'title': f'[DIR] {dir_name}',
                        'path': dir_path,
                        'type': 'directory',
                        'size': 0,
                        'modifiedTime': datetime.fromtimestamp(dir_stat.st_mtime).strftime('%Y-%m-%d %H:%M:%S'),
                        'status': 'synced',
                        'isLeaf': False
                    })
                
                # 添加文件
                for file_name in files:
                    file_path = os.path.join(rel_path, file_name)
                    file_full_path = os.path.join(root, file_name)
                    file_stat = os.stat(file_full_path)
                    
                    result.append({
                        'key': f'local_{file_path.replace("/", "_")}',
                        'title': f'{self.get_file_icon(file_name)} {file_name}',
                        'path': file_path,
                        'type': 'file',
                        'size': file_stat.st_size,
                        'modifiedTime': datetime.fromtimestamp(file_stat.st_mtime).strftime('%Y-%m-%d %H:%M:%S'),
                        'status': 'synced',
                        'isLeaf': True
                    })
            
            return result
        except Exception as e:
            logger.error(f"扫描本地目录树失败: {e}")
            raise

    def get_file_icon(self, filename):
        """根据文件名获取图标"""
        filename_lower = filename.lower()
        if filename_lower.endswith(('.txt', '.md', '.doc', '.docx')):
            return '[DOC]'
        elif filename_lower.endswith(('.tar.gz', '.zip', '.rar', '.7z')):
            return '[ZIP]'
        elif filename_lower.endswith(('.json', '.yaml', '.yml', '.xml')):
            return '[CFG]'
        elif filename_lower.endswith(('.py', '.js', '.sh', '.bat')):
            return '[SCR]'
        else:
            return '[FILE]'

@method_decorator(csrf_exempt, name='dispatch')
class RemoteFullTreeView(View):
    """获取完整的远程文件树"""
    @auth('model_storage.storage.view')
    def get(self, request):
        try:
            remote_url = request.GET.get('remote_url', 'http://***********/GPU_MODEL_REPO/01.DEV/')
            tree_data = self.scan_remote_tree(remote_url)
            return JsonResponse({'data': tree_data})
        except Exception as e:
            logger.error(f"获取远程文件树失败: {e}")
            return JsonResponse({
                'error': str(e),
                'data': []
            })

    def scan_remote_tree(self, remote_url):
        """递归扫描远程目录树"""
        result = []
        try:
            # 获取认证信息
            auth = network_config.get_auth_credentials()
            
            # 获取根目录内容
            response = network_retry.execute_with_retry(
                lambda: requests.get(remote_url, timeout=30, auth=auth)
            )
            response.raise_for_status()
            
            # 解析HTML获取目录列表
            items = self.parse_remote_html(response.text, remote_url)
            
            # 递归处理每个目录
            for item in items:
                if item['type'] == 'folder':
                    # 构建子目录URL
                    sub_url = urljoin(remote_url, item['name'] + '/')
                    # 递归获取子目录内容
                    try:
                        sub_response = network_retry.execute_with_retry(
                            lambda: requests.get(sub_url, timeout=30, auth=auth)
                        )
                        sub_response.raise_for_status()
                        sub_items = self.parse_remote_html(sub_response.text, sub_url)
                        
                        # 添加目录节点
                        result.append({
                            'key': f'remote_{item["name"].replace("/", "_")}',
                            'title': f'[DIR] {item["name"]}',
                            'path': item['name'],
                            'type': 'directory',
                            'size': 0,
                            'modifiedTime': item.get('modified_time', ''),
                            'status': 'synced',
                            'isLeaf': False,
                            'children': sub_items
                        })
                    except Exception as e:
                        logger.error(f"获取子目录失败 {sub_url}: {e}")
                        continue
                else:
                    # 添加文件节点
                    result.append({
                        'key': f'remote_{item["name"].replace("/", "_")}',
                        'title': f'{self.get_file_icon(item["name"])} {item["name"]}',
                        'path': item['name'],
                        'type': 'file',
                        'size': item.get('size', 0),
                        'modifiedTime': item.get('modified_time', ''),
                        'status': 'synced',
                        'isLeaf': True
                    })
            
            return result
        except Exception as e:
            logger.error(f"扫描远程目录树失败: {e}")
            raise

    def get_file_icon(self, filename):
        """根据文件名获取图标"""
        filename_lower = filename.lower()
        if filename_lower.endswith(('.txt', '.md', '.doc', '.docx')):
            return '[DOC]'
        elif filename_lower.endswith(('.tar.gz', '.zip', '.rar', '.7z')):
            return '[ZIP]'
        elif filename_lower.endswith(('.json', '.yaml', '.yml', '.xml')):
            return '[CFG]'
        elif filename_lower.endswith(('.py', '.js', '.sh', '.bat')):
            return '[SCR]'
        else:
            return '[FILE]'

    def parse_remote_html(self, html_content, base_url):
        """解析远程HTML内容"""
        from bs4 import BeautifulSoup
        soup = BeautifulSoup(html_content, 'html.parser')
        items = []
        
        for link in soup.find_all('a'):
            href = link.get('href')
            if not href or href in ['/', '../']:
                continue
                
            name = link.text.strip()
            if not name:
                continue
                
            # 获取文件信息
            info_text = link.find_next_sibling(text=True)
            if info_text:
                info_text = info_text.strip()
            else:
                info_text = ''
                
            is_directory = href.endswith('/')
            
            item = {
                'name': name,
                'type': 'folder' if is_directory else 'file',
                'modified_time': self.extract_date_from_info(info_text),
                'size': self.extract_size_from_info(info_text) if not is_directory else 0
            }
            
            items.append(item)
            
        return items

    def extract_size_from_info(self, info_text):
        """从信息文本中提取文件大小"""
        import re
        size_pattern = r'(\d+(?:\.\d+)?)\s*([KMGT]?B)'
        match = re.search(size_pattern, info_text)
        if match:
            size, unit = match.groups()
            size = float(size)
            if unit == 'KB':
                size *= 1024
            elif unit == 'MB':
                size *= 1024 * 1024
            elif unit == 'GB':
                size *= 1024 * 1024 * 1024
            elif unit == 'TB':
                size *= 1024 * 1024 * 1024 * 1024
            return int(size)
        return 0

    def extract_date_from_info(self, info_text):
        """从信息文本中提取日期"""
        import re
        date_pattern = r'\d{4}-\d{2}-\d{2}\s+\d{2}:\d{2}(?::\d{2})?'
        match = re.search(date_pattern, info_text)
        if match:
            return match.group()
        return ''

# ============= 新版发布计划相关API =============

@method_decorator(csrf_exempt, name='dispatch')
class NewReleasePlanView(View):
    """新版发布计划API"""

    @auth('model_storage.release_plan.view')
    def get(self, request, pk=None):
        """获取发布计划列表或单个计划详情"""
        try:
            if pk is not None:
                plan = NewReleasePlan.objects.get(id=pk)
                data = {
                    'id': plan.id,
                    'name': plan.name,
                    'description': plan.description,
                    'start_date': plan.start_date.strftime('%Y-%m-%d'),
                    'end_date': plan.end_date.strftime('%Y-%m-%d'),
                    'status': plan.status,
                    'status_display': plan.status_display,
                    'risk_level': plan.risk_level,
                    'risk_level_display': plan.risk_level_display,
                    'created_by': plan.created_by,
                    'created_at': plan.created_at.strftime('%Y-%m-%d %H:%M:%S'),
                    'updated_at': plan.updated_at.strftime('%Y-%m-%d %H:%M:%S'),
                    'task_count': plan.task_count,
                    'completed_tasks': plan.completed_tasks
                }
                return JsonResponse({'data': data, 'error': ''})
            
            plans = NewReleasePlan.objects.all().order_by('-created_at')
            data = []
            
            for plan in plans:
                plan_data = {
                    'id': plan.id,
                    'name': plan.name,
                    'description': plan.description,
                    'start_date': plan.start_date.strftime('%Y-%m-%d'),
                    'end_date': plan.end_date.strftime('%Y-%m-%d'),
                    'status': plan.status,
                    'status_display': plan.status_display,
                    'risk_level': plan.risk_level,
                    'risk_level_display': plan.risk_level_display,
                    'created_by': plan.created_by,
                    'created_at': plan.created_at.strftime('%Y-%m-%d %H:%M:%S'),
                    'updated_at': plan.updated_at.strftime('%Y-%m-%d %H:%M:%S'),
                    'task_count': plan.task_count,
                    'completed_tasks': plan.completed_tasks
                }
                data.append(plan_data)
            
            return JsonResponse({'data': data, 'error': ''})
        except NewReleasePlan.DoesNotExist:
            return JsonResponse({'data': None, 'error': '发布计划不存在'}, status=404)
        except Exception as e:
            return JsonResponse({'data': [], 'error': str(e)})
    
    @auth('model_storage.release_plan.add')
    def post(self, request):
        """创建发布计划"""
        try:
            data = json.loads(request.body)
            
            plan = NewReleasePlan.objects.create(
                name=data['name'],
                description=data.get('description', ''),
                start_date=data['start_date'],
                end_date=data['end_date'],
                risk_level=data.get('risk_level', 'low'),
                created_by=data.get('created_by', 'Admin')  # 提供默认值
            )
            
            return JsonResponse({
                'data': {
                    'id': plan.id,
                    'message': '发布计划创建成功'
                },
                'error': ''
            })
        except Exception as e:
            return JsonResponse({'data': None, 'error': str(e)}, status=500)
    
    @auth('model_storage.release_plan.edit')
    def put(self, request, pk=None):
        """更新发布计划"""
        try:
            data = json.loads(request.body)
            plan_id = pk if pk is not None else data.get('id')
            
            if not plan_id:
                return JsonResponse({'data': None, 'error': '缺少计划ID'}, status=400)
            
            plan = NewReleasePlan.objects.get(id=plan_id)
            plan.name = data['name']
            plan.description = data.get('description', plan.description)
            plan.start_date = data['start_date']
            plan.end_date = data['end_date']
            plan.status = data.get('status', plan.status)
            plan.risk_level = data.get('risk_level', plan.risk_level)
            plan.save()
            
            return JsonResponse({
                'data': {
                    'id': plan.id,
                    'message': '发布计划更新成功'
                },
                'error': ''
            })
        except NewReleasePlan.DoesNotExist:
            return JsonResponse({'data': None, 'error': '发布计划不存在'}, status=404)
        except Exception as e:
            return JsonResponse({'data': None, 'error': str(e)}, status=500)
    
    @auth('model_storage.release_plan.del')
    def delete(self, request, pk=None):
        """删除发布计划"""
        try:
            if pk is not None:
                plan = NewReleasePlan.objects.get(id=pk)
            else:
                data = json.loads(request.body)
                plan = NewReleasePlan.objects.get(id=data['id'])
            
            plan.delete()
            
            return JsonResponse({
                'data': {'message': '发布计划删除成功'},
                'error': ''
            })
        except NewReleasePlan.DoesNotExist:
            return JsonResponse({'data': None, 'error': '发布计划不存在'}, status=404)
        except Exception as e:
            return JsonResponse({'data': None, 'error': str(e)}, status=500)


@method_decorator(csrf_exempt, name='dispatch')
class TestTaskView(View):
    """测试任务管理"""

    @auth('model_storage.test_tasks.view')
    def get(self, request, pk=None):
        """获取测试任务列表或单个任务"""
        if pk:
            try:
                task = TestTask.objects.get(pk=pk)
                return json_response(task.to_dict())
            except TestTask.DoesNotExist:
                return json_response(error=f"测试任务 with id {pk} does not exist.", status=404)
        
        # 可选过滤参数
        plan_id = request.GET.get('plan_id')
        gpu_model = request.GET.get('gpu_model')
        model_name = request.GET.get('model_name')
        tester = request.GET.get('tester')
        test_status = request.GET.get('test_status')

        # 构建查询集
        tasks = TestTask.objects.all()
        if plan_id:
            tasks = tasks.filter(release_plan_id=plan_id)
        if gpu_model:
            tasks = tasks.filter(gpu_model=gpu_model)
        if model_name:
            tasks = tasks.filter(model_name__icontains=model_name)
        if tester:
            tasks = tasks.filter(tester__icontains=tester)
        if test_status:
            tasks = tasks.filter(test_status=test_status)

        tasks = tasks.order_by('-created_at')
        task_list = [t.to_dict() for t in tasks]
        return json_response(task_list)

    @auth('model_storage.test_tasks.add')
    def post(self, request):
        """创建新测试任务"""
        try:
            data = json.loads(request.body)
            # 修改：只要求 model_name 和 tester
            required_fields = ['model_name', 'tester']
            if not all(field in data for field in required_fields):
                return json_response(error="缺少必要字段：模型名称、测试人员", status=400)

            # 处理日期字段，使其可以为空
            from datetime import datetime
            for date_field in ['start_date', 'end_date']:
                if data.get(date_field):
                    try:
                        if isinstance(data[date_field], str) and data[date_field]:
                            data[date_field] = datetime.strptime(data[date_field], '%Y-%m-%d').date()
                        else:
                            data[date_field] = None
                    except (ValueError, TypeError):
                        data[date_field] = None
            
            task = TestTask.objects.create(
                release_plan_id=data.get('release_plan'),
                model_name=data.get('model_name'),
                model_type=data.get('model_type'),
                gpu_model=data.get('gpu_model'),
                tester=data.get('tester'),
                start_date=data.get('start_date'),
                end_date=data.get('end_date'),
                priority=data.get('priority', 'p2'),
                test_status=data.get('test_status', 'pending'),
                progress=data.get('progress', 0),
                document_output=data.get('document_output', False),
                test_case_set_id=data.get('test_case_set_id'),
                notes=data.get('notes', '')
            )
            return json_response(task.to_dict(), status=201)
        except json.JSONDecodeError:
            return json_response(error="无效的JSON格式", status=400)
        except Exception as e:
            return json_response(error=f"创建失败: {e}", status=500)

    @auth('model_storage.test_tasks.edit')
    def patch(self, request, pk):
        """更新测试任务（部分更新）"""
        try:
            logger.info(f"[PATCH TestTask] pk={pk}, body={request.body.decode(errors='ignore')}")
            task = TestTask.objects.get(pk=pk)
            data = json.loads(request.body)
            
            # 更新字段
            for key, value in data.items():
                if hasattr(task, key):
                    # 特殊处理status字段，因为表单传的是status，模型是test_status
                    if key == 'status':
                        setattr(task, 'test_status', value)
                    # 特殊处理日期字段
                    elif key in ['start_date', 'end_date', 'actual_start_date', 'actual_end_date']:
                        if value:
                            try:
                                from datetime import datetime
                                if isinstance(value, str):
                                    # 尝试解析日期字符串
                                    date_obj = datetime.strptime(value, '%Y-%m-%d').date()
                                    setattr(task, key, date_obj)
                                else:
                                    setattr(task, key, value)
                            except ValueError as e:
                                logger.error(f"日期格式错误: {key}={value}, error={e}")
                                return json_response(error=f"日期格式错误: {key}应为YYYY-MM-DD格式", status=400)
                        else:
                            setattr(task, key, None)
                    elif key not in ['id', 'created_at', 'created_by', 'release_plan']:
                        setattr(task, key, value)
            
            task.updated_at = timezone.now()
            task.save()
            
            return json_response(task.to_dict())
        except TestTask.DoesNotExist:
            return json_response(error=f"测试任务 with id {pk} does not exist.", status=404)
        except json.JSONDecodeError:
            return json_response(error="无效的JSON格式", status=400)
        except Exception as e:
            logger.exception("[PATCH TestTask] 未知异常")
            return json_response(error=f"更新失败: {e}", status=500)

    @auth('model_storage.test_tasks.del')
    def delete(self, request, pk):
        """删除测试任务"""
        try:
            task = TestTask.objects.get(pk=pk)
            task_name = f"{task.model_name} - {task.tester}"
            task.delete()
            return json_response({'message': f'测试任务 "{task_name}" 删除成功'})
        except TestTask.DoesNotExist:
            return json_response(error=f"测试任务 with id {pk} does not exist.", status_code=404)


@method_decorator(csrf_exempt, name='dispatch')
class ImportTestTaskView(View):
    """从Excel导入测试任务"""

    def options(self, request):
        """处理预检请求"""
        response = HttpResponse()
        response['Access-Control-Allow-Origin'] = '*'
        response['Access-Control-Allow-Methods'] = 'GET, POST, OPTIONS'
        response['Access-Control-Allow-Headers'] = 'Origin, Content-Type, Accept, X-Token'
        response['Access-Control-Max-Age'] = '86400'  # 24小时
        return response

    @auth('model_storage.test_tasks.view')
    def get(self, request):
        """下载导入模板"""
        try:
            import os
            import urllib.parse
            from django.http import HttpResponse
            from django.conf import settings
            import openpyxl
            from openpyxl.styles import Font, Alignment, PatternFill
            from io import BytesIO
            
            # 创建新的工作簿
            wb = openpyxl.Workbook()
            ws = wb.active
            ws.title = "测试任务导入模板"
            
            # 定义表头
            headers = [
                '模型名称',      # 必填
                '人员名称',      # 必填
                '计划开始时间',   # 可选，格式：2024-01-15
                '计划结束时间',   # 可选，格式：2024-01-20
                'GPU型号',       # 可选，P800/P800-PCIe/RG800等
                '优先级',        # 可选，高/中/低 或 p1/p2/p3
                '测试状态',      # 可选，待开始/进行中/已完成/已取消/阻塞中/已延期
                '进度',          # 可选，0-100的数字
                '资料输出',      # 可选，是/否
                '备注'           # 可选
            ]
            
            # 设置表头样式
            header_font = Font(bold=True, color="FFFFFF")
            header_fill = PatternFill(start_color="4472C4", end_color="4472C4", fill_type="solid")
            header_alignment = Alignment(horizontal="center", vertical="center")
            
            # 写入表头
            for col, header in enumerate(headers, 1):
                cell = ws.cell(row=1, column=col, value=header)
                cell.font = header_font
                cell.fill = header_fill
                cell.alignment = header_alignment
            
            # 添加示例数据
            example_data = [
                ['Baichuan2-13B', '张三', '2024-01-15', '2024-01-20', 'P800', '高', '待开始', '0', '否', '模型基础功能测试'],
                ['Qwen2.5-7B', '李四', '2024-01-16', '2024-01-25', 'R5500_G7', 'p2', '进行中', '30', '是', '性能基准测试'],
                ['DeepSeek-V3', '王五', '2024-01-18', '2024-01-30', 'C550', '低', '已完成', '100', '是', '兼容性测试完成']
            ]
            
            for row, data in enumerate(example_data, 2):
                for col, value in enumerate(data, 1):
                    ws.cell(row=row, column=col, value=value)
            
            # 设置列宽
            column_widths = [20, 15, 15, 15, 12, 10, 12, 8, 10, 30]
            for col, width in enumerate(column_widths, 1):
                ws.column_dimensions[openpyxl.utils.get_column_letter(col)].width = width
            
            # 添加说明工作表
            ws_info = wb.create_sheet("填写说明")
            info_data = [
                ["字段名称", "是否必填", "填写说明", "示例"],
                ["模型名称", "是", "要测试的模型名称", "Baichuan2-13B"],
                ["人员名称", "是", "负责测试的人员姓名", "张三"],
                ["计划开始时间", "否", "格式：YYYY-MM-DD", "2024-01-15"],
                ["计划结束时间", "否", "格式：YYYY-MM-DD", "2024-01-20"],
                ["GPU型号", "否", "P800/P800-PCIe/RG800/R5500_G6/R5500_G7/R5330_G7/C550/C500", "P800"],
                ["优先级", "否", "高/中/低 或 p1/p2/p3", "高 或 p1"],
                ["测试状态", "否", "待开始/进行中/已完成/已取消/阻塞中/已延期", "待开始"],
                ["进度", "否", "0-100的数字，表示完成百分比", "30"],
                ["资料输出", "否", "是/否，表示是否已输出测试资料文档", "是"],
                ["备注", "否", "任务相关说明", "模型基础功能测试"]
            ]
            
            # 设置说明表头样式
            for row, data in enumerate(info_data, 1):
                for col, value in enumerate(data, 1):
                    cell = ws_info.cell(row=row, column=col, value=value)
                    if row == 1:  # 表头
                        cell.font = header_font
                        cell.fill = header_fill
                        cell.alignment = header_alignment
            
            # 设置说明表列宽
            info_column_widths = [15, 10, 40, 20]
            for col, width in enumerate(info_column_widths, 1):
                ws_info.column_dimensions[openpyxl.utils.get_column_letter(col)].width = width
            
            # 保存到内存
            output = BytesIO()
            wb.save(output)
            output.seek(0)
            file_content = output.getvalue()
            output.close()
            
            # 创建HttpResponse
            response = HttpResponse(file_content)
            
            # 设置Content-Type
            response['Content-Type'] = 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
            
            # 对文件名进行URL编码，确保中文文件名可以正常显示
            filename = '测试任务导入模板.xlsx'
            encoded_filename = urllib.parse.quote(filename)
            
            # 设置多种格式的Content-Disposition，增加兼容性
            response['Content-Disposition'] = f'attachment; filename="{filename}"; filename*=UTF-8\'\'{encoded_filename}'
            
            # 设置文件大小
            response['Content-Length'] = len(file_content)
            
            # 添加CORS头
            response['Access-Control-Allow-Origin'] = '*'
            response['Access-Control-Allow-Methods'] = 'GET, OPTIONS'
            response['Access-Control-Allow-Headers'] = 'Origin, Content-Type, Accept, X-Token'
            
            # 添加缓存控制头
            response['Cache-Control'] = 'no-cache, no-store, must-revalidate'
            response['Pragma'] = 'no-cache'
            response['Expires'] = '0'
            
            return response
        except Exception as e:
            logger.error(f"下载模板失败: {e}")
            return json_response(error=f"下载模板失败: {e}", status_code=500)

    @auth('model_storage.test_tasks.add')
    def post(self, request):
        """从Excel导入测试任务"""
        file = request.FILES.get('file')
        release_plan_id = request.POST.get('release_plan_id')

        if not file:
            return json_response(error="未提供文件", status_code=400)
        if not release_plan_id:
            return json_response(error="未提供发布计划ID", status_code=400)

        try:
            plan = NewReleasePlan.objects.get(id=release_plan_id)
        except NewReleasePlan.DoesNotExist:
            return json_response(error="指定的发布计划不存在", status_code=404)

        try:
            workbook = openpyxl.load_workbook(file)
            sheet = workbook.active
            
            # 获取表头
            header = [cell.value for cell in sheet[1]]
            
            # 检查必须的列
            required_columns = ['模型名称', '人员名称']
            missing_columns = []
            column_mapping = {}
            
            for col in required_columns:
                try:
                    column_mapping[col] = header.index(col)
                except ValueError:
                    missing_columns.append(col)
            
            if missing_columns:
                return json_response(
                    error=f"Excel文件必须包含以下列: {', '.join(missing_columns)}", 
                    status_code=400
                )
            
            # 可选列的映射
            optional_columns = {
                '计划开始时间': 'start_date',
                '计划结束时间': 'end_date',
                'GPU型号': 'gpu_model',
                '优先级': 'priority',
                '测试状态': 'test_status',
                '进度': 'progress',
                '资料输出': 'document_output',
                '备注': 'notes'
            }
            
            for col, field in optional_columns.items():
                try:
                    column_mapping[col] = header.index(col)
                except ValueError:
                    pass  # 可选列不存在时忽略
            
            # 解析数据
            tasks_data = []
            errors = []
            
            for row_num, row in enumerate(sheet.iter_rows(min_row=2, values_only=True), start=2):
                if not row or all(cell is None for cell in row):
                    continue
                
                try:
                    # 获取必填字段
                    model_name = row[column_mapping['模型名称']]
                    tester_name = row[column_mapping['人员名称']]
                    
                    if not model_name or not tester_name:
                        errors.append(f"第{row_num}行: 模型名称和人员名称不能为空")
                        continue
                    
                    # 构建任务数据
                    task_data = {
                        'model_name': str(model_name).strip(),
                        'tester': str(tester_name).strip(),
                        # 设置默认值以避免数据库错误
                        'model_type': 'inference',  # 默认推理类型
                        'gpu_model': 'P800',        # 默认P800
                        'start_date': timezone.now().date(),  # 默认今天开始
                        'end_date': timezone.now().date() + timedelta(days=7),  # 默认7天后结束
                        'priority': 'p2',           # 默认中等优先级
                        'test_status': 'pending',   # 默认待开始状态
                        'progress': 0,              # 默认进度0%
                        'document_output': False,   # 默认未输出资料
                        'notes': ''                 # 默认空备注
                    }
                    
                    # 处理可选字段
                    for col, field in optional_columns.items():
                        if col in column_mapping:
                            value = row[column_mapping[col]]
                            if value is not None:
                                value = str(value).strip()
                                
                                # 特殊处理不同字段类型
                                if field == 'priority':
                                    # 统一优先级格式为 p1/p2/p3
                                    priority_mapping = {
                                        '高': 'p1', 'high': 'p1', 'p1': 'p1',
                                        '中': 'p2', 'medium': 'p2', 'p2': 'p2', 
                                        '低': 'p3', 'low': 'p3', 'p3': 'p3'
                                    }
                                    task_data[field] = priority_mapping.get(value.lower(), 'p2')
                                elif field == 'progress':
                                    try:
                                        # 处理百分比格式
                                        if '%' in value:
                                            value = value.replace('%', '')
                                        task_data[field] = min(100, max(0, int(float(value))))
                                    except (ValueError, TypeError):
                                        task_data[field] = 0
                                elif field == 'test_status':
                                    # 统一测试状态格式
                                    status_mapping = {
                                        '待开始': 'pending', '进行中': 'in_progress', '已完成': 'completed',
                                        '已取消': 'cancelled', '阻塞中': 'blocked', '已延期': 'delayed'
                                    }
                                    task_data[field] = status_mapping.get(value, 'pending')
                                elif field == 'gpu_model':
                                    # 统一GPU型号格式
                                    gpu_mapping = {
                                        'p800': 'P800', 'p800-pcie': 'P800-PCIe', 'rg800': 'RG800',
                                        'r5500_g6': 'R5500_G6', 'r5500_g7': 'R5500_G7', 'r5330_g7': 'R5330_G7',
                                        'c550': 'C550', 'c500': 'C500'
                                    }
                                    task_data[field] = gpu_mapping.get(value.lower(), value)
                                elif field == 'document_output':
                                    # 处理布尔值
                                    bool_mapping = {
                                        '是': True, 'yes': True, 'true': True, '1': True,
                                        '否': False, 'no': False, 'false': False, '0': False
                                    }
                                    task_data[field] = bool_mapping.get(value.lower(), False)
                                elif field in ['start_date', 'end_date']:
                                    # 处理日期格式
                                    try:
                                        if isinstance(value, str):
                                            from datetime import datetime
                                            # 尝试多种日期格式
                                            for fmt in ['%Y-%m-%d', '%Y/%m/%d', '%m/%d/%Y', '%d/%m/%Y']:
                                                try:
                                                    parsed_date = datetime.strptime(value, fmt)
                                                    task_data[field] = parsed_date.date()
                                                    break
                                                except ValueError:
                                                    continue
                                        else:
                                            # Excel日期格式
                                            task_data[field] = value
                                    except:
                                        pass
                                else:
                                    task_data[field] = value
                    
                    tasks_data.append(task_data)
                    
                except Exception as e:
                    errors.append(f"第{row_num}行解析错误: {str(e)}")
            
            if not tasks_data:
                return json_response(
                    error="Excel文件中没有有效的任务数据" + (f"，错误信息: {'; '.join(errors)}" if errors else ""), 
                    status_code=400
                )
            
            # 批量创建或更新任务
            created_count = 0
            updated_count = 0
            
            with transaction.atomic():
                for task_data in tasks_data:
                    try:
                        # 根据模型名称和测试人员查找现有任务
                        existing_task = TestTask.objects.filter(
                            release_plan=plan,
                            model_name=task_data['model_name'],
                            tester=task_data['tester']
                        ).first()
                        
                        if existing_task:
                            # 更新现有任务
                            for field, value in task_data.items():
                                if field not in ['model_name', 'tester']:  # 不更新关键字段
                                    setattr(existing_task, field, value)
                            existing_task.save()
                            updated_count += 1
                        else:
                            # 创建新任务
                            task_data['release_plan'] = plan
                            TestTask.objects.create(**task_data)
                            created_count += 1
                    except Exception as e:
                        errors.append(f"保存任务 {task_data['model_name']}-{task_data['tester']} 失败: {str(e)}")
            
            # 构建返回消息
            message_parts = []
            if created_count > 0:
                message_parts.append(f"新增 {created_count} 条任务")
            if updated_count > 0:
                message_parts.append(f"更新 {updated_count} 条任务")
            
            result_message = "导入完成: " + ", ".join(message_parts)
            
            if errors:
                result_message += f"，但有 {len(errors)} 个错误"
                logger.warning(f"导入任务时出现错误: {errors}")
            
            return json_response({
                'message': result_message,
                'details': {
                    'created': created_count,
                    'updated': updated_count,
                    'errors': len(errors),
                    'error_messages': errors[:10] if errors else []  # 只返回前10个错误
                }
            })

        except Exception as e:
            logger.error(f"导入任务失败: {e}")
            return json_response(error=f"处理Excel文件失败: {e}", status_code=500)


@method_decorator(csrf_exempt, name='dispatch')
class RiskAlertView(View):
    """风险预警API"""

    @auth('model_storage.release_plan.view')
    def get(self, request):
        """获取风险预警列表"""
        try:
            import json as json_lib
            
            release_plan_id = request.GET.get('release_plan_id')
            
            alerts = RiskAlert.objects.all()
            if release_plan_id:
                alerts = alerts.filter(release_plan_id=release_plan_id)
            
            alerts = alerts.order_by('-created_at')
            data = []
            
            for alert in alerts:
                try:
                    affected_tasks = json_lib.loads(alert.affected_tasks) if alert.affected_tasks else []
                except:
                    affected_tasks = []
                
                alert_data = {
                    'id': alert.id,
                    'release_plan_id': alert.release_plan.id,
                    'title': alert.title,
                    'description': alert.description,
                    'risk_type': alert.risk_type,
                    'risk_type_display': alert.risk_type_display,
                    'severity': alert.severity,
                    'severity_display': alert.severity_display,
                    'status': alert.status,
                    'status_display': alert.status_display,
                    'affected_tasks': affected_tasks,
                    'mitigation_plan': alert.mitigation_plan,
                    'reporter': alert.reporter,
                    'assignee': alert.assignee,
                    'due_date': alert.due_date.strftime('%Y-%m-%d') if alert.due_date else None,
                    'resolved_at': alert.resolved_at.strftime('%Y-%m-%d %H:%M:%S') if alert.resolved_at else None,
                    'created_at': alert.created_at.strftime('%Y-%m-%d %H:%M:%S'),
                    'updated_at': alert.updated_at.strftime('%Y-%m-%d %H:%M:%S'),
                }
                data.append(alert_data)
            
            return JsonResponse({'data': data, 'error': ''})
        except Exception as e:
            return JsonResponse({'data': [], 'error': str(e)})
    
    def post(self, request):
        """创建风险预警"""
        try:
            import json as json_lib
            
            data = json.loads(request.body)
            
            release_plan = NewReleasePlan.objects.get(id=data['release_plan_id'])
            
            affected_tasks = data.get('affected_tasks', [])
            if isinstance(affected_tasks, list):
                affected_tasks_str = json_lib.dumps(affected_tasks)
            else:
                affected_tasks_str = '[]'
            
            alert = RiskAlert.objects.create(
                release_plan=release_plan,
                title=data['title'],
                description=data['description'],
                risk_type=data['risk_type'],
                severity=data['severity'],
                affected_tasks=affected_tasks_str,
                mitigation_plan=data.get('mitigation_plan', ''),
                reporter=data['reporter'],
                assignee=data.get('assignee', ''),
                due_date=data.get('due_date') if data.get('due_date') else None
            )
            
            return JsonResponse({
                'data': {
                    'id': alert.id,
                    'message': '风险预警创建成功'
                },
                'error': ''
            })
        except NewReleasePlan.DoesNotExist:
            return JsonResponse({'data': None, 'error': '发布计划不存在'}, status=404)
        except Exception as e:
            return JsonResponse({'data': None, 'error': str(e)}, status=500)
    
    def put(self, request):
        """更新风险预警"""
        try:
            from django.utils import timezone
            import json as json_lib
            
            data = json.loads(request.body)
            
            alert = RiskAlert.objects.get(id=data['id'])
            
            alert.title = data.get('title', alert.title)
            alert.description = data.get('description', alert.description)
            alert.risk_type = data.get('risk_type', alert.risk_type)
            alert.severity = data.get('severity', alert.severity)
            alert.status = data.get('status', alert.status)
            alert.mitigation_plan = data.get('mitigation_plan', alert.mitigation_plan)
            alert.assignee = data.get('assignee', alert.assignee)
            
            if 'affected_tasks' in data:
                affected_tasks = data['affected_tasks']
                if isinstance(affected_tasks, list):
                    alert.affected_tasks = json_lib.dumps(affected_tasks)
                else:
                    alert.affected_tasks = '[]'
            
            if 'due_date' in data:
                alert.due_date = data['due_date'] if data['due_date'] else None
            
            # 如果状态改为已解决，设置解决时间
            if data.get('status') == 'resolved' and alert.status != 'resolved':
                alert.resolved_at = timezone.now()
            
            alert.save()
            
            return JsonResponse({
                'data': {
                    'id': alert.id,
                    'message': '风险预警更新成功'
                },
                'error': ''
            })
        except RiskAlert.DoesNotExist:
            return JsonResponse({'data': None, 'error': '风险预警不存在'}, status=404)
        except Exception as e:
            return JsonResponse({'data': None, 'error': str(e)}, status=500)
    
    def delete(self, request):
        """删除风险预警"""
        try:
            data = json.loads(request.body)
            
            alert = RiskAlert.objects.get(id=data['id'])
            alert.delete()
            
            return JsonResponse({
                'data': {'message': '风险预警删除成功'},
                'error': ''
            })
        except RiskAlert.DoesNotExist:
            return JsonResponse({'data': None, 'error': '风险预警不存在'}, status=404)
        except Exception as e:
            return JsonResponse({'data': None, 'error': str(e)}, status=500)


@method_decorator(csrf_exempt, name='dispatch')
class GanttDataView(View):
    """甘特图数据API"""
    
    def get(self, request):
        """获取甘特图数据"""
        try:
            import json as json_lib
            from collections import defaultdict
            
            release_plan_id = request.GET.get('release_plan_id')
            if not release_plan_id:
                return JsonResponse({'data': None, 'error': '缺少release_plan_id参数'}, status=400)
            
            # 获取发布计划
            release_plan = NewReleasePlan.objects.get(id=release_plan_id)
            
            # 获取任务按测试人员分组
            tasks = TestTask.objects.filter(release_plan=release_plan).order_by('start_date')
            tasks_by_tester = defaultdict(list)
            
            for task in tasks:
                # 解析测试步骤完成状态
                try:
                    step_completion_status = json_lib.loads(task.step_completion_status) if task.step_completion_status else {}
                except (json_lib.JSONDecodeError, TypeError):
                    step_completion_status = {}

                task_data = {
                    'id': task.id,
                    'model_name': task.model_name,
                    'model_type_display': task.model_type_display,
                    'gpu_model_display': task.gpu_model_display,
                    'start_date': task.start_date.strftime('%Y-%m-%d') if task.start_date else None,
                    'end_date': task.end_date.strftime('%Y-%m-%d') if task.end_date else None,
                    'priority_display': task.priority_display,
                    'test_status': task.test_status,
                    'test_status_display': task.test_status_display,
                    'provider_status_display': task.provider_status_display,
                    'progress': task.progress,
                    'notes': task.notes,
                    'color': task.color,
                    'document_output': task.document_output,  # 添加资料输出字段
                    'document_output_display': task.document_output_display,  # 添加资料输出显示字段
                    'test_case_set_id': task.test_case_set_id,  # 添加关联用例集ID
                    'test_case_set_info': task.get_test_case_set_info(),  # 添加关联用例集信息
                    'step_completion_status': step_completion_status  # 添加测试步骤完成状态
                }
                tasks_by_tester[task.tester].append(task_data)
            
            # 获取风险预警
            risk_alerts = RiskAlert.objects.filter(release_plan=release_plan, status='active')
            alerts_data = []
            
            for alert in risk_alerts:
                try:
                    affected_tasks = json_lib.loads(alert.affected_tasks) if alert.affected_tasks else []
                except:
                    affected_tasks = []
                
                alerts_data.append({
                    'title': alert.title,
                    'severity': alert.severity,
                    'severity_display': alert.severity_display,
                    'affected_tasks': affected_tasks
                })
            
            # 构建响应数据
            gantt_data = {
                'release_plan': {
                    'id': release_plan.id,
                    'name': release_plan.name,
                    'start_date': release_plan.start_date.strftime('%Y-%m-%d'),
                    'end_date': release_plan.end_date.strftime('%Y-%m-%d'),
                    'status': release_plan.status
                },
                'tasks_by_tester': dict(tasks_by_tester),
                'risk_alerts': alerts_data
            }
            
            return JsonResponse({'data': gantt_data, 'error': ''})
            
        except NewReleasePlan.DoesNotExist:
            return JsonResponse({'data': None, 'error': '发布计划不存在'}, status=404)
        except Exception as e:
            return JsonResponse({'data': None, 'error': str(e)})


@method_decorator(csrf_exempt, name='dispatch')
class StatisticsView(View):
    """统计数据API"""
    
    def get(self, request):
        """获取统计数据"""
        try:
            from collections import defaultdict
            
            release_plan_id = request.GET.get('release_plan_id')
            if not release_plan_id:
                return JsonResponse({'data': None, 'error': '缺少release_plan_id参数'}, status=400)
            
            # 获取发布计划
            release_plan = NewReleasePlan.objects.get(id=release_plan_id)
            
            # 获取任务统计
            tasks = TestTask.objects.filter(release_plan=release_plan)
            total_tasks = tasks.count()
            completed_tasks = tasks.filter(test_status='completed').count()
            running_tasks = tasks.filter(test_status='running').count()
            pending_tasks = tasks.filter(test_status='pending').count()
            
            # 计算完成率
            completion_rate = completed_tasks / total_tasks if total_tasks > 0 else 0
            
            # 计算平均进度
            avg_progress = 0
            if total_tasks > 0:
                total_progress = sum(task.progress for task in tasks)
                avg_progress = total_progress / total_tasks
            
            # 高优先级任务统计
            high_priority_tasks = tasks.filter(priority__in=['high', 'urgent']).count()
            
            # 风险统计
            risk_alerts = RiskAlert.objects.filter(release_plan=release_plan, status='active')
            risk_count = {
                'high': risk_alerts.filter(severity='high').count(),
                'medium': risk_alerts.filter(severity='medium').count(),
                'low': risk_alerts.filter(severity='low').count()
            }
            
            # 测试人员工作量统计
            tester_workload = defaultdict(int)
            for task in tasks:
                tester_workload[task.tester] += 1
            
            # 构建统计数据
            statistics = {
                'total_tasks': total_tasks,
                'completed_tasks': completed_tasks,
                'running_tasks': running_tasks,
                'pending_tasks': pending_tasks,
                'completion_rate': round(completion_rate, 3),
                'average_progress': round(avg_progress, 1),
                'high_priority_tasks': high_priority_tasks,
                'risk_count': risk_count,
                'tester_workload': dict(tester_workload)
            }
            
            return JsonResponse({'data': statistics, 'error': ''})
            
        except NewReleasePlan.DoesNotExist:
            return JsonResponse({'data': None, 'error': '发布计划不存在'}, status=404)
        except Exception as e:
            return JsonResponse({'data': None, 'error': str(e)})


@method_decorator(csrf_exempt, name='dispatch')
class DashboardStatisticsView(View):
    """监控概览统计数据API"""

    @auth('model_storage.storage.view')
    def get(self, request):
        """获取监控概览统计数据"""
        try:
            from django.db.models import Count, Q, Avg
            from collections import defaultdict
            import datetime

            # 1. 总体概况统计
            total_plans = NewReleasePlan.objects.count()
            running_plans = NewReleasePlan.objects.filter(status='running').count()
            completed_plans = NewReleasePlan.objects.filter(status='completed').count()

            total_tasks = TestTask.objects.count()
            completed_tasks = TestTask.objects.filter(test_status='completed').count()
            completion_rate = round(completed_tasks / total_tasks * 100, 1) if total_tasks > 0 else 0
            avg_progress = TestTask.objects.aggregate(avg_progress=Avg('progress'))['avg_progress'] or 0
            avg_progress = round(avg_progress, 1)

            # 本月统计
            current_month = datetime.datetime.now().replace(day=1)
            monthly_new_plans = NewReleasePlan.objects.filter(created_at__gte=current_month).count()
            monthly_completed_plans = NewReleasePlan.objects.filter(
                status='completed',
                updated_at__gte=current_month
            ).count()

            # 2. 人员效率统计
            tester_stats = TestTask.objects.values('tester').annotate(
                total_tasks=Count('id'),
                completed_tasks=Count('id', filter=Q(test_status='completed')),
                avg_progress=Avg('progress')
            ).order_by('-completed_tasks')[:10]

            # 3. GPU资源统计
            gpu_devices = GPUDevice.objects.values('vendor').annotate(count=Count('id'))
            gpu_total = GPUDevice.objects.count()

            # GPU使用统计（基于测试任务中的GPU型号）
            gpu_usage = TestTask.objects.exclude(gpu_model__isnull=True).exclude(gpu_model='').values('gpu_model').annotate(
                usage_count=Count('id')
            ).order_by('-usage_count')[:10]

            # 4. 风险预警统计
            high_risk_tasks = TestTask.objects.filter(priority='p1').count()
            delayed_tasks = TestTask.objects.filter(test_status='delayed').count()
            blocked_tasks = TestTask.objects.filter(test_status='blocked').count()

            # 5. 时间趋势分析（近30天）
            thirty_days_ago = datetime.datetime.now() - datetime.timedelta(days=30)

            # 按日期统计任务完成情况
            daily_completions = []
            for i in range(30):
                date = thirty_days_ago + datetime.timedelta(days=i)
                date_str = date.strftime('%Y-%m-%d')
                completed_count = TestTask.objects.filter(
                    test_status='completed',
                    updated_at__date=date.date()
                ).count()
                daily_completions.append({
                    'date': date_str,
                    'completed': completed_count
                })

            # 6. 测试用例集统计
            from apps.exec.models import TestCaseSet
            total_test_case_sets = TestCaseSet.objects.count()

            # 统计测试用例集的使用情况
            test_case_usage = TestTask.objects.exclude(test_case_set_id__isnull=True).values(
                'test_case_set_id'
            ).annotate(usage_count=Count('id')).order_by('-usage_count')[:5]

            # 7. 优先级分布
            priority_distribution = TestTask.objects.values('priority').annotate(
                count=Count('id')
            )

            # 8. 状态分布
            status_distribution = TestTask.objects.values('test_status').annotate(
                count=Count('id')
            )

            statistics = {
                # 总体概况
                'overview': {
                    'total_plans': total_plans,
                    'running_plans': running_plans,
                    'completed_plans': completed_plans,
                    'total_tasks': total_tasks,
                    'completed_tasks': completed_tasks,
                    'completion_rate': completion_rate,
                    'avg_progress': avg_progress,
                    'monthly_new_plans': monthly_new_plans,
                    'monthly_completed_plans': monthly_completed_plans,
                },

                # 人员效率
                'personnel': {
                    'tester_stats': list(tester_stats),
                    'total_testers': len(set(TestTask.objects.values_list('tester', flat=True)))
                },

                # GPU资源
                'gpu_resources': {
                    'total_devices': gpu_total,
                    'vendor_distribution': list(gpu_devices),
                    'popular_models': list(gpu_usage)
                },

                # 风险预警
                'risk_alerts': {
                    'high_priority_tasks': high_risk_tasks,
                    'delayed_tasks': delayed_tasks,
                    'blocked_tasks': blocked_tasks,
                    'total_risk_tasks': high_risk_tasks + delayed_tasks + blocked_tasks
                },

                # 时间趋势
                'trends': {
                    'daily_completions': daily_completions,
                    'completion_trend': sum([item['completed'] for item in daily_completions[-7:]]) # 最近7天完成数
                },

                # 测试用例集
                'test_cases': {
                    'total_sets': total_test_case_sets,
                    'popular_sets': list(test_case_usage)
                },

                # 分布统计
                'distributions': {
                    'priority': list(priority_distribution),
                    'status': list(status_distribution)
                }
            }

            return JsonResponse({'data': statistics, 'error': ''})

        except Exception as e:
            logger.error(f"获取监控概览统计数据失败: {e}")
            return JsonResponse({'data': None, 'error': str(e)}, status=500)


@method_decorator(csrf_exempt, name='dispatch')
class WeeklyReportView(View):
    """周报导出API - HTML格式"""

    @auth('model_storage.storage.view')
    def get(self, request):
        """生成HTML格式的周报"""
        try:
            from datetime import timedelta
            from django.utils import timezone
            import urllib.parse
            from collections import defaultdict

            print(f"[DEBUG] 开始生成HTML周报")

            # 获取发布计划ID参数
            release_plan_id = request.GET.get('release_plan_id')

            # 获取本周时间范围
            today = timezone.now().date()
            week_start = today - timedelta(days=today.weekday())  # 本周一
            week_end = week_start + timedelta(days=6)  # 本周日

            print(f"[DEBUG] 本周时间范围: {week_start} ~ {week_end}")
            print(f"[DEBUG] 发布计划ID: {release_plan_id}")

            # 构建查询条件
            query_conditions = Q(
                # 任务在本周内有活动：开始时间在本周内，或结束时间在本周内，或跨越本周
                Q(start_date__lte=week_end, end_date__gte=week_start) |
                # 或者任务状态不是已取消
                Q(test_status__in=['pending', 'in_progress', 'completed', 'blocked', 'delayed'])
            )

            # 如果指定了发布计划ID，只获取该计划的任务
            if release_plan_id:
                try:
                    release_plan_id = int(release_plan_id)
                    query_conditions &= Q(release_plan__id=release_plan_id)
                    print(f"[DEBUG] 过滤发布计划ID: {release_plan_id}")
                except ValueError:
                    print(f"[DEBUG] 无效的发布计划ID: {release_plan_id}")
                    return json_response(error="无效的发布计划ID", status=400)

            tasks = TestTask.objects.filter(query_conditions).order_by('tester', 'start_date', 'model_name')

            print(f"[DEBUG] 查询到 {tasks.count()} 个相关测试任务")

            # 生成HTML报告
            html_content = self.generate_html_report(tasks, week_start, week_end, release_plan_id)

            print(f"[DEBUG] HTML报告生成成功，长度: {len(html_content)} 字符")

            # 创建响应
            response = HttpResponse(html_content, content_type='text/html; charset=utf-8')

            # 生成文件名
            plan_suffix = f"_计划{release_plan_id}" if release_plan_id else ""
            filename = f'模型测试周报_{week_start.strftime("%Y%m%d")}-{week_end.strftime("%Y%m%d")}{plan_suffix}.html'
            encoded_filename = urllib.parse.quote(filename)
            response['Content-Disposition'] = f'attachment; filename="{filename}"; filename*=UTF-8\'\'{encoded_filename}'

            # 添加CORS头
            response['Access-Control-Allow-Origin'] = '*'
            response['Access-Control-Allow-Methods'] = 'GET, OPTIONS'
            response['Access-Control-Allow-Headers'] = 'Origin, Content-Type, Accept, X-Token'

            print(f"[DEBUG] 响应头设置成功，文件名: {filename}")

            return response
            
        except Exception as e:
            print(f"[DEBUG ERROR] 导出周报失败: {e}")
            import traceback
            traceback.print_exc()
            logger.error(f"导出周报失败: {e}")
            return json_response(error=f"导出周报失败: {e}", status=500)

    def identify_risk_tasks(self, tasks):
        """识别风险任务"""
        from datetime import datetime, timedelta
        from django.utils import timezone

        risk_tasks = []
        current_date = timezone.now().date()

        for task in tasks:
            risk_info = {
                'task': task,
                'risk_type': '',
                'risk_level': 'low',
                'risk_description': ''
            }

            # 检查是否超期
            if task.end_date and task.end_date < current_date and task.test_status != 'completed':
                risk_info['risk_type'] = '任务超期'
                risk_info['risk_level'] = 'high'
                overdue_days = (current_date - task.end_date).days
                risk_info['risk_description'] = f'已超期 {overdue_days} 天'
                risk_tasks.append(risk_info)
                continue

            # 检查是否即将到期（3天内）
            if task.end_date and task.test_status != 'completed':
                days_to_deadline = (task.end_date - current_date).days
                if 0 <= days_to_deadline <= 3:
                    risk_info['risk_type'] = '紧急截止'
                    risk_info['risk_level'] = 'medium'
                    risk_info['risk_description'] = f'还有 {days_to_deadline} 天到期'
                    risk_tasks.append(risk_info)
                    continue

            # 检查进度异常（进度低于预期）
            if task.start_date and task.end_date and task.test_status == 'in_progress':
                total_days = (task.end_date - task.start_date).days
                elapsed_days = (current_date - task.start_date).days
                if total_days > 0:
                    expected_progress = min(100, (elapsed_days / total_days) * 100)
                    if task.progress < expected_progress - 20:  # 进度落后20%以上
                        risk_info['risk_type'] = '进度滞后'
                        risk_info['risk_level'] = 'medium'
                        risk_info['risk_description'] = f'进度 {task.progress}%，预期 {expected_progress:.0f}%'
                        risk_tasks.append(risk_info)
                        continue

            # 检查阻塞状态
            if task.test_status == 'blocked':
                risk_info['risk_type'] = '任务阻塞'
                risk_info['risk_level'] = 'high'
                risk_info['risk_description'] = '任务被阻塞，需要处理'
                risk_tasks.append(risk_info)
                continue

            # 检查高优先级任务未开始
            if task.priority in ['p1', 'high'] and task.test_status == 'pending':
                risk_info['risk_type'] = '高优先级未开始'
                risk_info['risk_level'] = 'medium'
                risk_info['risk_description'] = 'P1高优先级任务尚未开始'
                risk_tasks.append(risk_info)

        return risk_tasks

    def generate_html_report(self, tasks, week_start, week_end, release_plan_id=None):
        """生成HTML报告内容"""
        from collections import defaultdict
        from django.utils import timezone
        
        # 按优先级排序任务（P1高 > P2中 > P3低）
        priority_order = {'p1': 1, 'high': 1, 'p2': 2, 'medium': 2, 'p3': 3, 'low': 3}
        sorted_tasks = sorted(tasks, key=lambda t: (
            priority_order.get(t.priority, 4),  # 优先级排序
            t.tester,  # 然后按测试人员
            t.start_date or timezone.now().date()  # 最后按开始时间
        ))
        
        # 数据统计
        total_tasks = len(sorted_tasks)
        completed_tasks = len([t for t in sorted_tasks if t.test_status == 'completed'])
        in_progress_tasks = len([t for t in sorted_tasks if t.test_status == 'in_progress'])
        blocked_tasks = len([t for t in sorted_tasks if t.test_status == 'blocked'])
        pending_tasks = len([t for t in sorted_tasks if t.test_status == 'pending'])
        document_output_tasks = len([t for t in sorted_tasks if t.document_output])
        
        # 避免除零错误
        if total_tasks == 0:
            completion_rate = 0
        else:
            completion_rate = (completed_tasks / total_tasks * 100)

        # 获取当前月份名称
        current_month = week_start.strftime('%m')
        month_names = {
            '01': '一月', '02': '二月', '03': '三月', '04': '四月',
            '05': '五月', '06': '六月', '07': '七月', '08': '八月',
            '09': '九月', '10': '十月', '11': '十一月', '12': '十二月'
        }
        month_name = month_names.get(current_month, f'{current_month}月')
        
        # 按测试人员分组并添加详细信息
        tester_stats = defaultdict(lambda: {
            'total': 0, 'completed': 0, 'in_progress': 0, 'blocked': 0, 'pending': 0,
            'document_output': 0, 'avg_progress': 0, 'tasks': [],
            'current_models': [], 'gpu_devices': set(), 'priority_distribution': {'p1': 0, 'p2': 0, 'p3': 0}
        })
        
        for task in sorted_tasks:
            stats = tester_stats[task.tester]
            stats['total'] += 1
            stats['tasks'].append(task)
            
            # 统计任务状态
            if task.test_status == 'completed':
                stats['completed'] += 1
            elif task.test_status == 'in_progress':
                stats['in_progress'] += 1
                # 记录当前进行中的模型
                stats['current_models'].append(f"{task.model_name}")
            elif task.test_status == 'blocked':
                stats['blocked'] += 1
            elif task.test_status == 'pending':
                stats['pending'] += 1
                
            if task.document_output:
                stats['document_output'] += 1
                
            # 记录GPU设备
            stats['gpu_devices'].add(task.gpu_model_display)
            
            # 统计优先级分布
            priority = task.priority.lower() if hasattr(task, 'priority') and task.priority else 'p2'
            if priority in ['p1', 'high']:
                stats['priority_distribution']['p1'] += 1
            elif priority in ['p2', 'medium']:
                stats['priority_distribution']['p2'] += 1
            elif priority in ['p3', 'low']:
                stats['priority_distribution']['p3'] += 1
        
        # 计算平均进度
        for tester, stats in tester_stats.items():
            if stats['total'] > 0:
                stats['avg_progress'] = sum(t.progress for t in stats['tasks']) / stats['total']
            # 转换GPU设备集合为列表
            stats['gpu_devices'] = list(stats['gpu_devices'])
        
        # 识别风险任务
        risk_tasks = self.identify_risk_tasks(sorted_tasks)

        # 按GPU型号统计
        gpu_stats = defaultdict(lambda: {'total': 0, 'completed': 0, 'in_progress': 0})
        for task in sorted_tasks:
            gpu_stats[task.gpu_model_display]['total'] += 1
            if task.test_status == 'completed':
                gpu_stats[task.gpu_model_display]['completed'] += 1
            elif task.test_status == 'in_progress':
                gpu_stats[task.gpu_model_display]['in_progress'] += 1
        
        # 生成HTML
        html = f"""
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>模型测试周报 - {week_start.strftime('%Y年%m月%d日')} ~ {week_end.strftime('%Y年%m月%d日')}</title>
    <style>
        * {{
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }}
        
        body {{
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }}
        
        .container {{
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }}
        
        .header {{
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 40px;
            text-align: center;
        }}
        
        .header h1 {{
            font-size: 2.5em;
            margin-bottom: 10px;
            font-weight: 300;
        }}
        
        .header .period {{
            font-size: 1.2em;
            opacity: 0.9;
        }}
        
        .content {{
            padding: 40px;
        }}
        
        .section {{
            margin-bottom: 40px;
        }}
        
        .section-title {{
            font-size: 1.8em;
            color: #333;
            margin-bottom: 20px;
            border-bottom: 3px solid #667eea;
            padding-bottom: 10px;
        }}
        
        .stats-grid {{
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }}
        
        .stat-card {{
            background: #f8f9fa;
            border-radius: 15px;
            padding: 25px;
            text-align: center;
            border-left: 5px solid #667eea;
            transition: transform 0.3s ease;
        }}
        
        .stat-card:hover {{
            transform: translateY(-5px);
        }}
        
        .stat-card.completed {{
            border-left-color: #28a745;
        }}
        
        .stat-card.in-progress {{
            border-left-color: #ffc107;
        }}
        
        .stat-card.blocked {{
            border-left-color: #dc3545;
        }}
        
        .stat-card.document {{
            border-left-color: #17a2b8;
        }}
        
        .stat-number {{
            font-size: 2.5em;
            font-weight: bold;
            color: #333;
            margin-bottom: 5px;
        }}
        
        .stat-label {{
            font-size: 1.1em;
            color: #666;
        }}
        
        .progress-bar {{
            width: 100%;
            height: 8px;
            background: #e9ecef;
            border-radius: 4px;
            overflow: hidden;
            margin: 10px 0;
        }}

        /* 主要进度条样式 - 更大更显眼 */
        .main-progress-bar {{
            width: 100%;
            height: 24px;
            background: #e9ecef;
            border-radius: 12px;
            overflow: hidden;
            margin: 20px 0;
            box-shadow: inset 0 2px 4px rgba(0,0,0,0.1);
        }}

        .main-progress-fill {{
            height: 100%;
            background: linear-gradient(90deg, #667eea, #764ba2);
            transition: width 0.5s ease;
            border-radius: 12px;
            position: relative;
        }}

        .main-progress-fill::after {{
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
            animation: shimmer 2s infinite;
        }}

        @keyframes shimmer {{
            0% {{ transform: translateX(-100%); }}
            100% {{ transform: translateX(100%); }}
        }}

        .progress-fill {{
            height: 100%;
            background: linear-gradient(90deg, #28a745, #20c997);
            transition: width 0.3s ease;
        }}

        /* 月份进度显示区域 */
        .month-progress-section {{
            text-align: center;
            margin: 30px 0;
            padding: 30px;
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border-radius: 20px;
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
        }}

        .month-progress-title {{
            font-size: 2.2em;
            font-weight: bold;
            color: #333;
            margin-bottom: 15px;
            background: linear-gradient(135deg, #667eea, #764ba2);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }}

        .progress-percentage {{
            font-size: 3em;
            font-weight: bold;
            color: #667eea;
            margin: 10px 0;
        }}

        /* 风险任务表格样式 */
        .risk-section {{
            margin: 30px 0;
            padding: 25px;
            background: #fff;
            border-radius: 15px;
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
        }}

        .risk-table {{
            width: 100%;
            border-collapse: collapse;
            margin-top: 15px;
            font-size: 0.9em;
        }}

        .risk-table th {{
            background: linear-gradient(135deg, #ff6b6b, #ee5a52);
            color: white;
            padding: 12px 8px;
            text-align: left;
            font-weight: bold;
            border: none;
        }}

        .risk-table th:first-child {{
            border-radius: 8px 0 0 0;
        }}

        .risk-table th:last-child {{
            border-radius: 0 8px 0 0;
        }}

        .risk-table td {{
            padding: 10px 8px;
            border-bottom: 1px solid #e9ecef;
            vertical-align: middle;
        }}

        .risk-table tr:hover {{
            background: #f8f9fa;
        }}

        .risk-level-high {{
            background: #fee;
            border-left: 4px solid #dc3545;
        }}

        .risk-level-medium {{
            background: #fff8e1;
            border-left: 4px solid #ffc107;
        }}

        .risk-level-low {{
            background: #f0f8ff;
            border-left: 4px solid #17a2b8;
        }}

        .risk-badge {{
            display: inline-block;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 0.8em;
            font-weight: bold;
            text-align: center;
            min-width: 60px;
        }}

        .risk-badge.high {{
            background: #dc3545;
            color: white;
        }}

        .risk-badge.medium {{
            background: #ffc107;
            color: #333;
        }}

        .risk-badge.low {{
            background: #17a2b8;
            color: white;
        }}
        
        .tester-card {{
            background: #fff;
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 20px;
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
            border-left: 5px solid #667eea;
        }}
        
        .tester-header {{
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
        }}
        
        .tester-name {{
            font-size: 1.4em;
            font-weight: bold;
            color: #333;
        }}
        
        .tester-stats {{
            display: flex;
            gap: 15px;
            font-size: 0.9em;
            flex-wrap: wrap;
        }}
        
        .tester-stat {{
            background: #f8f9fa;
            padding: 5px 10px;
            border-radius: 20px;
            color: #666;
        }}
        
        .tester-details {{
            background: #f8f9fa;
            border-radius: 10px;
            padding: 15px;
            margin-top: 15px;
        }}
        
        .detail-row {{
            display: flex;
            align-items: center;
            margin-bottom: 8px;
            font-size: 0.9em;
        }}
        
        .detail-label {{
            font-weight: bold;
            color: #555;
            width: 100px;
            flex-shrink: 0;
        }}
        
        .detail-content {{
            color: #666;
            flex: 1;
        }}
        
        .model-tag {{
            display: inline-block;
            background: #e3f2fd;
            color: #1976d2;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 0.8em;
            margin: 2px;
        }}
        
        .gpu-tag {{
            display: inline-block;
            background: #f3e5f5;
            color: #7b1fa2;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 0.8em;
            margin: 2px;
        }}
        
        .priority-badge {{
            display: inline-block;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 0.8em;
            margin: 2px;
            font-weight: bold;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }}
        
        .priority-p1 {{
            background: linear-gradient(135deg, #ff6b6b, #ff5252);
            color: white;
        }}
        
        .priority-p2 {{
            background: linear-gradient(135deg, #ffd93d, #ffb300);
            color: #333;
        }}
        
        .priority-p3 {{
            background: linear-gradient(135deg, #6bcf7f, #4caf50);
            color: white;
        }}
        
        .task-table {{
            width: 100%;
            border-collapse: collapse;
            margin-top: 15px;
            font-size: 0.9em;
        }}
        
        .task-table th,
        .task-table td {{
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #eee;
        }}
        
        .task-table th {{
            background: #f8f9fa;
            font-weight: bold;
            color: #333;
            position: sticky;
            top: 0;
            z-index: 10;
        }}
        
        .status-badge {{
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 0.8em;
            font-weight: bold;
            color: white;
        }}
        
        .status-completed {{
            background: #28a745;
        }}
        
        .status-in-progress {{
            background: #ffc107;
            color: #333;
        }}
        
        .status-blocked {{
            background: #dc3545;
        }}
        
        .status-pending {{
            background: #6c757d;
        }}
        
        .status-delayed {{
            background: #fd7e14;
        }}
        
        .priority-indicator {{
            width: 16px;
            height: 16px;
            border-radius: 50%;
            display: inline-block;
            margin-right: 8px;
        }}
        
        .priority-high {{
            background: #dc3545;
        }}
        
        .priority-medium {{
            background: #ffc107;
        }}
        
        .priority-low {{
            background: #28a745;
        }}
        
        .notes-cell {{
            max-width: 200px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }}
        
        .notes-tooltip {{
            position: relative;
            cursor: help;
        }}
        
        .notes-tooltip:hover::after {{
            content: attr(data-notes);
            position: absolute;
            background: #333;
            color: white;
            padding: 8px;
            border-radius: 4px;
            font-size: 0.8em;
            white-space: normal;
            z-index: 1000;
            bottom: 100%;
            left: 50%;
            transform: translateX(-50%);
            max-width: 300px;
            word-wrap: break-word;
        }}
        
        .gpu-stats {{
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
        }}
        
        .gpu-card {{
            background: #f8f9fa;
            border-radius: 15px;
            padding: 20px;
            text-align: center;
        }}
        
        .gpu-name {{
            font-size: 1.2em;
            font-weight: bold;
            color: #333;
            margin-bottom: 10px;
        }}
        
        .footer {{
            background: #f8f9fa;
            padding: 20px 40px;
            text-align: center;
            color: #666;
            font-size: 0.9em;
        }}
        
        .highlight {{
            background: linear-gradient(120deg, #a8edea 0%, #fed6e3 100%);
            padding: 2px 6px;
            border-radius: 4px;
        }}
        
        @media (max-width: 768px) {{
            .container {{
                margin: 10px;
                border-radius: 10px;
            }}
            
            .header {{
                padding: 20px;
            }}
            
            .header h1 {{
                font-size: 2em;
            }}
            
            .content {{
                padding: 20px;
            }}
            
            .stats-grid {{
                grid-template-columns: 1fr;
            }}
            
            .tester-header {{
                flex-direction: column;
                align-items: flex-start;
                gap: 10px;
            }}
            
            .tester-stats {{
                flex-wrap: wrap;
            }}
            
            .task-table {{
                font-size: 0.8em;
            }}
            
            .task-table th,
            .task-table td {{
                padding: 8px;
            }}
        }}
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 模型测试周报</h1>
            <div class="period">{week_start.strftime('%Y年%m月%d日')} ~ {week_end.strftime('%Y年%m月%d日')}</div>
        </div>
        
        <div class="content">
            <!-- 总体统计 -->
            <div class="section">
                <h2 class="section-title">📊 总体统计</h2>
                <div class="stats-grid">
                    <div class="stat-card">
                        <div class="stat-number">{total_tasks}</div>
                        <div class="stat-label">总任务数</div>
                    </div>
                    <div class="stat-card completed">
                        <div class="stat-number">{completed_tasks}</div>
                        <div class="stat-label">已完成</div>
                    </div>
                    <div class="stat-card in-progress">
                        <div class="stat-number">{in_progress_tasks}</div>
                        <div class="stat-label">进行中</div>
                    </div>
                    <div class="stat-card blocked">
                        <div class="stat-number">{blocked_tasks}</div>
                        <div class="stat-label">阻塞</div>
                    </div>
                    <div class="stat-card document">
                        <div class="stat-number">{document_output_tasks}</div>
                        <div class="stat-label">资料输出</div>
                    </div>
                </div>
                
                <!-- 月份进度显示 -->
                <div class="month-progress-section">
                    <div class="month-progress-title">{month_name}整体进度</div>
                    <div class="progress-percentage">{completion_rate:.1f}%</div>
                    <div class="main-progress-bar">
                        <div class="main-progress-fill" style="width: {completion_rate:.1f}%"></div>
                    </div>
                    <div style="margin-top: 15px; color: #666; font-size: 1.1em;">
                        已完成 {completed_tasks} / {total_tasks} 个任务
                    </div>
                </div>

                <!-- 风险任务模块 -->
                {self.generate_risk_section(risk_tasks)}
            </div>

            <!-- 按测试人员统计 -->
            <div class="section">
                <h2 class="section-title">👥 按测试人员统计</h2>
                {self.generate_tester_section(tester_stats)}
            </div>
            
            <!-- GPU型号统计 -->
            <div class="section">
                <h2 class="section-title">💻 GPU型号统计</h2>
                <div class="gpu-stats">
                    {self.generate_gpu_section(gpu_stats, tasks)}
                </div>
            </div>
            
            <!-- 详细任务列表 -->
            <div class="section">
                <h2 class="section-title">📋 详细任务列表（按优先级排序）</h2>
                {self.generate_task_table(sorted_tasks)}
            </div>
        </div>
        
        <div class="footer">
            <p>📅 报告生成时间: {timezone.now().strftime('%Y年%m月%d日 %H:%M:%S')}</p>
            <p>🏢 Spug模型测试管理系统</p>
        </div>
    </div>
</body>
</html>
        """
        
        return html
    
    def generate_tester_section(self, tester_stats):
        """生成测试人员统计部分"""
        html = ""
        for tester, stats in tester_stats.items():
            completion_rate = (stats['completed'] / stats['total'] * 100) if stats['total'] > 0 else 0
            
            # 生成当前进行中的模型标签
            current_models_html = ""
            if stats['current_models']:
                for model in stats['current_models']:
                    current_models_html += f'<span class="model-tag">{model}</span>'
            else:
                current_models_html = '<span style="color: #999;">暂无进行中任务</span>'
            
            # 生成GPU设备标签
            gpu_devices_html = ""
            if stats['gpu_devices']:
                for gpu in stats['gpu_devices']:
                    gpu_devices_html += f'<span class="gpu-tag">{gpu}</span>'
            else:
                gpu_devices_html = '<span style="color: #999;">暂无分配设备</span>'
            
            # 生成优先级分布
            priority_dist = stats['priority_distribution']
            priority_html = ""
            if priority_dist['p1'] > 0:
                priority_html += f'<span class="priority-badge priority-p1">P1高: {priority_dist["p1"]}</span>'
            if priority_dist['p2'] > 0:
                priority_html += f'<span class="priority-badge priority-p2">P2中: {priority_dist["p2"]}</span>'
            if priority_dist['p3'] > 0:
                priority_html += f'<span class="priority-badge priority-p3">P3低: {priority_dist["p3"]}</span>'
            
            if not priority_html:
                priority_html = '<span style="color: #999;">暂无任务</span>'
            
            # 生成待开始任务提醒
            pending_reminder = ""
            if stats['pending'] > 0:
                pending_reminder = f'<div style="color: #ff9800; font-size: 0.85em; margin-top: 5px;">⚠️ 有 {stats["pending"]} 个任务待开始</div>'
            
            html += f"""
            <div class="tester-card">
                <div class="tester-header">
                    <div class="tester-name">👤 {tester}</div>
                    <div class="tester-stats">
                        <span class="tester-stat">总计: {stats['total']}</span>
                        <span class="tester-stat">完成: {stats['completed']}</span>
                        <span class="tester-stat">进行中: {stats['in_progress']}</span>
                        <span class="tester-stat">阻塞: {stats['blocked']}</span>
                        <span class="tester-stat">资料输出: {stats['document_output']}</span>
                    </div>
                </div>
                <div style="display: flex; align-items: center; gap: 15px;">
                    <div style="flex: 1;">
                        <div style="font-size: 0.9em; color: #666; margin-bottom: 5px;">
                            完成率: {completion_rate:.1f}% | 平均进度: {stats['avg_progress']:.1f}%
                        </div>
                        <div class="progress-bar">
                            <div class="progress-fill" style="width: {completion_rate:.1f}%"></div>
                        </div>
                        {pending_reminder}
                    </div>
                </div>
                
                <!-- 测试人员详细信息 -->
                <div class="tester-details" style="display: flex; align-items: flex-start; gap: 24px; flex-wrap: wrap;">
                    <div class="detail-item">
                        <span class="detail-label">📋 当前模型:</span>
                        <span class="detail-content">{current_models_html}</span>
                    </div>
                    <div class="detail-item">
                        <span class="detail-label">🖥️ 使用GPU:</span>
                        <span class="detail-content">{gpu_devices_html}</span>
                    </div>
                    <div class="detail-item">
                        <span class="detail-label">🎯 优先级分布:</span>
                        <span class="detail-content">{priority_html}</span>
                    </div>
                </div>
            </div>
            """
        
        return html
    
    def generate_gpu_section(self, gpu_stats, tasks_queryset=None):
        """生成GPU统计部分"""
        html = ""

        # 获取GPU管理中的模型总计（包括自动关联和手动录入的模型）
        gpu_model_counts = {}
        try:
            from .models import GPUDevice
            gpus = GPUDevice.objects.all()
            for gpu in gpus:
                if gpu.name in gpu_stats:  # 只统计当前周报中出现的GPU
                    # 使用GPU管理页面显示的模型总数（自动关联 + 手动录入）
                    auto_models = gpu.tested_models_auto
                    manual_models_list = gpu.get_manual_models_list()
                    all_models = sorted(list(set(auto_models) | set(manual_models_list)))
                    gpu_model_counts[gpu.name] = len(all_models)
        except Exception as e:
            print(f"[DEBUG] 获取GPU模型总计失败: {e}")

        for gpu_model, stats in gpu_stats.items():
            completion_rate = (stats['completed'] / stats['total'] * 100) if stats['total'] > 0 else 0

            # 获取该GPU的模型总计，如果没有找到则使用任务总计作为后备
            gpu_total_models = gpu_model_counts.get(gpu_model, stats['total'])

            html += f"""
            <div class="gpu-card">
                <div class="gpu-name">💾 {gpu_model}</div>
                <div style="font-size: 0.9em; color: #666; margin-bottom: 10px;">
                    历史总计: {gpu_total_models} | 完成: {stats['completed']} | 进行中: {stats['in_progress']}
                </div>
                <div class="progress-bar">
                    <div class="progress-fill" style="width: {completion_rate:.1f}%"></div>
                </div>
                <div style="font-size: 0.9em; color: #666; margin-top: 5px;">
                    完成率: {completion_rate:.1f}%
                </div>
            </div>
            """

        return html
    
    def generate_task_table(self, tasks):
        """生成任务表格"""
        html = """
        <table class="task-table">
            <thead>
                <tr>
                    <th>优先级</th>
                    <th>模型名称</th>
                    <th>测试人员</th>
                    <th>GPU型号</th>
                    <th>状态</th>
                    <th>进度</th>
                    <th>资料输出</th>
                    <th>关联用例集</th>
                    <th>时间范围</th>
                    <th>备注</th>
                </tr>
            </thead>
            <tbody>
        """
        
        for task in tasks:
            status_class = f"status-{task.test_status.replace('_', '-')}"
            document_icon = "✅" if task.document_output else "❌"
            
            # 生成优先级指示器
            priority = task.priority.lower() if hasattr(task, 'priority') and task.priority else 'p2'
            if priority in ['p1', 'high']:
                priority_html = '<span class="priority-indicator priority-high"></span>P1高'
                priority_class = 'priority-p1'
            elif priority in ['p2', 'medium']:
                priority_html = '<span class="priority-indicator priority-medium"></span>P2中'
                priority_class = 'priority-p2'
            elif priority in ['p3', 'low']:
                priority_html = '<span class="priority-indicator priority-low"></span>P3低'
                priority_class = 'priority-p3'
            else:
                priority_html = '<span class="priority-indicator" style="background: #ccc;"></span>未设置'
                priority_class = 'priority-p2'
            
            # 处理备注字段
            notes = getattr(task, 'notes', '') or ''
            notes_display = notes if notes else ''
            notes_tooltip = f'data-notes="{notes}"' if notes else ''
            notes_class = 'notes-tooltip' if notes else ''
            
            # 处理日期显示
            start_date_str = task.start_date.strftime('%m-%d') if task.start_date else '未设置'
            end_date_str = task.end_date.strftime('%m-%d') if task.end_date else '未设置'

            # 获取测试用例集信息
            test_case_set_info = task.get_test_case_set_info()
            if test_case_set_info:
                case_set_display = f'<span style="color: #1890ff; font-size: 0.85em;">{test_case_set_info["name"]}</span>'
                if test_case_set_info.get('test_cases_count', 0) > 0:
                    case_set_display += f'<br><span style="color: #666; font-size: 0.75em;">({test_case_set_info["test_cases_count"]}个用例)</span>'
            else:
                case_set_display = '<span style="color: #999; font-size: 0.85em;">未关联</span>'
            
            html += f"""
                <tr>
                    <td><span class="priority-badge {priority_class}">{priority_html}</span></td>
                    <td><strong>{task.model_name}</strong></td>
                    <td>{task.tester}</td>
                    <td>{task.gpu_model_display}</td>
                    <td><span class="status-badge {status_class}">{task.test_status_display}</span></td>
                    <td>
                        <div style="display: flex; align-items: center; gap: 10px;">
                            <div class="progress-bar" style="flex: 1; height: 6px;">
                                <div class="progress-fill" style="width: {task.progress}%"></div>
                            </div>
                            <span style="font-size: 0.9em; color: #666;">{task.progress}%</span>
                        </div>
                    </td>
                    <td style="text-align: center;">{document_icon}</td>
                    <td style="text-align: center; font-size: 0.85em;">
                        {case_set_display}
                    </td>
                    <td style="font-size: 0.9em; color: #666;">
                        {start_date_str} ~ {end_date_str}
                    </td>
                    <td class="notes-cell {notes_class}" {notes_tooltip}>
                        {notes_display[:50] + '...' if len(notes_display) > 50 else notes_display}
                    </td>
                </tr>
            """
        
        html += """
            </tbody>
        </table>
        """
        
        return html

    def generate_risk_section(self, risk_tasks):
        """生成风险任务模块"""
        if not risk_tasks:
            return """
            <div class="risk-section">
                <h2 class="section-title">⚠️ 风险任务监控</h2>
                <div style="text-align: center; padding: 30px; color: #28a745;">
                    <div style="font-size: 3em; margin-bottom: 10px;">✅</div>
                    <div style="font-size: 1.3em; font-weight: bold;">当前无风险任务</div>
                    <div style="color: #666; margin-top: 10px;">所有任务进展顺利</div>
                </div>
            </div>
            """

        # 按风险级别分组
        high_risks = [r for r in risk_tasks if r['risk_level'] == 'high']
        medium_risks = [r for r in risk_tasks if r['risk_level'] == 'medium']
        low_risks = [r for r in risk_tasks if r['risk_level'] == 'low']

        total_risks = len(risk_tasks)

        html = f"""
        <div class="risk-section">
            <h2 class="section-title">⚠️ 风险任务监控</h2>
            <div style="margin-bottom: 20px; padding: 15px; background: #fff3cd; border-radius: 8px; border-left: 4px solid #ffc107;">
                <strong>发现 {total_risks} 个风险任务</strong> -
                高风险: {len(high_risks)} 个，中风险: {len(medium_risks)} 个，低风险: {len(low_risks)} 个
            </div>

            <table class="risk-table">
                <thead>
                    <tr>
                        <th>风险级别</th>
                        <th>任务名称</th>
                        <th>测试人员</th>
                        <th>GPU型号</th>
                        <th>风险类型</th>
                        <th>风险描述</th>
                        <th>当前状态</th>
                        <th>进度</th>
                        <th>截止时间</th>
                    </tr>
                </thead>
                <tbody>
        """

        # 按风险级别排序显示
        sorted_risks = sorted(risk_tasks, key=lambda x: {'high': 1, 'medium': 2, 'low': 3}[x['risk_level']])

        for risk_info in sorted_risks:
            task = risk_info['task']
            risk_level = risk_info['risk_level']
            risk_type = risk_info['risk_type']
            risk_description = risk_info['risk_description']

            # 设置行的CSS类
            row_class = f"risk-level-{risk_level}"

            # 风险级别标签
            risk_badge_text = {'high': '高风险', 'medium': '中风险', 'low': '低风险'}[risk_level]

            # 状态显示
            status_display = task.test_status_display if hasattr(task, 'test_status_display') else task.test_status

            # 截止时间显示
            end_date_str = task.end_date.strftime('%m-%d') if task.end_date else '未设置'

            html += f"""
                <tr class="{row_class}">
                    <td>
                        <span class="risk-badge {risk_level}">{risk_badge_text}</span>
                    </td>
                    <td><strong>{task.model_name}</strong></td>
                    <td>{task.tester}</td>
                    <td>{task.gpu_model_display}</td>
                    <td>{risk_type}</td>
                    <td>{risk_description}</td>
                    <td>{status_display}</td>
                    <td>
                        <div style="display: flex; align-items: center; gap: 8px;">
                            <div class="progress-bar" style="flex: 1; height: 4px;">
                                <div class="progress-fill" style="width: {task.progress}%"></div>
                            </div>
                            <span style="font-size: 0.8em; color: #666;">{task.progress}%</span>
                        </div>
                    </td>
                    <td>{end_date_str}</td>
                </tr>
            """

        html += """
                </tbody>
            </table>
        </div>
        """

        return html

@method_decorator(csrf_exempt, name='dispatch')
class DocStatisticsView(View):
    """文档统计API - 实现model_report.sh的功能"""

    # 缓存配置
    CACHE_KEY = 'spug:doc_statistics:data'
    CACHE_EXPIRE_SECONDS = 3600  # 1小时缓存

    @auth('model_storage.doc_statistics.view')
    def get(self, request):
        """获取文档统计数据"""
        try:
            # 检查是否强制刷新或清除缓存
            force_refresh = request.GET.get('force_refresh', '').lower() == 'true'
            clear_cache = request.GET.get('clear_cache', '').lower() == 'true'
            debug_mode = request.GET.get('debug', '').lower() == 'true'

            # 如果需要清除缓存
            if clear_cache:
                self.clear_statistics_cache()
                logger.info("已清除文档统计缓存")
                print("🧹 已清除文档统计缓存")

            # 尝试从缓存获取数据
            if not force_refresh and not clear_cache:
                cached_data = self.get_cached_statistics()
                if cached_data:
                    logger.info("返回缓存的文档统计数据")
                    return JsonResponse({
                        'data': cached_data,
                        'success': True,
                        'from_cache': True,
                        'cache_time': cached_data.get('timestamp')
                    })

            # 缓存未命中或强制刷新，重新获取数据
            logger.info("开始重新获取文档统计数据...")
            statistics = self.get_doc_statistics()

            # 保存到缓存
            self.save_statistics_to_cache(statistics)

            return JsonResponse({
                'data': statistics,
                'success': True,
                'from_cache': False
            })
        except Exception as e:
            logger.error(f"获取文档统计失败: {e}")
            return JsonResponse({
                'data': None,
                'success': False,
                'error': str(e)
            }, status=500)

    def post(self, request):
        """异步刷新文档统计数据"""
        try:
            import threading

            # 清除现有缓存
            self.clear_statistics_cache()

            # 启动后台线程进行数据刷新
            def background_refresh():
                try:
                    logger.info("开始后台刷新文档统计数据...")
                    statistics = self.get_doc_statistics()
                    self.save_statistics_to_cache(statistics)
                    logger.info("后台刷新文档统计数据完成")
                except Exception as e:
                    logger.error(f"后台刷新文档统计数据失败: {e}")

            refresh_thread = threading.Thread(target=background_refresh)
            refresh_thread.daemon = True
            refresh_thread.start()

            return JsonResponse({
                'success': True,
                'message': '已启动后台数据刷新，请稍后重新获取数据'
            })

        except Exception as e:
            logger.error(f"启动异步刷新失败: {e}")
            return JsonResponse({
                'success': False,
                'error': str(e)
            }, status=500)

    def get_cached_statistics(self):
        """从缓存获取文档统计数据"""
        try:
            from django.core.cache import cache
            cached_data = cache.get(self.CACHE_KEY)
            if cached_data:
                logger.info("命中文档统计缓存")
                return cached_data
            return None
        except Exception as e:
            logger.error(f"获取缓存数据失败: {e}")
            return None

    def save_statistics_to_cache(self, statistics):
        """保存文档统计数据到缓存"""
        try:
            from django.core.cache import cache
            cache.set(self.CACHE_KEY, statistics, self.CACHE_EXPIRE_SECONDS)
            logger.info(f"文档统计数据已缓存，过期时间: {self.CACHE_EXPIRE_SECONDS}秒")
        except Exception as e:
            logger.error(f"保存缓存数据失败: {e}")

    def clear_statistics_cache(self):
        """清除文档统计缓存"""
        try:
            from django.core.cache import cache
            cache.delete(self.CACHE_KEY)
            logger.info("文档统计缓存已清除")
        except Exception as e:
            logger.error(f"清除缓存失败: {e}")

    def get_doc_statistics(self):
        """获取文档统计数据 - 修复版本，与model_report.sh逻辑保持一致"""
        try:
            from apps.host.models import Host

            # 获取主机配置
            host_obj = Host.objects.filter(hostname='**********').first()
            if not host_obj:
                return {
                    'error': '未找到主机配置',
                    'vendor_stats': {},
                    'total_stats': {'models': 0, 'versions': 0, 'docs': 0, 'words': 0, 'pdfs': 0, 'complete': 0, 'incomplete': 0},
                    'rates': {'doc_rate': 0, 'complete_rate': 0},
                    'timestamp': timezone.now().isoformat()
                }

            print("📊 开始连接远程服务器进行文档统计...")
            print("🔧 为确保数据准确性，暂时使用逐个扫描模式")

            # 通过SSH连接并扫描
            with host_obj.get_ssh() as ssh:
                return self.scan_all_vendors_via_ssh(ssh)
                
        except Exception as e:
            print(f"❌ 获取文档统计失败: {e}")
            return {
                'error': f'获取文档统计失败: {str(e)}',
                'vendor_stats': {},
                'total_stats': {'models': 0, 'versions': 0, 'docs': 0, 'words': 0, 'pdfs': 0, 'complete': 0, 'incomplete': 0},
                'rates': {'doc_rate': 0, 'complete_rate': 0},
                'timestamp': timezone.now().isoformat()
            }

    def scan_all_vendors_via_ssh(self, ssh):
        """通过SSH扫描所有厂商的统计数据"""
        base_path = "/HDD_Raid/SVN_MODEL_REPO/Vendor"

        # 自动获取所有厂商目录
        try:
            # 使用更简单的命令来获取厂商列表
            command = f'ls -1 "{base_path}" | grep -v "^\\.svn" | sort'
            stdin, stdout, stderr = ssh.exec_command(command)
            vendors_output = stdout.read().decode('utf-8').strip()
            error_output = stderr.read().decode('utf-8').strip()

            if vendors_output and not error_output:
                vendors = [v.strip() for v in vendors_output.split('\n') if v.strip()]
                print(f"🏢 自动发现厂商: {vendors}")
            else:
                # 如果自动获取失败，使用备用列表
                vendors = ["AMD", "Biren", "Cambricon", "Enflame", "Hygon", "Iluvatar", "Kunlunxin", "MetaX", "Moffett", "Moore_Threads"]
                print(f"⚠️ 自动获取厂商失败，使用备用列表: {vendors}")
                if error_output:
                    print(f"⚠️ 错误信息: {error_output}")
        except Exception as e:
            # 如果出错，使用备用列表
            vendors = ["AMD", "Biren", "Cambricon", "Enflame", "Hygon", "Iluvatar", "Kunlunxin", "MetaX", "Moffett", "Moore_Threads"]
            print(f"⚠️ 获取厂商列表出错: {e}，使用备用列表: {vendors}")
        
        vendor_stats = {}
        
        # 总计变量
        total_models = 0
        total_complete = 0
        total_incomplete = 0
        total_versions = 0
        total_docs = 0
        total_words = 0
        total_pdfs = 0
        
        print(f"📊 开始扫描基础路径: {base_path}")

        # 暂时禁用批量扫描，使用可靠的逐个扫描方法确保数据准确性
        print("🔧 使用逐个扫描模式确保数据准确性")

        for vendor in vendors:
            vendor_path = f"{base_path}/{vendor}"
            print(f"🏢 扫描厂商: {vendor}")

            # 检查厂商目录是否存在
            exit_code, output = ssh.exec_command_raw(f'test -d "{vendor_path}" && echo "exists" || echo "not_exists"')

            if "not_exists" in output:
                print(f"  ❌ 厂商目录不存在: {vendor_path}")
                vendor_stats[vendor] = {
                    'models': 0, 'versions': 0, 'docs': 0, 'words': 0, 'pdfs': 0,
                    'complete': 0, 'incomplete': 0, 'missing_models': []
                }
                continue

            vendor_stats[vendor] = self.scan_vendor_directory_via_ssh_fixed(ssh, vendor_path, vendor)

            # 累计到总数
            total_models += vendor_stats[vendor]['models']
            total_complete += vendor_stats[vendor]['complete']
            total_incomplete += vendor_stats[vendor]['incomplete']
            total_versions += vendor_stats[vendor]['versions']
            total_docs += vendor_stats[vendor]['docs']
            total_words += vendor_stats[vendor]['words']
            total_pdfs += vendor_stats[vendor]['pdfs']
        
        # 计算覆盖率
        doc_rate = round((total_docs / total_versions * 100) if total_versions > 0 else 0, 1)
        complete_rate = round((total_complete / total_models * 100) if total_models > 0 else 0, 1)
        
        result = {
            'vendor_stats': vendor_stats,
            'total_stats': {
                'models': total_models,
                'versions': total_versions,
                'docs': total_docs,
                'words': total_words,
                'pdfs': total_pdfs,
                'complete': total_complete,
                'incomplete': total_incomplete
            },
            'rates': {
                'doc_rate': doc_rate,
                'complete_rate': complete_rate
            },
            'timestamp': timezone.now().isoformat()
        }
        
        print(f"📊 扫描完成，总模型数: {total_models}, 完整: {total_complete}, 缺失: {total_incomplete}")
        return result

    def batch_scan_vendors_via_ssh(self, ssh, base_path, vendors):
        """批量扫描所有厂商的统计数据 - 优化版本，减少SSH调用次数"""
        print(f"📊 开始批量扫描，厂商数量: {len(vendors)}")

        # 构建批量扫描脚本
        script_lines = [
            "#!/bin/bash",
            "# 批量文档统计脚本",
            f"BASE_PATH='{base_path}'",
            "",
            "# 输出格式: VENDOR|MODELS|VERSIONS|DOCS|WORDS|PDFS|COMPLETE|INCOMPLETE",
            ""
        ]

        for vendor in vendors:
            vendor_path = f"{base_path}/{vendor}"
            script_lines.extend([
                f"# 扫描厂商: {vendor}",
                f"if [ -d '{vendor_path}' ]; then",
                f"  echo 'VENDOR_START:{vendor}'",
                f"  # 统计GPU型号目录数量（模型数）",
                f"  MODELS=$(find '{vendor_path}' -mindepth 1 -maxdepth 1 -type d -not -path '*.svn*' | wc -l)",
                f"  echo 'MODELS:'$MODELS",
                f"  ",
                f"  # 统计版本目录和文档",
                f"  VERSIONS=0",
                f"  DOCS=0",
                f"  WORDS=0",
                f"  PDFS=0",
                f"  COMPLETE=0",
                f"  INCOMPLETE=0",
                f"  ",
                f"  # 遍历GPU型号目录",
                f"  for gpu_dir in '{vendor_path}'/*; do",
                f"    if [ -d \"$gpu_dir\" ] && [[ \"$gpu_dir\" != *.svn* ]]; then",
                f"      # 遍历任务类型目录",
                f"      for task_dir in \"$gpu_dir\"/*; do",
                f"        if [ -d \"$task_dir\" ] && [[ \"$task_dir\" != *.svn* ]]; then",
                f"          # 统计版本目录",
                f"          VERSION_COUNT=$(find \"$task_dir\" -mindepth 1 -maxdepth 1 -type d -name 'v*' -not -path '*.svn*' | wc -l)",
                f"          VERSIONS=$((VERSIONS + VERSION_COUNT))",
                f"          ",
                f"          # 检查每个版本目录的文档",
                f"          for version_dir in \"$task_dir\"/v*; do",
                f"            if [ -d \"$version_dir\" ] && [[ \"$version_dir\" != *.svn* ]]; then",
                f"              DOC_PATH=\"$version_dir/doc\"",
                f"              if [ -d \"$DOC_PATH\" ]; then",
                f"                DOCS=$((DOCS + 1))",
                f"                # 检查Word文件",
                f"                if find \"$DOC_PATH\" -maxdepth 1 -type f \\( -iname '*.doc' -o -iname '*.docx' \\) -not -path '*.svn*' | grep -q .; then",
                f"                  WORDS=$((WORDS + 1))",
                f"                fi",
                f"                # 检查PDF文件",
                f"                if find \"$DOC_PATH\" -maxdepth 1 -type f -iname '*.pdf' -not -path '*.svn*' | grep -q .; then",
                f"                  PDFS=$((PDFS + 1))",
                f"                fi",
                f"              fi",
                f"            fi",
                f"          done",
                f"        fi",
                f"      done",
                f"    fi",
                f"  done",
                f"  ",
                f"  # 计算完整和不完整模型数",
                f"  if [ $MODELS -gt 0 ]; then",
                f"    if [ $DOCS -gt 0 ]; then",
                f"      COMPLETE=$MODELS",
                f"    else",
                f"      INCOMPLETE=$MODELS",
                f"    fi",
                f"  fi",
                f"  ",
                f"  echo 'VERSIONS:'$VERSIONS",
                f"  echo 'DOCS:'$DOCS",
                f"  echo 'WORDS:'$WORDS",
                f"  echo 'PDFS:'$PDFS",
                f"  echo 'COMPLETE:'$COMPLETE",
                f"  echo 'INCOMPLETE:'$INCOMPLETE",
                f"  echo 'VENDOR_END:{vendor}'",
                f"else",
                f"  echo 'VENDOR_START:{vendor}'",
                f"  echo 'MODELS:0'",
                f"  echo 'VERSIONS:0'",
                f"  echo 'DOCS:0'",
                f"  echo 'WORDS:0'",
                f"  echo 'PDFS:0'",
                f"  echo 'COMPLETE:0'",
                f"  echo 'INCOMPLETE:0'",
                f"  echo 'VENDOR_END:{vendor}'",
                f"fi",
                ""
            ])

        # 执行批量扫描脚本
        script_content = "\n".join(script_lines)

        try:
            # 创建临时脚本文件
            temp_script = f"/tmp/doc_stats_batch_{timezone.now().strftime('%Y%m%d_%H%M%S')}.sh"

            # 上传脚本
            exit_code, output = ssh.exec_command_raw(f"cat > {temp_script} << 'EOF'\n{script_content}\nEOF")
            if exit_code != 0:
                raise Exception(f"创建批量扫描脚本失败: {output}")

            # 设置执行权限并运行
            exit_code, output = ssh.exec_command_raw(f"chmod +x {temp_script} && {temp_script}")

            # 清理临时文件
            ssh.exec_command_raw(f"rm -f {temp_script}")

            if exit_code != 0:
                raise Exception(f"批量扫描脚本执行失败: {output}")

            # 解析输出结果
            return self.parse_batch_scan_output(output, vendors)

        except Exception as e:
            print(f"❌ 批量扫描失败: {e}")
            raise

    def parse_batch_scan_output(self, output, vendors):
        """解析批量扫描的输出结果"""
        vendor_results = {}
        current_vendor = None
        current_stats = {}

        lines = output.strip().split('\n')

        for line in lines:
            line = line.strip()
            if not line:
                continue

            if line.startswith('VENDOR_START:'):
                current_vendor = line.split(':', 1)[1]
                current_stats = {
                    'models': 0, 'versions': 0, 'docs': 0, 'words': 0, 'pdfs': 0,
                    'complete': 0, 'incomplete': 0, 'missing_models': []
                }
            elif line.startswith('VENDOR_END:'):
                if current_vendor:
                    vendor_results[current_vendor] = current_stats
                    print(f"  📊 {current_vendor} 批量统计: 模型{current_stats['models']}, 版本{current_stats['versions']}, 完整{current_stats['complete']}")
                current_vendor = None
            elif ':' in line and current_vendor:
                key, value = line.split(':', 1)
                if key in ['MODELS', 'VERSIONS', 'DOCS', 'WORDS', 'PDFS', 'COMPLETE', 'INCOMPLETE']:
                    current_stats[key.lower()] = int(value) if value.isdigit() else 0

        return vendor_results

    def scan_vendor_directory_via_ssh_fixed(self, ssh, vendor_path, vendor_name):
        """扫描单个厂商目录 - 修复版本，按照model_report.sh的逻辑"""
        print(f"  📁 扫描厂商目录: {vendor_path}")
        
        vendor_models = 0
        vendor_complete = 0
        vendor_incomplete = 0
        vendor_versions = 0
        vendor_docs = 0
        vendor_words = 0
        vendor_pdfs = 0
        vendor_missing_models = []
        
        try:
            # 获取GPU型号目录 (第一层子目录)
            exit_code, output = ssh.exec_command_raw(f'find "{vendor_path}" -mindepth 1 -maxdepth 1 -type d -not -path "*.svn*"')
            
            if exit_code != 0:
                print(f"  ❌ 无法访问厂商目录: {vendor_path}")
                return {
                    'models': 0, 'versions': 0, 'docs': 0, 'words': 0, 'pdfs': 0,
                    'complete': 0, 'incomplete': 0, 'missing_models': []
                }
            
            gpu_dirs = [line.strip() for line in output.split('\n') if line.strip()]
            print(f"  📱 找到GPU型号目录: {len(gpu_dirs)}个")
            
            # 遍历GPU型号目录
            for gpu_dir in gpu_dirs:
                if not gpu_dir or '.svn' in gpu_dir:
                    continue
                
                print(f"    🔍 扫描GPU目录: {gpu_dir}")
                
                # 获取大模型目录 (第二层子目录)
                exit_code, output = ssh.exec_command_raw(f'find "{gpu_dir}" -mindepth 1 -maxdepth 1 -type d -not -path "*.svn*"')
                
                if exit_code != 0:
                    continue
                
                model_dirs = [line.strip() for line in output.split('\n') if line.strip()]
                print(f"      🤖 找到大模型目录: {len(model_dirs)}个")
                
                # 遍历大模型目录
                for model_dir in model_dirs:
                    if not model_dir or '.svn' in model_dir:
                        continue
                    
                    model_name = os.path.basename(model_dir)
                    print(f"        📊 分析模型: {model_name}")
                    
                    # 计数模型
                    vendor_models += 1
                    
                    # 分析该模型的文档完整性
                    model_stats = self.scan_model_directory_via_ssh_fixed(ssh, model_dir, model_name)
                    
                    # 累计统计
                    vendor_versions += model_stats['versions']
                    vendor_docs += model_stats['docs']
                    vendor_words += model_stats['words']
                    vendor_pdfs += model_stats['pdfs']
                    
                    # 判断模型是否完整：按照原版脚本逻辑 - 版本数=文档数=Word数=PDF数
                    if model_stats['versions'] > 0:
                        versions = model_stats['versions']
                        docs = model_stats['docs']
                        words = model_stats['words']
                        pdfs = model_stats['pdfs']

                        # 原版脚本逻辑：版本数 = 文档数 = Word数 = PDF数 才算完整
                        if versions == docs == words == pdfs:
                            vendor_complete += 1
                            print(f"        ✅ 模型完整: {model_name} (版本:{versions}, 文档:{docs}, Word:{words}, PDF:{pdfs})")
                        else:
                            vendor_incomplete += 1
                            vendor_missing_models.append(model_name)
                            print(f"        ❌ 模型不完整: {model_name} (版本:{versions}, 文档:{docs}, Word:{words}, PDF:{pdfs})")
                    else:
                        print(f"        ⚠️  模型无版本: {model_name}")
            
        except Exception as e:
            print(f"  ❌ 扫描厂商目录异常: {e}")
        
        result = {
            'models': vendor_models,
            'versions': vendor_versions,
            'docs': vendor_docs,
            'words': vendor_words,
            'pdfs': vendor_pdfs,
            'complete': vendor_complete,
            'incomplete': vendor_incomplete,
            'missing_models': vendor_missing_models
        }
        
        print(f"  📊 {vendor_name} 统计: 模型{vendor_models}, 版本{vendor_versions}, 完整{vendor_complete}, 缺失{vendor_incomplete}")
        return result

    def scan_model_directory_via_ssh_fixed(self, ssh, model_dir, model_name):
        """扫描单个模型目录 - 修复版本"""
        model_versions = 0
        model_docs = 0
        model_words = 0
        model_pdfs = 0
        
        try:
            # 获取任务类型目录 (如 Inference, Training 等)
            exit_code, output = ssh.exec_command_raw(f'find "{model_dir}" -mindepth 1 -maxdepth 1 -type d -not -path "*.svn*"')
            
            if exit_code != 0:
                return {'versions': 0, 'docs': 0, 'words': 0, 'pdfs': 0}
            
            task_dirs = [line.strip() for line in output.split('\n') if line.strip()]
            
            # 遍历任务类型目录
            for task_dir in task_dirs:
                if not task_dir or '.svn' in task_dir:
                    continue
                
                # 获取版本目录 (v* 开头的目录)
                exit_code, output = ssh.exec_command_raw(f'find "{task_dir}" -mindepth 1 -maxdepth 1 -type d -name "v*" -not -path "*.svn*"')
                
                if exit_code != 0:
                    continue
                
                version_dirs = [line.strip() for line in output.split('\n') if line.strip()]
                
                # 遍历版本目录
                for version_dir in version_dirs:
                    if not version_dir or '.svn' in version_dir:
                        continue
                    
                    # 计数版本目录
                    model_versions += 1
                    
                    # 检查doc文件夹
                    doc_path = f"{version_dir}/doc"
                    exit_code, output = ssh.exec_command_raw(f'test -d "{doc_path}" && echo "exists" || echo "not_exists"')
                    
                    if "exists" in output:
                        model_docs += 1
                        
                        # 检查Word文件
                        exit_code, word_output = ssh.exec_command_raw(f'find "{doc_path}" -maxdepth 1 -type f \\( -iname "*.doc" -o -iname "*.docx" \\) -not -path "*.svn*"')
                        if exit_code == 0 and word_output.strip():
                            model_words += 1
                        
                        # 检查PDF文件
                        exit_code, pdf_output = ssh.exec_command_raw(f'find "{doc_path}" -maxdepth 1 -type f -iname "*.pdf" -not -path "*.svn*"')
                        if exit_code == 0 and pdf_output.strip():
                            model_pdfs += 1
        
        except Exception as e:
            print(f"          ❌ 扫描模型目录异常: {e}")
        
        return {
            'versions': model_versions,
            'docs': model_docs,
            'words': model_words,
            'pdfs': model_pdfs
        }

@method_decorator(csrf_exempt, name='dispatch')
class GPUView(View):
    """GPU 管理"""

    @auth('model_storage.gpu_management.view')
    def get(self, request, pk=None):
        """获取GPU列表或单个GPU"""
        if pk:
            try:
                gpu = GPUDevice.objects.get(pk=pk)
                return json_response(gpu.to_dict())
            except GPUDevice.DoesNotExist:
                return json_response(error=f"GPU with id {pk} does not exist.", status=404)

        # 过滤参数
        vendor = request.GET.get('vendor')
        name = request.GET.get('name')
        model_name = request.GET.get('model_name')

        # 分页参数
        page_param = request.GET.get('page')
        page_size_param = request.GET.get('page_size')
        page = int(page_param) if page_param else 1
        page_size = int(page_size_param) if page_size_param else 12  # 默认改为12，与前端一致

        print(f"[DEBUG] GPU分页请求: page={page}, page_size={page_size}, page_param={page_param}, page_size_param={page_size_param}")

        gpus = GPUDevice.objects.all()
        if vendor:
            gpus = gpus.filter(vendor__icontains=vendor)
        if name:
            gpus = gpus.filter(name__icontains=name)
        if model_name:
            tested_gpu_names = TestTask.objects.filter(model_name__icontains=model_name).values_list('gpu_model', flat=True).distinct()
            gpus = gpus.filter(name__in=tested_gpu_names)

        gpus = gpus.order_by('name')

        # 如果明确没有传递分页参数，返回所有数据（兼容旧版本）
        if not page_param and not page_size_param:
            gpu_list = [g.to_dict() for g in gpus]
            print(f"[DEBUG] 返回所有数据，共{len(gpu_list)}条")
            return json_response(gpu_list)

        # 分页处理
        total = gpus.count()
        start = (page - 1) * page_size
        end = start + page_size
        gpu_page = gpus[start:end]

        gpu_list = [g.to_dict() for g in gpu_page]

        result = {
            'results': gpu_list,
            'count': total,
            'current_page': page,
            'page_size': page_size,
            'total_pages': (total + page_size - 1) // page_size
        }

        print(f"[DEBUG] 返回分页数据: 第{page}页，每页{page_size}条，共{total}条，当前页{len(gpu_list)}条")

        # 返回分页格式数据
        return json_response(result)

    @auth('model_storage.gpu_management.add')
    def post(self, request):
        """创建新的GPU"""
        try:
            data = json.loads(request.body)
            # 基础校验
            if not data.get('name') or not data.get('vendor'):
                return json_response(error="'name'和'vendor'为必填字段", status=400)

            gpu = GPUDevice.objects.create(
                name=data['name'],
                vendor=data['vendor'],
                description=data.get('description', ''),
                manual_models=data.get('manual_models', '')
            )
            return json_response(gpu.to_dict(), status=201)
        except json.JSONDecodeError:
            return json_response(error="无效的JSON格式", status=400)
        except Exception as e:
            return json_response(error=f"创建失败: {e}", status=500)

    @auth('model_storage.gpu_management.edit')
    def patch(self, request, pk):
        """部分更新GPU"""
        try:
            gpu = GPUDevice.objects.get(pk=pk)
            data = json.loads(request.body)
            for key, value in data.items():
                if hasattr(gpu, key) and key not in ['id', 'created_at', 'updated_at']:
                    setattr(gpu, key, value)
            gpu.updated_at = timezone.now()
            gpu.save()
            return json_response(gpu.to_dict())
        except GPUDevice.DoesNotExist:
            return json_response(error=f"GPU with id {pk} does not exist.", status=404)
        except json.JSONDecodeError:
            return json_response(error="无效的JSON格式", status=400)
        except Exception as e:
            return json_response(error=f"更新失败: {e}", status=500)

    @auth('model_storage.gpu_management.del')
    def delete(self, request, pk):
        """删除GPU"""
        try:
            gpu = GPUDevice.objects.get(pk=pk)
            gpu_name = gpu.name
            gpu.delete()
            return json_response({'message': f'GPU "{gpu_name}" 删除成功'})
        except GPUDevice.DoesNotExist:
            return json_response(error=f"GPU with id {pk} does not exist.", status=404)
        except Exception as e:
            return json_response(error=str(e), status=500)


@method_decorator(csrf_exempt, name='dispatch')
class TestTaskStepStatusView(View):
    """测试任务步骤状态管理API"""

    @auth('model_storage.test_tasks.edit')
    def put(self, request, task_id):
        """更新测试任务的测试步骤完成状态"""
        try:
            import json
            data = json.loads(request.body)
            task_id = int(task_id)

            # 查找要更新的测试任务
            try:
                task = TestTask.objects.get(id=task_id)
            except TestTask.DoesNotExist:
                return json_response(error='测试任务不存在')

            # 更新步骤完成状态
            step_completion_status = data.get('step_completion_status', {})
            task.step_completion_status = json.dumps(step_completion_status)

            # 如果有手动进度更新，则使用手动值
            if 'progress_percentage' in data:
                task.progress = data.get('progress_percentage', 0)

            task.save()

            return json_response({
                'message': '测试任务步骤状态更新成功',
                'task': task.to_dict()
            })

        except Exception as e:
            return json_response(error=f'更新失败: {str(e)}')


@method_decorator(csrf_exempt, name='dispatch')
class UserSearchView(View):
    """用户搜索API - 用于任务分配时的人员选择"""

    def get(self, request):
        """获取用户列表，支持模糊搜索"""
        try:
            from apps.account.models import User

            # 获取搜索关键词
            search_keyword = request.GET.get('search', '').strip()

            # 构建查询条件
            users_query = User.objects.filter(
                deleted_by_id__isnull=True,  # 未删除的用户
                is_active=True  # 激活状态的用户
            )

            # 如果有搜索关键词，进行模糊匹配
            if search_keyword:
                from django.db.models import Q
                users_query = users_query.filter(
                    Q(nickname__icontains=search_keyword) |  # 姓名模糊匹配
                    Q(username__icontains=search_keyword)    # 用户名模糊匹配
                )

            # 限制返回数量，避免数据过多
            users_query = users_query[:20]

            # 构建返回数据
            users_data = []
            for user in users_query:
                users_data.append({
                    'id': user.id,
                    'username': user.username,
                    'nickname': user.nickname,
                    'label': user.nickname,  # 用于前端显示
                    'value': user.nickname   # 用于前端选择
                })

            return json_response({
                'data': users_data,
                'error': ''
            })

        except Exception as e:
            return json_response({
                'data': [],
                'error': f'获取用户列表失败: {str(e)}'
            })


@method_decorator(csrf_exempt, name='dispatch')
class TaskAssignView(View):
    """任务分配API"""

    def post(self, request):
        """分配任务给指定人员"""
        try:
            import json
            data = json.loads(request.body)

            # 获取分配参数
            task_id = data.get('task_id')
            assignee = data.get('assignee')
            message_content = data.get('message', '')
            assigned_by = data.get('assigned_by', '管理员')

            if not task_id or not assignee:
                return json_response(error='任务ID和分配人员不能为空')

            # 查找任务
            try:
                task = TestTask.objects.get(id=task_id)
            except TestTask.DoesNotExist:
                return json_response(error='任务不存在')

            # 更新任务的负责人
            old_tester = task.tester
            task.tester = assignee
            task.save()

            # 记录分配日志（这里可以扩展为专门的分配记录表）
            assign_log = {
                'task_id': task_id,
                'task_name': task.model_name,
                'old_assignee': old_tester,
                'new_assignee': assignee,
                'assigned_by': assigned_by,
                'message': message_content,
                'assigned_at': timezone.now().strftime('%Y-%m-%d %H:%M:%S')
            }

            # 发送企业微信通知
            try:
                from .wechat_service import send_task_assignment_wechat
                success, msg = send_task_assignment_wechat(
                    task.to_dict(),
                    assignee,
                    assigned_by,
                    message_content
                )
                assign_log['wechat_notification'] = {
                    'success': success,
                    'message': msg
                }
            except Exception as e:
                logger.warning(f"企业微信通知发送失败: {str(e)}")
                assign_log['wechat_notification'] = {
                    'success': False,
                    'message': f'通知发送异常: {str(e)}'
                }

            return json_response({
                'message': f'任务已成功分配给 {assignee}',
                'data': {
                    'task': task.to_dict(),
                    'assign_log': assign_log
                }
            })

        except json.JSONDecodeError:
            return json_response(error='无效的JSON格式')
        except Exception as e:
            return json_response(error=f'任务分配失败: {str(e)}')


@method_decorator(csrf_exempt, name='dispatch')
class WeChatConfigView(View):
    """企业微信配置管理API"""

    def get(self, request):
        """获取用户的企业微信配置状态"""
        try:
            from apps.account.models import User
            from apps.alarm.models import Contact

            # 获取所有活跃用户的企业微信配置状态
            users_config = []
            users = User.objects.filter(deleted_by_id__isnull=True, is_active=True)

            for user in users:
                contact = Contact.objects.filter(name=user.nickname).first()
                users_config.append({
                    'id': user.id,
                    'username': user.username,
                    'nickname': user.nickname,
                    'has_wechat_config': bool(contact and contact.qy_wx),
                    'wechat_webhook': contact.qy_wx if contact else None
                })

            return json_response({
                'data': users_config,
                'total': len(users_config),
                'configured_count': len([u for u in users_config if u['has_wechat_config']])
            })

        except Exception as e:
            return json_response(error=f'获取企业微信配置失败: {str(e)}')

    def post(self, request):
        """测试企业微信消息发送"""
        try:
            import json
            data = json.loads(request.body)

            webhook_url = data.get('webhook_url')
            test_message = data.get('test_message', '这是一条来自Spug模型测试管理平台的测试消息')

            if not webhook_url:
                return json_response(error='请提供企业微信Webhook地址')

            # 构建测试消息
            message_data = {
                'msgtype': 'markdown',
                'markdown': {
                    'content': f"""## 🧪 企业微信通知测试

**测试时间：** {timezone.now().strftime('%Y-%m-%d %H:%M:%S')}

**测试内容：** {test_message}

### ✅ 配置状态
如果您能看到这条消息，说明企业微信通知配置正常！

---
> 📱 来自 **Spug模型测试管理平台**"""
                }
            }

            # 发送测试消息
            from .wechat_service import WeChatNotificationService
            success = WeChatNotificationService._send_wechat_message(webhook_url, message_data)

            if success:
                return json_response({'message': '测试消息发送成功！'})
            else:
                return json_response(error='测试消息发送失败，请检查Webhook地址是否正确')

        except json.JSONDecodeError:
            return json_response(error='无效的JSON格式')
        except Exception as e:
            return json_response(error=f'测试发送失败: {str(e)}')

    def post(self, request, task_id):
        """手动设置测试任务进度百分比"""
        try:
            import json
            data = json.loads(request.body)
            task_id = int(task_id)

            # 查找要更新的测试任务
            try:
                task = TestTask.objects.get(id=task_id)
            except TestTask.DoesNotExist:
                return json_response(error='测试任务不存在')

            # 手动设置进度
            progress_percentage = data.get('progress_percentage', 0)
            if 0 <= progress_percentage <= 100:
                task.progress = progress_percentage
                task.save()

                return json_response({
                    'message': '测试任务进度更新成功',
                    'task': task.to_dict()
                })
            else:
                return json_response(error='进度百分比必须在0-100之间')

        except Exception as e:
            return json_response(error=f'更新失败: {str(e)}')


@method_decorator(csrf_exempt, name='dispatch')
class TestPlanStatisticsView(View):
    """测试计划统计API"""

    @auth('model_storage.test_plans.view')
    def get(self, request):
        """获取测试计划统计数据"""
        try:
            from django.db.models import Q, Avg
            from django.utils import timezone
            print("=== 开始获取测试计划统计数据 ===")
            logger.info("开始获取测试计划统计数据")
            
            # 总体概况统计 - 统一使用NewReleasePlan模型
            print("=== 开始统计总体概况 ===")
            try:
                total_plans = NewReleasePlan.objects.count()
                print(f"发布计划总数: {total_plans} (来源: NewReleasePlan表)")
                logger.info(f"发布计划总数: {total_plans}, 来源: NewReleasePlan表")
            except Exception as e:
                print(f"获取发布计划总数失败: {e}")
                logger.error(f"获取发布计划总数失败: {e}")
                total_plans = 0

            try:
                total_tasks = TestTask.objects.count()
                print(f"测试任务总数: {total_tasks} (来源: TestTask表)")
                logger.info(f"测试任务总数: {total_tasks}, 来源: TestTask表")
            except Exception as e:
                print(f"获取测试任务总数失败: {e}")
                logger.error(f"获取测试任务总数失败: {e}")
                total_tasks = 0

            try:
                completed_tasks = TestTask.objects.filter(test_status='completed').count()
                print(f"已完成任务数: {completed_tasks}")
                logger.info(f"已完成任务数: {completed_tasks}")
            except Exception as e:
                print(f"获取已完成任务数失败: {e}")
                logger.error(f"获取已完成任务数失败: {e}")
                completed_tasks = 0

            completion_rate = (completed_tasks / total_tasks * 100) if total_tasks > 0 else 0
            print(f"完成率: {completion_rate}%")

            # 计算平均进度 - 改进空值处理
            print("=== 开始计算平均进度 ===")
            try:
                # 使用数据库聚合函数计算平均值，自动处理NULL值
                avg_progress_result = TestTask.objects.filter(
                    progress__isnull=False
                ).aggregate(avg_progress=Avg('progress'))
                avg_progress = avg_progress_result['avg_progress'] or 0
                print(f"平均进度: {avg_progress}% (使用数据库聚合计算)")
                logger.info(f"平均进度: {avg_progress}%")
            except Exception as e:
                print(f"计算平均进度失败: {e}")
                logger.error(f"计算平均进度失败: {e}")
                avg_progress = 0

            overview = {
                'total_plans': total_plans,
                'total_tasks': total_tasks,
                'completion_rate': round(completion_rate, 1),
                'avg_progress': round(avg_progress, 1)
            }

            # 人员效率统计
            print("=== 开始统计人员效率 ===")
            try:
                from django.db.models import Count
                tester_stats = TestTask.objects.values('tester').annotate(
                    total_tasks=Count('id'),
                    completed_tasks=Count('id', filter=Q(test_status='completed'))
                ).order_by('-completed_tasks')[:10]
                print(f"测试人员统计: {list(tester_stats)}")
            except Exception as e:
                print(f"统计人员效率失败: {e}")
                tester_stats = []

            # 计算每个测试人员的平均进度
            for stat in tester_stats:
                tester_tasks = TestTask.objects.filter(tester=stat['tester'])
                if tester_tasks.exists():
                    total_progress = sum(task.progress or 0 for task in tester_tasks)
                    stat['avg_progress'] = total_progress / tester_tasks.count()
                else:
                    stat['avg_progress'] = 0

            personnel = {
                'total_testers': len(set(TestTask.objects.values_list('tester', flat=True))),
                'tester_stats': list(tester_stats)
            }

            # GPU资源统计 - 来自GPU管理模块
            print("=== 开始统计GPU资源 ===")
            try:
                total_devices = GPUDevice.objects.count()
                print(f"GPU设备总数: {total_devices} (来源: GPUDevice表)")
                logger.info(f"GPU设备总数: {total_devices}, 来源: GPUDevice表")
                
                # 数据一致性检查：检查TestTask中的gpu_model是否在GPUDevice中存在
                gpu_device_names = set(GPUDevice.objects.values_list('name', flat=True))
                task_gpu_models = set(TestTask.objects.exclude(gpu_model__isnull=True).exclude(gpu_model='').values_list('gpu_model', flat=True))
                unmatched_models = task_gpu_models - gpu_device_names
                if unmatched_models:
                    print(f"警告: 测试任务中存在未在GPU设备表中注册的GPU型号: {unmatched_models}")
                    logger.warning(f"数据不一致: 测试任务中的GPU型号 {unmatched_models} 未在GPUDevice表中找到")
                    
            except Exception as e:
                print(f"获取GPU设备总数失败: {e}")
                logger.error(f"获取GPU设备总数失败: {e}")
                total_devices = 0

            # 热门GPU型号统计 - 来自测试任务数据
            try:
                from django.db.models import Count
                popular_models = TestTask.objects.exclude(
                    gpu_model__isnull=True
                ).exclude(
                    gpu_model=''
                ).values('gpu_model').annotate(
                    usage_count=Count('id')
                ).order_by('-usage_count')[:5]
                print(f"热门GPU型号: {list(popular_models)} (来源: TestTask表)")
                logger.info(f"热门GPU型号统计: {list(popular_models)}")
            except Exception as e:
                print(f"统计热门GPU型号失败: {e}")
                logger.error(f"统计热门GPU型号失败: {e}")
                popular_models = []

            gpu_resources = {
                'total_devices': total_devices,
                'popular_models': list(popular_models)
            }

            # 风险预警统计 - 来自发布计划和测试任务数据
            print("=== 开始统计风险预警 ===")
            try:
                high_priority_tasks = TestTask.objects.filter(priority='p1').count()
                print(f"高优先级任务数: {high_priority_tasks} (来源: TestTask表, priority='p1')")
                logger.info(f"高优先级任务数: {high_priority_tasks}")

                delayed_tasks = TestTask.objects.filter(
                    end_date__lt=timezone.now().date(),
                    test_status__in=['pending', 'in_progress']
                ).count()
                print(f"延期任务数: {delayed_tasks} (来源: TestTask表, 结束时间小于今天且状态为待开始或进行中)")
                logger.info(f"延期任务数: {delayed_tasks}")

                blocked_tasks = TestTask.objects.filter(test_status='blocked').count()
                print(f"阻塞任务数: {blocked_tasks} (来源: TestTask表, test_status='blocked')")
                logger.info(f"阻塞任务数: {blocked_tasks}")

                total_risk_tasks = high_priority_tasks + delayed_tasks + blocked_tasks
                print(f"风险任务总数: {total_risk_tasks}")
                logger.info(f"风险任务总数: {total_risk_tasks}")
            except Exception as e:
                print(f"统计风险预警失败: {e}")
                logger.error(f"统计风险预警失败: {e}")
                high_priority_tasks = delayed_tasks = blocked_tasks = total_risk_tasks = 0

            risk_alerts = {
                'high_priority_tasks': high_priority_tasks,
                'delayed_tasks': delayed_tasks,
                'blocked_tasks': blocked_tasks,
                'total_risk_tasks': total_risk_tasks
            }

            result_data = {
                'overview': overview,
                'personnel': personnel,
                'gpu_resources': gpu_resources,
                'risk_alerts': risk_alerts
            }
            
            print("=== 最终返回数据 ===")
            print(f"返回数据: {result_data}")
            logger.info(f"测试计划统计数据获取成功，返回数据: {result_data}")
            
            # 数据验证
            if total_tasks > 0 and completed_tasks > total_tasks:
                logger.warning(f"数据异常: 已完成任务数({completed_tasks})大于总任务数({total_tasks})")
            
            return json_response(result_data)

        except Exception as e:
            import traceback
            error_msg = f'获取测试计划统计失败: {str(e)}'
            print(f"=== 发生异常 ===")
            print(f"异常信息: {error_msg}")
            print(f"异常堆栈: {traceback.format_exc()}")
            logger.error(f"获取测试计划统计数据失败: {e}")
            logger.error(traceback.format_exc())
            return json_response(error='获取统计数据失败', detail=str(e))


# ============= Word模板管理相关API =============

@method_decorator(csrf_exempt, name='dispatch')
class WordTemplateView(View):
    """Word模板管理API"""

    @auth('document_management.word_template.view|model_storage.storage.view')
    def get(self, request):
        """获取模板列表或单个模板详情"""
        try:
            template_id = request.GET.get('id')
            # 检查是否为编辑模式（通过特殊参数标识）
            is_edit_mode = request.GET.get('edit_mode') == 'true'

            if template_id:
                # 获取单个模板详情
                template = WordTemplate.objects.get(id=template_id)

                # 获取模板变量 - 优先从TemplateVariable表获取，如果没有则从variables_config获取
                variables = TemplateVariable.objects.filter(template=template).order_by('sort_order')

                template_data = template.to_dict()

                if variables.exists():
                    # 从TemplateVariable表获取变量
                    variables_data = []
                    for var in variables:
                        var_dict = var.to_dict()
                        # 在编辑模式下，清空默认值以避免覆盖实例数据
                        if is_edit_mode:
                            var_dict['default_value'] = ''
                        variables_data.append(var_dict)
                    template_data['variables'] = variables_data
                else:
                    # 从variables_config字段获取变量
                    variables_config = json.loads(template.variables_config or '{}')
                    variables_list = []

                    for i, (var_name, var_config) in enumerate(variables_config.items()):
                        variables_list.append({
                            'variable_name': var_name,
                            'display_name': var_config.get('description', var_name),
                            'variable_type': var_config.get('type', 'text'),
                            'default_value': '' if is_edit_mode else var_config.get('default_value', ''),
                            'is_required': var_config.get('required', True),
                            'options': var_config.get('options', []),
                            'sort_order': i,
                            'group_name': var_config.get('group_name', '其他'),
                            'help_text': var_config.get('help_text', ''),
                            'placeholder': var_config.get('placeholder', ''),
                            'validation_rule': ''
                        })

                    template_data['variables'] = variables_list

                return JsonResponse({'data': template_data, 'error': ''})
            else:
                # 获取模板列表
                templates = WordTemplate.objects.all().order_by('-created_at')
                data = [template.to_dict() for template in templates]

                return JsonResponse({'data': data, 'error': ''})

        except WordTemplate.DoesNotExist:
            return JsonResponse({'data': None, 'error': '模板不存在'}, status=404)
        except Exception as e:
            logger.error(f"获取Word模板失败: {e}")
            return JsonResponse({'data': None, 'error': str(e)}, status=500)

    @auth('document_management.word_template.add|model_storage.storage.add')
    def post(self, request):
        """创建新模板"""
        try:
            # 检查是否是文件上传请求
            if request.FILES.get('file'):
                return self._handle_file_upload(request)
            else:
                # 处理JSON数据请求
                data = json.loads(request.body)

                # 输入验证
                validation_errors = self._validate_template_data(data)
                if validation_errors:
                    return JsonResponse({'data': None, 'error': '; '.join(validation_errors)}, status=400)

                # 创建模板
                template = WordTemplate.objects.create(
                    name=data['name'].strip(),
                    description=data.get('description', '').strip(),
                    template_file=data['template_file'],
                    template_type=data.get('template_type', 'test_guide'),
                    status=data.get('status', 'active'),
                    variables_config=json.dumps(data.get('variables_config', {})),
                    created_by=data.get('created_by', 'system')
                )

            # 创建模板变量
            variables_data = data.get('variables', [])
            for var_data in variables_data:
                TemplateVariable.objects.create(
                    template=template,
                    variable_name=var_data['variable_name'],
                    display_name=var_data['display_name'],
                    variable_type=var_data.get('variable_type', 'text'),
                    default_value=var_data.get('default_value', ''),
                    options=json.dumps(var_data.get('options', [])),
                    is_required=var_data.get('is_required', True),
                    validation_rule=var_data.get('validation_rule', ''),
                    sort_order=var_data.get('sort_order', 0),
                    group_name=var_data.get('group_name', ''),
                    help_text=var_data.get('help_text', ''),
                    placeholder=var_data.get('placeholder', '')
                )

            return JsonResponse({
                'data': {'id': template.id, 'message': '模板创建成功'},
                'error': ''
            })

        except Exception as e:
            logger.error(f"创建Word模板失败: {e}")
            return JsonResponse({'data': None, 'error': str(e)}, status=500)

    def _handle_file_upload(self, request):
        """处理文件上传"""
        import os
        import tempfile
        from django.conf import settings

        try:
            file = request.FILES.get('file')
            if not file:
                return JsonResponse({'data': None, 'error': '请选择要上传的文件'}, status=400)

            # 验证文件类型
            if not file.name.lower().endswith(('.docx', '.doc')):
                return JsonResponse({'data': None, 'error': '只支持.docx和.doc格式的Word文档'}, status=400)

            # 获取表单数据
            name = request.POST.get('name', file.name.replace('.docx', '').replace('.doc', ''))
            description = request.POST.get('description', f'从文件 {file.name} 导入的Word模板')
            template_type = request.POST.get('template_type', 'imported')
            status = request.POST.get('status', 'active')
            overwrite = request.POST.get('overwrite', 'false').lower() == 'true'  # 是否覆盖现有模板

            # 检查是否存在同名模板
            existing_template = WordTemplate.objects.filter(name=name).first()
            if existing_template and not overwrite:
                return JsonResponse({
                    'data': {
                        'duplicate_detected': True,
                        'existing_template': {
                            'id': existing_template.id,
                            'name': existing_template.name,
                            'description': existing_template.description,
                            'created_at': existing_template.created_at.strftime('%Y-%m-%d %H:%M:%S') if hasattr(existing_template, 'created_at') else '',
                            'original_filename': existing_template.original_filename or file.name
                        }
                    }
                }, status=200)

            # 创建上传目录
            upload_dir = os.path.join(settings.BASE_DIR, 'uploads', 'word_templates')
            os.makedirs(upload_dir, exist_ok=True)

            # 生成唯一文件名
            import uuid
            from datetime import datetime
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            file_extension = os.path.splitext(file.name)[1]
            unique_filename = f"{timestamp}_{uuid.uuid4().hex[:8]}{file_extension}"
            file_path = os.path.join(upload_dir, unique_filename)

            # 保存文件
            with open(file_path, 'wb') as f:
                for chunk in file.chunks():
                    f.write(chunk)

            # 如果是覆盖模式，更新现有模板
            if existing_template and overwrite:
                # 删除旧文件
                if existing_template.template_file and os.path.exists(existing_template.template_file):
                    try:
                        os.remove(existing_template.template_file)
                    except OSError:
                        pass  # 忽略删除失败的情况

                # 更新模板记录
                existing_template.description = description
                existing_template.template_file = file_path
                existing_template.original_filename = file.name
                existing_template.template_type = template_type
                existing_template.status = status
                existing_template.variables_config = '{}'
                existing_template.save()
                template = existing_template
                action_message = 'Word模板覆盖成功'
            else:
                # 创建新模板记录
                template = WordTemplate.objects.create(
                    name=name,
                    description=description,
                    template_file=file_path,
                    original_filename=file.name,
                    template_type=template_type,
                    status=status,
                    variables_config='{}',
                    created_by=getattr(request.user, 'nickname', 'system') if hasattr(request, 'user') else 'system'
                )
                action_message = 'Word模板导入成功'

            return JsonResponse({
                'data': {'id': template.id, 'message': action_message},
                'error': ''
            })

        except Exception as e:
            logger.error(f"文件上传失败: {e}")
            return JsonResponse({'data': None, 'error': str(e)}, status=500)

    @auth('document_management.word_template.edit|model_storage.storage.edit')
    def put(self, request):
        """更新模板"""
        try:
            data = json.loads(request.body)
            template_id = data.get('id')

            template = WordTemplate.objects.get(id=template_id)

            # 更新模板基本信息
            template.name = data.get('name', template.name)
            template.description = data.get('description', template.description)
            template.template_file = data.get('template_file', template.template_file)
            template.template_type = data.get('template_type', template.template_type)
            template.status = data.get('status', template.status)
            template.variables_config = json.dumps(data.get('variables_config', {}))
            template.save()

            # 更新模板变量（先删除旧的，再创建新的）
            if 'variables' in data:
                TemplateVariable.objects.filter(template=template).delete()

                variables_data = data.get('variables', [])
                for var_data in variables_data:
                    TemplateVariable.objects.create(
                        template=template,
                        variable_name=var_data['variable_name'],
                        display_name=var_data['display_name'],
                        variable_type=var_data.get('variable_type', 'text'),
                        default_value=var_data.get('default_value', ''),
                        options=json.dumps(var_data.get('options', [])),
                        is_required=var_data.get('is_required', True),
                        validation_rule=var_data.get('validation_rule', ''),
                        sort_order=var_data.get('sort_order', 0),
                        group_name=var_data.get('group_name', ''),
                        help_text=var_data.get('help_text', ''),
                        placeholder=var_data.get('placeholder', '')
                    )
            elif 'variables_config' in data:
                # 如果只有variables_config，也需要同步更新TemplateVariable表
                TemplateVariable.objects.filter(template=template).delete()

                variables_config = data.get('variables_config', {})
                sort_order = 0
                for var_name, var_config in variables_config.items():
                    TemplateVariable.objects.create(
                        template=template,
                        variable_name=var_name,
                        display_name=var_config.get('description', var_name).replace(f'({var_name})', '').strip(),
                        variable_type=var_config.get('type', 'text'),
                        default_value=var_config.get('default_value', ''),
                        options=json.dumps(var_config.get('options', [])),
                        is_required=var_config.get('required', True),
                        validation_rule='',
                        sort_order=sort_order,
                        group_name=var_config.get('group_name', '其他'),
                        help_text='',
                        placeholder=''
                    )
                    sort_order += 1

            return JsonResponse({
                'data': {'id': template.id, 'message': '模板更新成功'},
                'error': ''
            })

        except WordTemplate.DoesNotExist:
            return JsonResponse({'data': None, 'error': '模板不存在'}, status=404)
        except Exception as e:
            logger.error(f"更新Word模板失败: {e}")
            return JsonResponse({'data': None, 'error': str(e)}, status=500)

    @auth('document_management.word_template.del|model_storage.storage.del')
    def delete(self, request):
        """删除模板"""
        try:
            data = json.loads(request.body)
            template_id = data.get('id')

            template = WordTemplate.objects.get(id=template_id)
            template.delete()

            return JsonResponse({
                'data': {'message': '模板删除成功'},
                'error': ''
            })

        except WordTemplate.DoesNotExist:
            return JsonResponse({'data': None, 'error': '模板不存在'}, status=404)
        except Exception as e:
            logger.error(f"删除Word模板失败: {e}")
            return JsonResponse({'data': None, 'error': str(e)}, status=500)


@method_decorator(csrf_exempt, name='dispatch')
class WordTemplateReimportView(View):
    """Word模板重新导入API"""

    @auth('document_management.word_template.reimport|model_storage.storage.edit')
    def post(self, request):
        """重新导入模板文件"""
        import os
        import tempfile
        from django.conf import settings

        try:
            file = request.FILES.get('file')
            template_id = request.POST.get('template_id')
            preserve_defaults = request.POST.get('preserve_defaults', 'false').lower() == 'true'

            if not file:
                return JsonResponse({'data': None, 'error': '请选择要上传的文件'}, status=400)

            if not template_id:
                return JsonResponse({'data': None, 'error': '缺少模板ID'}, status=400)

            # 验证文件类型
            if not file.name.lower().endswith(('.docx', '.doc')):
                return JsonResponse({'data': None, 'error': '只支持.docx和.doc格式的Word文档'}, status=400)

            # 获取现有模板
            template = WordTemplate.objects.get(id=template_id)

            # 如果需要保留默认值，先保存现有的变量配置
            existing_variables = {}
            if preserve_defaults:
                variables = TemplateVariable.objects.filter(template=template)
                for var in variables:
                    existing_variables[var.variable_name] = {
                        'default_value': var.default_value,
                        'display_name': var.display_name,
                        'variable_type': var.variable_type,
                        'is_required': var.is_required,
                        'options': var.options,
                        'group_name': var.group_name,
                        'help_text': var.help_text,
                        'placeholder': var.placeholder,
                        'sort_order': var.sort_order
                    }

            # 删除旧文件（如果存在）
            if template.template_file and os.path.exists(template.template_file):
                try:
                    os.remove(template.template_file)
                except Exception as e:
                    logger.warning(f"删除旧模板文件失败: {e}")

            # 创建上传目录
            upload_dir = os.path.join(settings.BASE_DIR, 'uploads', 'word_templates')
            os.makedirs(upload_dir, exist_ok=True)

            # 生成唯一文件名
            import uuid
            from datetime import datetime
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            file_extension = os.path.splitext(file.name)[1]
            unique_filename = f"{timestamp}_{uuid.uuid4().hex[:8]}{file_extension}"
            file_path = os.path.join(upload_dir, unique_filename)

            # 保存新文件
            with open(file_path, 'wb') as f:
                for chunk in file.chunks():
                    f.write(chunk)

            # 更新模板文件路径和原文件名
            template.template_file = file_path
            template.original_filename = file.name
            template.save()

            # 重新解析模板变量
            try:
                # 调用变量解析功能
                parser = TemplateVariableParseView()
                parsed_variables = parser.parse_document_variables(file_path)

                # 删除旧的变量记录
                TemplateVariable.objects.filter(template=template).delete()

                # 创建新的变量记录
                for i, var_info in enumerate(parsed_variables):
                    var_name = var_info['variable_name']

                    # 如果需要保留默认值且存在旧配置，则使用旧配置
                    if preserve_defaults and var_name in existing_variables:
                        old_var = existing_variables[var_name]
                        TemplateVariable.objects.create(
                            template=template,
                            variable_name=var_name,
                            display_name=old_var['display_name'],
                            variable_type=old_var['variable_type'],
                            default_value=old_var['default_value'],
                            options=old_var['options'],
                            is_required=old_var['is_required'],
                            validation_rule='',
                            sort_order=old_var['sort_order'],
                            group_name=old_var['group_name'],
                            help_text=old_var['help_text'],
                            placeholder=old_var['placeholder']
                        )
                    else:
                        # 使用新解析的变量信息
                        TemplateVariable.objects.create(
                            template=template,
                            variable_name=var_name,
                            display_name=var_info.get('display_name', var_name),
                            variable_type=var_info.get('variable_type', 'text'),
                            default_value=var_info.get('default_value', ''),
                            options=json.dumps(var_info.get('options', [])),
                            is_required=var_info.get('is_required', True),
                            validation_rule='',
                            sort_order=var_info.get('sort_order', i),
                            group_name=var_info.get('group_name', '其他'),
                            help_text=var_info.get('help_text', ''),
                            placeholder=var_info.get('placeholder', '')
                        )

            except Exception as e:
                logger.warning(f"重新解析模板变量失败: {e}")

            return JsonResponse({
                'data': {'message': '模板文件重新导入成功'},
                'error': ''
            })

        except WordTemplate.DoesNotExist:
            return JsonResponse({'data': None, 'error': '模板不存在'}, status=404)
        except Exception as e:
            logger.error(f"重新导入模板文件失败: {e}")
            return JsonResponse({'data': None, 'error': str(e)}, status=500)

    def _validate_template_data(self, data):
        """验证模板数据"""
        errors = []

        # 验证必填字段
        if not data.get('name'):
            errors.append('模板名称不能为空')
        elif len(data['name'].strip()) < 2:
            errors.append('模板名称至少需要2个字符')
        elif len(data['name'].strip()) > 128:
            errors.append('模板名称不能超过128个字符')

        # 验证模板名称格式
        import re
        if data.get('name') and not re.match(r'^[a-zA-Z0-9\u4e00-\u9fa5_\-\s]+$', data['name'].strip()):
            errors.append('模板名称只能包含中英文、数字、下划线、连字符和空格')

        # 验证模板文件路径
        if not data.get('template_file'):
            errors.append('模板文件路径不能为空')
        elif not os.path.exists(data['template_file']):
            errors.append('模板文件不存在')

        # 验证模板类型
        valid_types = ['test_guide', 'report', 'manual', 'specification', 'imported']
        if data.get('template_type') and data['template_type'] not in valid_types:
            errors.append(f'无效的模板类型，支持的类型：{", ".join(valid_types)}')

        # 验证状态
        valid_statuses = ['active', 'inactive', 'draft']
        if data.get('status') and data['status'] not in valid_statuses:
            errors.append(f'无效的状态，支持的状态：{", ".join(valid_statuses)}')

        # 验证变量配置
        if data.get('variables_config'):
            try:
                if isinstance(data['variables_config'], str):
                    json.loads(data['variables_config'])
                elif not isinstance(data['variables_config'], dict):
                    errors.append('变量配置必须是有效的JSON对象')
            except json.JSONDecodeError:
                errors.append('变量配置JSON格式无效')

        # 验证描述长度
        if data.get('description') and len(data['description']) > 1000:
            errors.append('模板描述不能超过1000个字符')

        return errors

    def _validate_document_generation_security(self, instance):
        """验证文档生成的安全性"""
        # 检查模板文件路径安全性
        template_path = instance.template.template_file

        # 确保路径在允许的目录内
        allowed_dirs = [
            os.path.join(settings.BASE_DIR, 'storage', 'templates'),
            '/HDD_Raid/SVN_MODEL_REPO'
        ]

        is_safe_path = False
        for allowed_dir in allowed_dirs:
            try:
                if os.path.commonpath([template_path, allowed_dir]) == allowed_dir:
                    is_safe_path = True
                    break
            except ValueError:
                continue

        if not is_safe_path:
            raise SecurityError(f"模板文件路径不安全: {template_path}")

        # 检查变量值内容安全性
        variables_values = json.loads(instance.variables_values) if instance.variables_values else {}
        for var_name, var_value in variables_values.items():
            if isinstance(var_value, str):
                # 检查是否包含潜在的恶意内容
                if len(var_value) > 10000:  # 单个变量值不超过10K字符
                    raise ValueError(f"变量 {var_name} 的值过长")

                # 检查是否包含危险字符
                dangerous_patterns = ['<script', 'javascript:', 'vbscript:', 'onload=', 'onerror=']
                var_value_lower = var_value.lower()
                for pattern in dangerous_patterns:
                    if pattern in var_value_lower:
                        raise ValueError(f"变量 {var_name} 包含不安全的内容")

        # 检查富文本内容大小
        if instance.rich_content and len(instance.rich_content) > 500000:  # 500K字符限制
            raise ValueError("富文本内容过大，请减少内容或分段处理")

    def _cleanup_temp_files(self):
        """清理临时文件"""
        try:
            import tempfile
            import glob

            # 清理系统临时目录中的相关文件
            temp_dir = tempfile.gettempdir()
            patterns = [
                os.path.join(temp_dir, 'docx_temp_*.png'),
                os.path.join(temp_dir, 'docx_temp_*.jpg'),
                os.path.join(temp_dir, 'docx_temp_*.jpeg'),
                os.path.join(temp_dir, 'word_temp_*.docx'),
            ]

            for pattern in patterns:
                for temp_file in glob.glob(pattern):
                    try:
                        # 只删除超过1小时的临时文件
                        if os.path.exists(temp_file):
                            file_age = time.time() - os.path.getmtime(temp_file)
                            if file_age > 3600:  # 1小时
                                os.remove(temp_file)
                                logger.debug(f"清理临时文件: {temp_file}")
                    except Exception as e:
                        logger.warning(f"清理临时文件失败 {temp_file}: {e}")

        except Exception as e:
            logger.warning(f"临时文件清理过程出错: {e}")


@method_decorator(csrf_exempt, name='dispatch')
class DocumentInstanceView(View):
    """文档实例管理API"""

    @auth('document_management.document_instance.view|model_storage.storage.view')
    def get(self, request, pk=None):
        """获取文档实例列表或单个实例详情"""
        try:
            # 优先使用URL中的pk参数，然后是查询参数中的id
            instance_id = pk or request.GET.get('id')
            template_id = request.GET.get('template_id')

            if instance_id:
                # 获取单个实例详情
                instance = DocumentInstance.objects.get(id=instance_id)
                return JsonResponse({'data': instance.to_dict(), 'error': ''})
            else:
                # 获取实例列表
                instances = DocumentInstance.objects.all()
                if template_id:
                    instances = instances.filter(template_id=template_id)

                instances = instances.order_by('-created_at')
                data = [instance.to_dict() for instance in instances]

                return JsonResponse({'data': data, 'error': ''})

        except DocumentInstance.DoesNotExist:
            return JsonResponse({'data': None, 'error': '文档实例不存在'}, status=404)
        except Exception as e:
            logger.error(f"获取文档实例失败: {e}")
            return JsonResponse({'data': None, 'error': str(e)}, status=500)

    @auth('document_management.document_instance.add|model_storage.storage.add')
    def post(self, request):
        """创建新文档实例"""
        try:
            data = json.loads(request.body)

            template = WordTemplate.objects.get(id=data['template_id'])

            instance = DocumentInstance.objects.create(
                title=data['title'],
                template=template,
                variables_values=json.dumps(data.get('variables_values', {})),
                rich_content=data.get('rich_content', ''),
                created_by=data.get('created_by', 'system')
            )

            return JsonResponse({
                'data': {'id': instance.id, 'message': '文档实例创建成功'},
                'error': ''
            })

        except WordTemplate.DoesNotExist:
            return JsonResponse({'data': None, 'error': '模板不存在'}, status=404)
        except Exception as e:
            logger.error(f"创建文档实例失败: {e}")
            return JsonResponse({'data': None, 'error': str(e)}, status=500)

    @auth('document_management.document_instance.edit|model_storage.storage.edit')
    def put(self, request, pk=None):
        """更新文档实例"""
        try:
            data = json.loads(request.body)
            instance_id = pk or data.get('id')

            instance = DocumentInstance.objects.get(id=instance_id)

            instance.title = data.get('title', instance.title)
            instance.variables_values = json.dumps(data.get('variables_values', {}))
            instance.rich_content = data.get('rich_content', instance.rich_content)
            instance.status = data.get('status', instance.status)
            instance.save()

            return JsonResponse({
                'data': {'id': instance.id, 'message': '文档实例更新成功'},
                'error': ''
            })

        except DocumentInstance.DoesNotExist:
            return JsonResponse({'data': None, 'error': '文档实例不存在'}, status=404)
        except Exception as e:
            logger.error(f"更新文档实例失败: {e}")
            return JsonResponse({'data': None, 'error': str(e)}, status=500)

    @auth('document_management.document_instance.del|model_storage.storage.del')
    def delete(self, request):
        """删除文档实例"""
        try:
            data = json.loads(request.body)
            instance_id = data.get('id')

            instance = DocumentInstance.objects.get(id=instance_id)

            # 删除生成的文件
            if instance.generated_file_path and os.path.exists(instance.generated_file_path):
                os.remove(instance.generated_file_path)

            instance.delete()

            return JsonResponse({
                'data': {'message': '文档实例删除成功'},
                'error': ''
            })

        except DocumentInstance.DoesNotExist:
            return JsonResponse({'data': None, 'error': '文档实例不存在'}, status=404)
        except Exception as e:
            logger.error(f"删除文档实例失败: {e}")
            return JsonResponse({'data': None, 'error': str(e)}, status=500)


@method_decorator(csrf_exempt, name='dispatch')
class TemplateVariableParseView(View):
    """模板变量解析API"""

    @auth('document_management.word_template.view|model_storage.storage.view')
    def post(self, request):
        """解析Word文档中的变量"""
        try:
            data = json.loads(request.body)
            template_file_path = data.get('template_file_path')

            if not template_file_path or not os.path.exists(template_file_path):
                return JsonResponse({'data': None, 'error': '模板文件不存在'}, status=400)

            # 解析文档中的变量
            variables = self.parse_document_variables(template_file_path)

            return JsonResponse({
                'data': {'variables': variables},
                'error': ''
            })

        except Exception as e:
            logger.error(f"解析模板变量失败: {e}")
            return JsonResponse({'data': None, 'error': str(e)}, status=500)

    def parse_document_variables(self, file_path):
        """解析Word文档中的${xxx}格式变量"""
        import re
        from docx import Document

        try:
            doc = Document(file_path)
            variables = set()

            # 解析段落中的变量 - 支持${变量名}格式
            for paragraph in doc.paragraphs:
                text = paragraph.text
                matches = re.findall(r'\$\{([^}]+)\}', text)
                variables.update(matches)

            # 解析表格中的变量 - 支持${变量名}格式
            for table in doc.tables:
                for row in table.rows:
                    for cell in row.cells:
                        text = cell.text
                        matches = re.findall(r'\$\{([^}]+)\}', text)
                        variables.update(matches)

            # 转换为变量配置格式
            variable_configs = []
            for i, var in enumerate(sorted(variables)):
                # 根据变量名推断类型和显示名称
                var_type, display_name, group_name = self.infer_variable_type(var)

                # 为不同类型设置默认选项
                options = []
                default_value = ''
                if var_type == 'select':
                    if '推理' in var or '训练' in var:
                        options = ['推理', '训练', '推理/训练']
                        default_value = '推理'

                variable_configs.append({
                    'variable_name': var,
                    'display_name': display_name,
                    'variable_type': var_type,
                    'default_value': default_value,
                    'options': options,
                    'is_required': True,
                    'validation_rule': '',
                    'sort_order': i,
                    'group_name': group_name,
                    'help_text': f'请输入{display_name}',
                    'placeholder': f'请输入{display_name}'
                })

            return variable_configs

        except Exception as e:
            logger.error(f"解析文档变量失败: {e}")
            return []

    def infer_variable_type(self, variable_name):
        """根据变量名推断变量类型和显示名称"""
        var_lower = variable_name.lower()

        # GPU相关
        if 'gpu' in var_lower or '加速卡' in variable_name:
            return 'gpu_model', f'GPU型号({variable_name})', 'GPU配置'

        # 厂商相关
        if '厂商' in variable_name or 'vendor' in var_lower:
            return 'vendor', f'厂商名称({variable_name})', '基本信息'

        # 模型相关
        if '模型' in variable_name or 'model' in var_lower:
            return 'text', f'模型名称({variable_name})', '基本信息'

        # 机型相关
        if '机型' in variable_name:
            return 'text', f'机型信息({variable_name})', '基本信息'

        # 日期相关
        if '时间' in variable_name or '日期' in variable_name or 'date' in var_lower or 'time' in var_lower:
            return 'date', f'日期({variable_name})', '时间配置'

        # 选择类型（推理/训练）
        if '推理' in variable_name or '训练' in variable_name or 'inference' in var_lower or 'training' in var_lower:
            # 为推理/训练类型提供默认选项
            var_type = 'select'
            display_name = f'类型选择({variable_name})'
            group_name = '测试配置'
            return var_type, display_name, group_name

        # 富文本类型（包含"正文"、"设置"等关键词）
        if '正文' in variable_name or '设置正文' in variable_name or '部署正文' in variable_name or '执行正文' in variable_name:
            return 'richtext', f'富文本内容({variable_name})', '内容编辑'

        # 版本号相关
        if '版本号' in variable_name or 'version' in var_lower:
            return 'text', f'版本号({variable_name})', '文件信息'

        # 文件相关
        if 'file' in var_lower or '文件' in variable_name:
            return 'text', f'文件名({variable_name})', '文件信息'

        # 获取方式相关
        if '获取方式' in variable_name:
            return 'text', f'获取方式({variable_name})', '文件信息'

        # 内容描述相关（非正文的内容）
        if '内容' in variable_name and '正文' not in variable_name:
            return 'textarea', f'内容描述({variable_name})', '文件信息'

        # 多行文本类型（包含"备注"、"注释"等关键词）
        if '备注' in variable_name or '注释' in variable_name or '详情' in variable_name or 'note' in var_lower or 'remark' in var_lower:
            return 'textarea', f'多行文本({variable_name})', '其他'

        # 默认文本类型
        return 'text', variable_name, '其他'


@method_decorator(csrf_exempt, name='dispatch')
class WordTemplateGenerateView(View):
    """Word模板直接生成文档API"""

    def _cleanup_temp_files(self):
        """清理临时文件"""
        try:
            import tempfile
            import glob

            # 清理系统临时目录中的相关文件
            temp_dir = tempfile.gettempdir()
            patterns = [
                os.path.join(temp_dir, 'docx_temp_*.png'),
                os.path.join(temp_dir, 'docx_temp_*.jpg'),
                os.path.join(temp_dir, 'docx_temp_*.jpeg'),
                os.path.join(temp_dir, 'word_temp_*.docx'),
            ]

            for pattern in patterns:
                for temp_file in glob.glob(pattern):
                    try:
                        # 只删除超过1小时的临时文件
                        if os.path.exists(temp_file):
                            file_age = time.time() - os.path.getmtime(temp_file)
                            if file_age > 3600:  # 1小时
                                os.remove(temp_file)
                                logger.debug(f"清理临时文件: {temp_file}")
                    except Exception as e:
                        logger.warning(f"清理临时文件失败 {temp_file}: {e}")

        except Exception as e:
            logger.warning(f"临时文件清理过程出错: {e}")

    def _validate_document_generation_security(self, instance):
        """验证文档生成的安全性"""
        # 检查模板文件路径安全性
        template_path = instance.template.template_file

        # 确保路径在允许的目录内
        allowed_dirs = [
            os.path.join(settings.BASE_DIR, 'storage', 'templates'),
            os.path.join(settings.BASE_DIR, 'spug_api', 'uploads', 'word_templates'),
            '/HDD_Raid/SVN_MODEL_REPO'
        ]

        is_safe_path = False
        for allowed_dir in allowed_dirs:
            try:
                if os.path.commonpath([template_path, allowed_dir]) == allowed_dir:
                    is_safe_path = True
                    break
            except ValueError:
                continue

        if not is_safe_path:
            raise SecurityError(f"模板文件路径不安全: {template_path}")

        # 检查变量值内容安全性
        variables_values = json.loads(instance.variables_values) if instance.variables_values else {}
        for var_name, var_value in variables_values.items():
            if isinstance(var_value, str):
                # 检查是否包含潜在的恶意内容
                if len(var_value) > 10000:  # 单个变量值不超过10K字符
                    raise ValueError(f"变量 {var_name} 的值过长")

                # 检查是否包含危险字符
                dangerous_patterns = ['<script', 'javascript:', 'vbscript:', 'onload=', 'onerror=']
                var_value_lower = var_value.lower()
                for pattern in dangerous_patterns:
                    if pattern in var_value_lower:
                        raise ValueError(f"变量 {var_name} 包含不安全的内容")

        # 检查富文本内容大小
        if instance.rich_content and len(instance.rich_content) > 500000:  # 500K字符限制
            raise ValueError("富文本内容过大，请减少内容或分段处理")

    @auth('document_management.document_generator.generate|model_storage.storage.add')
    def post(self, request):
        """直接从模板生成Word文档"""
        try:
            data = json.loads(request.body)
            template_id = data.get('template_id')
            title = data.get('title', '生成的文档')
            variables = data.get('variables', {})
            content = data.get('content', '')
            save_instance = data.get('save_instance', False)  # 新增参数：是否保存文档实例

            # 获取模板
            template = WordTemplate.objects.get(id=template_id)

            if save_instance:
                # 只有明确要求保存时才创建文档实例
                instance = DocumentInstance.objects.create(
                    title=title,
                    template=template,
                    variables_values=json.dumps(variables),
                    rich_content=content,
                    created_by=getattr(request.user, 'nickname', 'system') if hasattr(request, 'user') else 'system'
                )
                
                # 生成文档
                generated_file_path = self.generate_word_document(instance)
                
                # 更新实例信息
                instance.generated_file_path = generated_file_path
                instance.file_size = os.path.getsize(generated_file_path) if os.path.exists(generated_file_path) else 0
                instance.status = 'generated'
                instance.save()
                
                # 生成下载URL
                download_url = f'/api/model-storage/documents/download/?instance_id={instance.id}'
                
                response_data = {
                    'id': instance.id,
                    'file_path': generated_file_path,
                    'download_url': download_url,
                    'message': '文档生成成功'
                }
            else:
                # 不保存实例，直接生成临时文档
                class TempInstance:
                    def __init__(self, template, variables_values, rich_content, title):
                        self.template = template
                        self.variables_values = variables_values
                        self.rich_content = rich_content
                        self.title = title
                
                temp_instance = TempInstance(
                    template=template,
                    variables_values=json.dumps(variables),
                    rich_content=content,
                    title=title
                )
                
                # 生成文档到临时路径
                generated_file_path = self.generate_word_document_temp(temp_instance)
                
                # 生成临时下载URL（不依赖实例ID）
                import uuid
                temp_id = str(uuid.uuid4())
                download_url = f'/api/model-storage/documents/download-temp/?file_path={generated_file_path}&temp_id={temp_id}'
                
                response_data = {
                    'file_path': generated_file_path,
                    'download_url': download_url,
                    'message': '文档生成成功（未保存实例）',
                    'temp_file': True
                }

            # 更新模板使用次数
            from django.utils import timezone
            template.usage_count = (template.usage_count or 0) + 1
            template.last_used_at = timezone.now()
            template.save()

            return JsonResponse({
                'data': response_data,
                'error': ''
            })

        except WordTemplate.DoesNotExist:
            return JsonResponse({'data': None, 'error': '模板不存在'}, status=404)
        except Exception as e:
            logger.error(f"生成文档失败: {e}")
            return JsonResponse({'data': None, 'error': str(e)}, status=500)

    def generate_word_document_temp(self, temp_instance):
        """生成临时Word文档（不保存实例）"""
        import tempfile
        import os
        
        # 生成临时文件路径
        temp_dir = tempfile.gettempdir()
        temp_filename = f"temp_doc_{temp_instance.title}_{int(time.time())}.docx"
        temp_file_path = os.path.join(temp_dir, temp_filename)
        
        # 调用原有的文档生成逻辑，但保存到临时路径
        generated_path = self._generate_word_document_internal(temp_instance, temp_file_path)
        return generated_path
    
    def generate_word_document(self, instance):
        """生成Word文档 - 保持模板但优化富文本处理"""
        # 生成到正常路径
        return self._generate_word_document_internal(instance)
    
    def _generate_word_document_internal(self, instance, custom_output_path=None):
        """内部文档生成方法"""
        from docx import Document
        from docx.shared import Inches, RGBColor, Pt
        from docx.enum.text import WD_ALIGN_PARAGRAPH
        import re
        import time

        try:
            # 安全性检查 - 内部项目暂时禁用
            # self._validate_document_generation_security(instance)

            # 加载模板文档
            template_path = instance.template.template_file
            if not os.path.exists(template_path):
                raise FileNotFoundError(f"模板文件不存在: {template_path}")

            # 检查模板文件大小
            template_size = os.path.getsize(template_path)
            if template_size > 50 * 1024 * 1024:  # 50MB限制
                raise ValueError("模板文件过大，请使用小于50MB的模板")

            doc = Document(template_path)

            # 获取变量值
            variables_values = json.loads(instance.variables_values) if instance.variables_values else {}

            # 替换段落中的变量
            for paragraph in doc.paragraphs:
                self.replace_paragraph_variables_with_rich_text_support(paragraph, variables_values)

            # 替换表格中的变量
            for table in doc.tables:
                for row in table.rows:
                    for cell in row.cells:
                        for paragraph in cell.paragraphs:
                            self.replace_paragraph_variables_with_rich_text_support(paragraph, variables_values)

            # 如果有富文本内容，添加框框处理
            if instance.rich_content:
                self.add_rich_content_with_frame(doc, instance.rich_content)

            # 生成输出文件路径
            if custom_output_path:
                # 使用自定义路径（临时文件）
                output_path = custom_output_path
                # 确保目录存在
                os.makedirs(os.path.dirname(output_path), exist_ok=True)
            else:
                # 使用正常路径（保存实例）
                output_dir = os.path.join(settings.BASE_DIR, 'storage', 'generated_documents')
                os.makedirs(output_dir, exist_ok=True)
                
                timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
                filename = f"{instance.title}_{timestamp}.docx"
                output_path = os.path.join(output_dir, filename)

            # 保存文档
            doc.save(output_path)

            # 检查生成的文档大小
            generated_size = os.path.getsize(output_path)
            if generated_size > 100 * 1024 * 1024:  # 100MB限制
                os.remove(output_path)
                raise ValueError("生成的文档过大，请减少内容")

            # 清理临时文件
            self._cleanup_temp_files()

            return output_path

        except Exception as e:
            logger.error(f"生成Word文档失败: {e}")
            # 清理可能产生的临时文件
            self._cleanup_temp_files()
            raise

    def replace_paragraph_variables_with_rich_text_support(self, paragraph, variables_values):
        """替换段落中的变量，保持原始格式"""
        try:
            import re

            # 获取段落文本
            full_text = paragraph.text
            if not full_text or '${' not in full_text:
                return

            # 查找变量占位符
            pattern = r'\$\{([^}]+)\}'
            matches = re.findall(pattern, full_text)

            if not matches:
                return

            # 检查是否需要替换
            need_replace = False
            for var_name in matches:
                if var_name in variables_values:
                    need_replace = True
                    break

            if not need_replace:
                return

            # 保存原始格式信息
            original_runs = []
            for run in paragraph.runs:
                original_runs.append({
                    'text': run.text,
                    'font_name': run.font.name,
                    'font_size': run.font.size,
                    'bold': run.bold,
                    'italic': run.italic,
                    'underline': run.underline,
                    'color': run.font.color.rgb if run.font.color.rgb else None
                })

            # 执行变量替换
            new_text = full_text
            for var_name in matches:
                if var_name in variables_values:
                    var_value = str(variables_values[var_name])
                    # 清理HTML标签（如果有）
                    import re
                    clean_value = re.sub(r'<[^>]+>', '', var_value)
                    new_text = new_text.replace(f'${{{var_name}}}', clean_value)

            # 清空段落并重新添加内容
            paragraph.clear()

            # 如果有原始格式，尝试保持
            if original_runs and new_text:
                run = paragraph.add_run(new_text)
                # 应用第一个run的格式
                first_run = original_runs[0]
                if first_run['font_name']:
                    run.font.name = first_run['font_name']
                if first_run['font_size']:
                    run.font.size = first_run['font_size']
                if first_run['bold']:
                    run.bold = first_run['bold']
                if first_run['italic']:
                    run.italic = first_run['italic']
                if first_run['underline']:
                    run.underline = first_run['underline']
                if first_run['color']:
                    run.font.color.rgb = first_run['color']
            else:
                paragraph.add_run(new_text)

        except Exception as e:
            logger.warning(f"替换段落变量失败: {e}")
            # 降级处理：简单文本替换
            try:
                import re
                new_text = paragraph.text
                for var_name, var_value in variables_values.items():
                    placeholder = f'${{{var_name}}}'
                    if placeholder in new_text:
                        clean_value = re.sub(r'<[^>]+>', '', str(var_value))
                        new_text = new_text.replace(placeholder, clean_value)
                paragraph.clear()
                paragraph.add_run(new_text)
            except:
                pass

    def add_rich_content_with_frame(self, doc, rich_content):
        """添加带框框的富文本内容"""
        try:
            from docx.shared import Pt, Inches, RGBColor
            from docx.enum.text import WD_ALIGN_PARAGRAPH
            from docx.enum.table import WD_TABLE_ALIGNMENT
            import re

            if not rich_content or not rich_content.strip():
                return

            # 添加空行分隔
            doc.add_paragraph()

            # 创建单行单列的表格作为框框
            table = doc.add_table(rows=1, cols=1)
            table.alignment = WD_TABLE_ALIGNMENT.LEFT
            table.style = 'Table Grid'

            # 设置表格宽度
            table.columns[0].width = Inches(6)

            # 获取单元格
            cell = table.cell(0, 0)

            # 设置单元格边框
            from docx.oxml.shared import qn
            from docx.oxml import parse_xml
            tcPr = cell._tc.get_or_add_tcPr()

            # 设置边框
            tcBorders = parse_xml(r'<w:tcBorders xmlns:w="http://schemas.openxmlformats.org/wordprocessingml/2006/main">'
                                 r'<w:top w:val="single" w:sz="4" w:space="0" w:color="000000"/>'
                                 r'<w:left w:val="single" w:sz="4" w:space="0" w:color="000000"/>'
                                 r'<w:bottom w:val="single" w:sz="4" w:space="0" w:color="000000"/>'
                                 r'<w:right w:val="single" w:sz="4" w:space="0" w:color="000000"/>'
                                 r'</w:tcBorders>')
            tcPr.append(tcBorders)

            # 设置单元格内边距
            from docx.shared import Twips
            tcMar = parse_xml(r'<w:tcMar xmlns:w="http://schemas.openxmlformats.org/wordprocessingml/2006/main">'
                             r'<w:top w:w="144" w:type="dxa"/>'
                             r'<w:left w:w="144" w:type="dxa"/>'
                             r'<w:bottom w:w="144" w:type="dxa"/>'
                             r'<w:right w:w="144" w:type="dxa"/>'
                             r'</w:tcMar>')
            tcPr.append(tcMar)

            # 清理富文本内容
            clean_content = self.clean_rich_text_content(rich_content)

            # 将清理后的内容添加到单元格
            self.add_formatted_content_to_cell(cell, clean_content)

        except Exception as e:
            logger.error(f"添加带框框的富文本内容失败: {e}")
            # 降级：添加普通段落
            try:
                clean_content = self.clean_rich_text_for_inline(rich_content)
                if clean_content.strip():
                    para = doc.add_paragraph()
                    run = para.add_run(clean_content)
                    run.font.name = '黑体'
                    run.font.size = Pt(12)
            except:
                pass

    def clean_rich_text_content(self, rich_content):
        """清理富文本内容"""
        import re
        if not rich_content:
            return ""

        # 简单的HTML标签清理
        clean_content = re.sub(r'<[^>]+>', '', rich_content)
        # 清理多余的空白字符
        clean_content = re.sub(r'\s+', ' ', clean_content).strip()
        return clean_content

    def clean_rich_text_for_inline(self, rich_content):
        """为内联显示清理富文本内容"""
        return self.clean_rich_text_content(rich_content)

    def add_formatted_content_to_cell(self, cell, content):
        """向单元格添加格式化内容"""
        try:
            from docx.shared import Pt

            # 清空单元格默认段落
            cell.paragraphs[0].clear()

            # 添加内容
            paragraph = cell.paragraphs[0]
            run = paragraph.add_run(content)
            run.font.name = '宋体'
            run.font.size = Pt(12)

        except Exception as e:
            logger.warning(f"添加格式化内容到单元格失败: {e}")
            # 降级处理
            cell.text = content

    def setup_clean_document_style(self, doc):
        """设置纯净的文档样式"""
        try:
            from docx.shared import Pt, Inches
            from docx.enum.style import WD_STYLE_TYPE
            from docx.enum.text import WD_ALIGN_PARAGRAPH

            # 设置页面边距
            sections = doc.sections
            for section in sections:
                section.top_margin = Inches(1)
                section.bottom_margin = Inches(1)
                section.left_margin = Inches(1)
                section.right_margin = Inches(1)

            # 设置默认字体样式
            style = doc.styles['Normal']
            font = style.font
            font.name = '宋体'
            font.size = Pt(12)

            # 设置段落样式 - 统一左对齐
            paragraph_format = style.paragraph_format
            paragraph_format.space_after = Pt(6)
            paragraph_format.line_spacing = 1.15
            paragraph_format.alignment = WD_ALIGN_PARAGRAPH.LEFT  # 强制左对齐

        except Exception as e:
            logger.warning(f"设置文档样式失败: {e}")

    def add_basic_info_table(self, doc, variables_values):
        """添加基本信息表格"""
        try:
            from docx.shared import Pt
            from docx.enum.table import WD_TABLE_ALIGNMENT

            if not variables_values:
                return

            # 添加基本信息标题
            heading = doc.add_heading('基本信息', level=1)

            # 创建表格
            table = doc.add_table(rows=1, cols=2)
            table.style = 'Table Grid'
            table.alignment = WD_TABLE_ALIGNMENT.LEFT

            # 设置表头
            hdr_cells = table.rows[0].cells
            hdr_cells[0].text = '项目'
            hdr_cells[1].text = '内容'

            # 添加变量数据
            for var_name, var_value in variables_values.items():
                if var_value and str(var_value).strip():
                    row_cells = table.add_row().cells
                    row_cells[0].text = var_name
                    row_cells[1].text = str(var_value)

        except Exception as e:
            logger.warning(f"添加基本信息表格失败: {e}")

    def add_template_content_clean(self, doc, template_path, variables_values):
        """添加清理后的模板内容"""
        try:
            from docx import Document as DocxDocument

            if not os.path.exists(template_path):
                return

            template_doc = DocxDocument(template_path)

            # 添加模板内容标题
            doc.add_heading('模板内容', level=1)

            # 处理模板段落
            for paragraph in template_doc.paragraphs:
                if paragraph.text.strip():
                    new_para = doc.add_paragraph()
                    new_para.text = paragraph.text

                    # 替换变量
                    self.replace_paragraph_variables_with_rich_text_support(new_para, variables_values)

        except Exception as e:
            logger.warning(f"添加模板内容失败: {e}")

    def add_clean_rich_content(self, doc, rich_content):
        """添加清理后的富文本内容"""
        try:
            from docx.shared import Pt
            from docx.enum.text import WD_ALIGN_PARAGRAPH

            # 添加富文本内容标题
            heading = doc.add_heading('详细内容', level=1)
            heading.alignment = WD_ALIGN_PARAGRAPH.LEFT

            # 清理富文本内容
            clean_content = self.clean_rich_text_content(rich_content)

            if clean_content.strip():
                para = doc.add_paragraph()
                run = para.add_run(clean_content)
                run.font.name = '宋体'
                run.font.size = Pt(12)

        except Exception as e:
            logger.warning(f"添加富文本内容失败: {e}")

    def generate_clean_word_document(self, instance):
        """全新的纯净Word文档生成方案"""
        from docx import Document
        from docx.shared import Inches, Pt, RGBColor
        from docx.enum.text import WD_ALIGN_PARAGRAPH
        from docx.enum.style import WD_STYLE_TYPE
        import re

        try:
            # 创建全新的文档（不使用模板，避免格式污染）
            doc = Document()

            # 设置页面和字体
            self.setup_clean_document_style(doc)

            # 添加文档标题（保持居中）
            title_para = doc.add_heading(instance.title, level=0)
            title_para.alignment = WD_ALIGN_PARAGRAPH.CENTER

            # 添加分隔线（改为左对齐）
            separator_para = doc.add_paragraph("=" * 50)
            separator_para.alignment = WD_ALIGN_PARAGRAPH.LEFT
            doc.add_paragraph()  # 空行

            # 获取变量值并清理
            variables_values = json.loads(instance.variables_values) if instance.variables_values else {}

            # 添加基本信息表格
            self.add_basic_info_table(doc, variables_values)

            # 处理模板内容（如果有）
            if instance.template and instance.template.template_file:
                self.add_template_content_clean(doc, instance.template.template_file, variables_values)

            # 添加富文本内容（清理后）
            if instance.rich_content:
                self.add_clean_rich_content(doc, instance.rich_content)

            # 生成输出文件路径
            output_dir = os.path.join(settings.BASE_DIR, 'storage', 'generated_documents')
            os.makedirs(output_dir, exist_ok=True)

            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            filename = f"{instance.title}_{timestamp}_clean.docx"
            output_path = os.path.join(output_dir, filename)

            # 保存文档
            doc.save(output_path)

            return output_path

        except Exception as e:
            logger.error(f"生成纯净Word文档失败: {e}")
            raise

    def setup_clean_document_style(self, doc):
        """设置纯净的文档样式"""
        try:
            from docx.shared import Pt, Inches
            from docx.enum.style import WD_STYLE_TYPE
            from docx.enum.text import WD_ALIGN_PARAGRAPH

            # 设置页面边距
            sections = doc.sections
            for section in sections:
                section.top_margin = Inches(1)
                section.bottom_margin = Inches(1)
                section.left_margin = Inches(1)
                section.right_margin = Inches(1)

            # 设置默认字体样式
            style = doc.styles['Normal']
            font = style.font
            font.name = '宋体'
            font.size = Pt(12)

            # 设置段落样式 - 统一左对齐
            paragraph_format = style.paragraph_format
            paragraph_format.space_after = Pt(6)
            paragraph_format.line_spacing = 1.15
            paragraph_format.alignment = WD_ALIGN_PARAGRAPH.LEFT  # 强制左对齐

        except Exception as e:
            logger.warning(f"设置文档样式失败: {e}")

    def add_left_aligned_paragraph(self, doc, text=""):
        """添加左对齐的段落"""
        from docx.enum.text import WD_ALIGN_PARAGRAPH

        para = doc.add_paragraph(text)
        para.alignment = WD_ALIGN_PARAGRAPH.LEFT
        return para

    def add_left_aligned_heading(self, doc, text, level=1):
        """添加左对齐的标题"""
        from docx.enum.text import WD_ALIGN_PARAGRAPH

        heading = doc.add_heading(text, level=level)
        heading.alignment = WD_ALIGN_PARAGRAPH.LEFT
        return heading

    def add_basic_info_table(self, doc, variables_values):
        """添加基本信息表格"""
        try:
            from docx.shared import Pt
            from docx.enum.table import WD_TABLE_ALIGNMENT
            from docx.enum.text import WD_ALIGN_PARAGRAPH

            # 添加基本信息标题（左对齐）
            info_title = doc.add_heading('基本信息', level=1)
            info_title.alignment = WD_ALIGN_PARAGRAPH.LEFT

            # 创建信息表格
            table = doc.add_table(rows=1, cols=2)
            table.alignment = WD_TABLE_ALIGNMENT.LEFT  # 表格左对齐
            table.style = 'Table Grid'

            # 设置表头
            header_cells = table.rows[0].cells
            header_cells[0].text = '项目'
            header_cells[1].text = '内容'

            # 设置表头样式
            for cell in header_cells:
                for paragraph in cell.paragraphs:
                    paragraph.alignment = WD_ALIGN_PARAGRAPH.LEFT  # 表头段落左对齐
                    for run in paragraph.runs:
                        run.font.name = '宋体'
                        run.font.size = Pt(12)
                        run.bold = True

            # 添加变量信息
            for key, value in variables_values.items():
                if key and value:  # 只添加有值的变量
                    row_cells = table.add_row().cells
                    row_cells[0].text = str(key)

                    # 清理值内容
                    clean_value = self.clean_text_content(str(value))
                    row_cells[1].text = clean_value

                    # 设置单元格样式
                    for cell in row_cells:
                        for paragraph in cell.paragraphs:
                            paragraph.alignment = WD_ALIGN_PARAGRAPH.LEFT  # 单元格段落左对齐
                            for run in paragraph.runs:
                                run.font.name = '宋体'
                                run.font.size = Pt(12)

            doc.add_paragraph()  # 添加空行

        except Exception as e:
            logger.error(f"添加基本信息表格失败: {e}")

    def add_template_content_clean(self, doc, template_path, variables_values):
        """添加模板内容，保持原始格式"""
        try:
            from docx import Document as DocxDocument

            # 读取模板内容
            template_doc = DocxDocument(template_path)

            # 添加模板内容标题
            doc.add_heading('模板内容', level=1)

            # 复制模板段落，保持原始格式
            for paragraph in template_doc.paragraphs:
                if paragraph.text.strip():
                    # 创建新段落
                    new_para = doc.add_paragraph()

                    # 复制段落格式
                    new_para.alignment = paragraph.alignment
                    new_para.paragraph_format.space_before = paragraph.paragraph_format.space_before
                    new_para.paragraph_format.space_after = paragraph.paragraph_format.space_after
                    new_para.paragraph_format.line_spacing = paragraph.paragraph_format.line_spacing

                    # 复制每个run，保持格式
                    for run in paragraph.runs:
                        new_run = new_para.add_run(run.text)

                        # 复制字体格式
                        if run.font.name:
                            new_run.font.name = run.font.name
                        if run.font.size:
                            new_run.font.size = run.font.size
                        new_run.bold = run.bold
                        new_run.italic = run.italic
                        new_run.underline = run.underline
                        if run.font.color.rgb:
                            new_run.font.color.rgb = run.font.color.rgb

                    # 替换变量，保持格式
                    self.replace_paragraph_variables_with_rich_text_support(new_para, variables_values)

            doc.add_paragraph()  # 添加空行

        except Exception as e:
            logger.error(f"添加模板内容失败: {e}")
            # 降级处理
            try:
                from docx import Document as DocxDocument
                template_doc = DocxDocument(template_path)

                for paragraph in template_doc.paragraphs:
                    if paragraph.text.strip():
                        text = paragraph.text
                        for var_name, var_value in variables_values.items():
                            placeholder = f"${{{var_name}}}"
                            clean_value = self.clean_text_content(str(var_value))
                            text = text.replace(placeholder, clean_value)

                        if text.strip():
                            doc.add_paragraph(text.strip())
            except:
                pass

    def add_clean_rich_content(self, doc, rich_content):
        """添加清理后的富文本内容"""
        try:
            from docx.shared import Pt
            from docx.enum.text import WD_ALIGN_PARAGRAPH

            # 添加富文本内容标题
            heading = doc.add_heading('详细内容', level=1)
            heading.alignment = WD_ALIGN_PARAGRAPH.LEFT  # 标题也左对齐

            # 清理富文本内容
            clean_content = self.clean_text_content(rich_content)

            if clean_content.strip():
                # 按段落分割
                paragraphs = clean_content.split('\n\n')

                for para_text in paragraphs:
                    para_text = para_text.strip()
                    if para_text:
                        # 检查是否是标题
                        if para_text.startswith('#'):
                            # 处理标题
                            level = min(para_text.count('#'), 3)
                            title_text = para_text.lstrip('#').strip()
                            if title_text:
                                heading = doc.add_heading(title_text, level=level)
                                heading.alignment = WD_ALIGN_PARAGRAPH.LEFT  # 标题左对齐
                        else:
                            # 处理普通段落
                            para = doc.add_paragraph()
                            para.alignment = WD_ALIGN_PARAGRAPH.LEFT  # 段落左对齐

                            # 处理格式化文本
                            self.add_formatted_text_to_paragraph(para, para_text)

        except Exception as e:
            logger.error(f"添加富文本内容失败: {e}")

    def clean_text_content(self, content):
        """彻底清理文本内容"""
        if not content:
            return ""

        import re

        # 转换为字符串
        text = str(content)

        # 1. 移除HTML标签，但保留内容
        text = re.sub(r'<br\s*/?>', '\n', text)  # br标签转换为换行
        text = re.sub(r'<p[^>]*>', '\n', text)   # p标签转换为换行
        text = re.sub(r'</p>', '\n', text)
        text = re.sub(r'<h[1-6][^>]*>', '\n# ', text)  # 标题标签
        text = re.sub(r'</h[1-6]>', '\n', text)
        text = re.sub(r'<li[^>]*>', '\n• ', text)  # 列表项
        text = re.sub(r'</li>', '', text)
        text = re.sub(r'<[^>]+>', '', text)  # 移除所有其他HTML标签

        # 2. 解码HTML实体
        html_entities = {
            '&amp;': '&', '&lt;': '<', '&gt;': '>', '&quot;': '"',
            '&#39;': "'", '&nbsp;': ' ', '&copy;': '©', '&reg;': '®'
        }
        for entity, char in html_entities.items():
            text = text.replace(entity, char)

        # 3. 规范化空白字符
        text = re.sub(r'\r\n', '\n', text)  # 统一换行符
        text = re.sub(r'\r', '\n', text)
        text = re.sub(r'\t', '    ', text)  # 制表符转空格
        text = re.sub(r'[ ]+', ' ', text)   # 多个空格合并
        text = re.sub(r'\n\s*\n\s*\n+', '\n\n', text)  # 多个空行合并

        # 4. 移除控制字符
        text = re.sub(r'[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]', '', text)

        # 5. 清理首尾空白
        text = text.strip()

        return text

    def replace_paragraph_variables_with_rich_text_support(self, paragraph, variables_values):
        """替换段落中的变量，保持原始格式"""
        try:
            import re

            # 获取段落文本
            full_text = paragraph.text
            if not full_text or '${' not in full_text:
                return

            # 查找变量占位符
            pattern = r'\$\{([^}]+)\}'
            matches = re.findall(pattern, full_text)

            if not matches:
                return

            # 检查是否需要替换
            need_replace = False
            for var_name in matches:
                if var_name in variables_values:
                    need_replace = True
                    break

            if not need_replace:
                return

            # 保存原始格式信息
            original_runs = []
            for run in paragraph.runs:
                original_runs.append({
                    'text': run.text,
                    'font_name': run.font.name,
                    'font_size': run.font.size,
                    'bold': run.bold,
                    'italic': run.italic,
                    'underline': run.underline,
                    'color': run.font.color.rgb if run.font.color.rgb else None
                })

            # 替换变量
            new_text = full_text
            for var_name in matches:
                placeholder = f"${{{var_name}}}"
                if var_name in variables_values:
                    var_value = variables_values[var_name]

                    # 处理富文本内容
                    if isinstance(var_value, str) and ('<' in var_value and '>' in var_value):
                        clean_value = self.clean_rich_text_for_inline(var_value)
                    else:
                        clean_value = str(var_value)

                    new_text = new_text.replace(placeholder, clean_value)

            # 如果文本有变化，更新段落
            if new_text != full_text:
                # 清空段落
                paragraph.clear()

                # 重新添加文本，尝试保持格式
                new_run = paragraph.add_run(new_text)

                # 恢复第一个run的格式（通常是主要格式）
                if original_runs:
                    try:
                        first_run = original_runs[0]
                        if first_run['font_name']:
                            new_run.font.name = first_run['font_name']
                        if first_run['font_size']:
                            new_run.font.size = first_run['font_size']
                        new_run.bold = first_run['bold']
                        new_run.italic = first_run['italic']
                        new_run.underline = first_run['underline']
                        if first_run['color']:
                            new_run.font.color.rgb = first_run['color']
                    except Exception as format_error:
                        logger.warning(f"恢复格式失败: {format_error}")

        except Exception as e:
            logger.error(f"替换段落变量失败: {e}")
            # 最简单的降级处理
            try:
                full_text = paragraph.text
                if '${' in full_text:
                    new_text = full_text
                    for var_name, var_value in variables_values.items():
                        placeholder = f"${{{var_name}}}"
                        new_text = new_text.replace(placeholder, str(var_value))

                    if new_text != full_text:
                        paragraph.clear()
                        paragraph.add_run(new_text)
            except:
                pass


            # 降级处理
            self.replace_paragraph_variables_enhanced(paragraph, variables_values)

    def clean_rich_text_for_inline(self, rich_text):
        """清理富文本内容用于内联显示"""
        if not rich_text:
            return ""

        import re

        # 转换为字符串
        text = str(rich_text)

        # 移除HTML标签，但保留内容
        text = re.sub(r'<br\s*/?>', '\n', text)  # br标签转换为换行
        text = re.sub(r'<p[^>]*>', '', text)     # 移除p开始标签
        text = re.sub(r'</p>', '\n', text)       # p结束标签转换为换行
        text = re.sub(r'<[^>]+>', '', text)      # 移除所有其他HTML标签

        # 解码HTML实体
        html_entities = {
            '&amp;': '&', '&lt;': '<', '&gt;': '>', '&quot;': '"',
            '&#39;': "'", '&nbsp;': ' ', '&copy;': '©', '&reg;': '®'
        }
        for entity, char in html_entities.items():
            text = text.replace(entity, char)

        # 规范化空白字符
        text = re.sub(r'\s+', ' ', text)  # 多个空白字符合并为一个空格
        text = text.strip()

        return text

    def add_rich_content_with_frame(self, doc, rich_content):
        """添加带框框的富文本内容"""
        try:
            from docx.shared import Pt, Inches, RGBColor
            from docx.enum.text import WD_ALIGN_PARAGRAPH
            from docx.enum.table import WD_TABLE_ALIGNMENT
            import re

            if not rich_content or not rich_content.strip():
                return

            # 添加空行分隔
            doc.add_paragraph()

            # 添加分隔符（左对齐）
            separator_para = doc.add_paragraph()
            separator_run = separator_para.add_run("=" * 60)
            separator_run.font.name = '黑体'
            separator_run.font.size = Pt(10)
            separator_para.alignment = WD_ALIGN_PARAGRAPH.LEFT

            # 添加富文本内容标题（左对齐）
            title_para = doc.add_paragraph()
            title_run = title_para.add_run("详细内容")
            title_run.font.name = '黑体'
            title_run.font.size = Pt(14)
            title_run.bold = True
            title_para.alignment = WD_ALIGN_PARAGRAPH.LEFT

            # 添加空行
            doc.add_paragraph()

            # 创建框框表格来包含富文本内容（左对齐）
            table = doc.add_table(rows=1, cols=1)
            table.alignment = WD_TABLE_ALIGNMENT.LEFT  # 表格左对齐
            table.style = 'Table Grid'

            # 设置表格样式
            cell = table.cell(0, 0)

            # 设置单元格边框（创建框框效果）
            from docx.oxml.shared import qn
            from docx.oxml import parse_xml

            # 设置边框
            tc = cell._tc
            tcPr = tc.get_or_add_tcPr()
            tcBorders = parse_xml(r'<w:tcBorders xmlns:w="http://schemas.openxmlformats.org/wordprocessingml/2006/main">'
                                 r'<w:top w:val="single" w:sz="8" w:space="0" w:color="000000"/>'
                                 r'<w:left w:val="single" w:sz="8" w:space="0" w:color="000000"/>'
                                 r'<w:bottom w:val="single" w:sz="8" w:space="0" w:color="000000"/>'
                                 r'<w:right w:val="single" w:sz="8" w:space="0" w:color="000000"/>'
                                 r'</w:tcBorders>')
            tcPr.append(tcBorders)

            # 设置单元格内边距
            from docx.shared import Twips
            tcMar = parse_xml(r'<w:tcMar xmlns:w="http://schemas.openxmlformats.org/wordprocessingml/2006/main">'
                             r'<w:top w:w="144" w:type="dxa"/>'
                             r'<w:left w:w="144" w:type="dxa"/>'
                             r'<w:bottom w:w="144" w:type="dxa"/>'
                             r'<w:right w:w="144" w:type="dxa"/>'
                             r'</w:tcMar>')
            tcPr.append(tcMar)

            # 清理富文本内容
            clean_content = self.clean_rich_text_content(rich_content)

            # 将清理后的内容添加到单元格
            self.add_formatted_content_to_cell(cell, clean_content)

        except Exception as e:
            logger.error(f"添加带框框的富文本内容失败: {e}")
            # 降级：添加普通段落
            try:
                clean_content = self.clean_rich_text_for_inline(rich_content)
                if clean_content.strip():
                    para = doc.add_paragraph()
                    run = para.add_run(clean_content)
                    run.font.name = '黑体'
                    run.font.size = Pt(12)
            except:
                pass

    def clean_rich_text_content(self, rich_content):
        """清理富文本内容，保留基本格式"""
        if not rich_content:
            return ""

        import re

        text = str(rich_content)

        # 保留重要的格式标记，转换为简单标记
        format_map = {
            '<strong>': '**', '</strong>': '**',
            '<b>': '**', '</b>': '**',
            '<em>': '*', '</em>': '*',
            '<i>': '*', '</i>': '*',
            '<u>': '_', '</u>': '_',
            '<h1>': '\n# ', '</h1>': '\n',
            '<h2>': '\n## ', '</h2>': '\n',
            '<h3>': '\n### ', '</h3>': '\n',
            '<p>': '\n', '</p>': '\n',
            '<br>': '\n', '<br/>': '\n', '<br />': '\n',
            '<li>': '\n• ', '</li>': '',
            '<ul>': '\n', '</ul>': '\n',
            '<ol>': '\n', '</ol>': '\n'
        }

        # 应用格式映射
        for html, replacement in format_map.items():
            text = text.replace(html, replacement)

        # 移除所有剩余的HTML标签
        text = re.sub(r'<[^>]*>', '', text)

        # 解码HTML实体
        html_entities = {
            '&amp;': '&', '&lt;': '<', '&gt;': '>', '&quot;': '"',
            '&#39;': "'", '&nbsp;': ' ', '&copy;': '©', '&reg;': '®'
        }
        for entity, char in html_entities.items():
            text = text.replace(entity, char)

        # 规范化空白字符
        text = re.sub(r'\r\n', '\n', text)
        text = re.sub(r'\r', '\n', text)
        text = re.sub(r'[ ]+', ' ', text)
        text = re.sub(r'\n\s*\n\s*\n+', '\n\n', text)

        return text.strip()

    def add_formatted_content_to_cell(self, cell, content):
        """向单元格添加格式化内容"""
        try:
            from docx.shared import Pt
            import re

            if not content:
                return

            # 按段落分割内容
            paragraphs = content.split('\n\n')

            # 清空单元格现有内容
            cell.text = ''

            for i, para_text in enumerate(paragraphs):
                para_text = para_text.strip()
                if not para_text:
                    continue

                # 如果不是第一个段落，添加段落
                if i > 0:
                    cell.add_paragraph()

                # 获取当前段落（第一个段落使用现有的，后续的是新添加的）
                if i == 0:
                    paragraph = cell.paragraphs[0]
                else:
                    paragraph = cell.paragraphs[-1]

                # 确保段落左对齐
                from docx.enum.text import WD_ALIGN_PARAGRAPH
                paragraph.alignment = WD_ALIGN_PARAGRAPH.LEFT

                # 检查是否是标题
                if para_text.startswith('#'):
                    level = min(para_text.count('#'), 3)
                    title_text = para_text.lstrip('#').strip()
                    if title_text:
                        run = paragraph.add_run(title_text)
                        run.font.name = '黑体'
                        run.font.size = Pt(14 - level)
                        run.bold = True
                else:
                    # 处理格式化文本
                    self.add_formatted_text_to_paragraph_black(paragraph, para_text)

        except Exception as e:
            logger.error(f"向单元格添加格式化内容失败: {e}")
            # 降级：添加纯文本
            cell.text = content

    def add_formatted_text_to_paragraph_black(self, paragraph, text):
        """向段落添加格式化文本（黑体）"""
        try:
            from docx.shared import Pt
            import re

            # 简单的格式处理
            parts = re.split(r'(\*\*[^*]+\*\*|\*[^*]+\*|_[^_]+_)', text)

            for part in parts:
                if not part:
                    continue

                run = paragraph.add_run()
                run.font.name = '黑体'  # 使用黑体
                run.font.size = Pt(12)

                if part.startswith('**') and part.endswith('**'):
                    # 粗体
                    run.text = part[2:-2]
                    run.bold = True
                elif part.startswith('*') and part.endswith('*'):
                    # 斜体
                    run.text = part[1:-1]
                    run.italic = True
                elif part.startswith('_') and part.endswith('_'):
                    # 下划线
                    run.text = part[1:-1]
                    run.underline = True
                else:
                    # 普通文本
                    run.text = part

        except Exception as e:
            logger.error(f"添加格式化文本失败: {e}")
            # 降级：添加纯文本
            run = paragraph.add_run(text)
            run.font.name = '黑体'
            run.font.size = Pt(12)

    def setup_document_styles(self, doc):
        """设置文档样式"""
        try:
            from docx.shared import Pt, RGBColor
            from docx.enum.style import WD_STYLE_TYPE

            # 设置正文样式
            styles = doc.styles

            # 确保有正文样式
            try:
                normal_style = styles['Normal']
                normal_font = normal_style.font
                normal_font.name = '宋体'
                normal_font.size = Pt(12)
                normal_font.color.rgb = RGBColor(0, 0, 0)
            except:
                pass

        except Exception as e:
            logger.warning(f"设置文档样式失败: {e}")

    def replace_paragraph_variables_enhanced(self, paragraph, variables_values):
        """增强的变量替换，保持原始格式"""
        # 直接调用保持格式的替换方法
        self.replace_paragraph_variables_with_rich_text_support(paragraph, variables_values)

    def html_to_clean_text(self, html_content):
        """将HTML转换为干净的文本"""
        try:
            import html2text

            # 配置html2text
            h = html2text.HTML2Text()
            h.ignore_links = False
            h.ignore_images = False
            h.ignore_emphasis = False
            h.body_width = 0  # 不换行
            h.unicode_snob = True
            h.escape_snob = True

            # 转换
            text = h.handle(html_content)

            # 清理多余的换行
            text = re.sub(r'\n\s*\n', '\n\n', text)
            text = text.strip()

            return text

        except ImportError:
            logger.warning("html2text未安装，使用简单转换")
            return self.simple_html_to_text(html_content)
        except Exception as e:
            logger.error(f"HTML转文本失败: {e}")
            return self.simple_html_to_text(html_content)

    def simple_html_to_text(self, html_content):
        """简单的HTML到文本转换"""
        from bs4 import BeautifulSoup
        import re

        try:
            soup = BeautifulSoup(html_content, 'html.parser')

            # 处理换行
            for br in soup.find_all('br'):
                br.replace_with('\n')

            # 处理段落
            for p in soup.find_all('p'):
                p.insert_after('\n')

            # 获取纯文本
            text = soup.get_text()

            # 清理多余空白
            text = re.sub(r'\n\s*\n', '\n\n', text)
            text = re.sub(r'[ \t]+', ' ', text)
            text = text.strip()

            return text

        except Exception as e:
            logger.error(f"简单HTML转换失败: {e}")
            # 最后的降级方案：正则表达式去除标签
            return re.sub(r'<[^>]+>', '', html_content)

    def add_rich_content_enhanced(self, doc, rich_content):
        """增强的富文本内容添加"""
        if not rich_content:
            return

        try:
            # 添加分隔符
            doc.add_paragraph()
            separator_para = doc.add_paragraph()
            separator_run = separator_para.add_run("=" * 50)
            separator_run.font.name = '宋体'
            separator_run.font.size = Pt(10)

            # 添加标题
            title_para = doc.add_paragraph()
            title_run = title_para.add_run("附加内容")
            title_run.font.name = '宋体'
            title_run.font.size = Pt(14)
            title_run.bold = True

            separator_para2 = doc.add_paragraph()
            separator_run2 = separator_para2.add_run("=" * 50)
            separator_run2.font.name = '宋体'
            separator_run2.font.size = Pt(10)

            doc.add_paragraph()

            # 处理富文本内容
            self.process_rich_content_to_docx(doc, rich_content)

        except Exception as e:
            logger.error(f"添加富文本内容失败: {e}")
            # 降级方案：添加纯文本
            clean_text = self.html_to_clean_text(rich_content)
            doc.add_paragraph(clean_text)

    def process_rich_content_to_docx(self, doc, html_content):
        """将富文本内容智能转换为docx格式"""
        from bs4 import BeautifulSoup
        import base64
        import io

        try:
            soup = BeautifulSoup(html_content, 'html.parser')

            # 处理每个元素
            for element in soup.find_all(['p', 'h1', 'h2', 'h3', 'h4', 'h5', 'h6', 'ul', 'ol', 'li', 'img', 'strong', 'em', 'br']):
                if element.name in ['h1', 'h2', 'h3', 'h4', 'h5', 'h6']:
                    # 标题
                    level = int(element.name[1])
                    para = doc.add_paragraph()
                    run = para.add_run(element.get_text())
                    run.font.name = '宋体'
                    run.font.size = Pt(16 - level)
                    run.bold = True

                elif element.name == 'p':
                    # 段落
                    para = doc.add_paragraph()
                    self.process_paragraph_content(para, element)

                elif element.name in ['ul', 'ol']:
                    # 列表
                    for li in element.find_all('li'):
                        para = doc.add_paragraph()
                        run = para.add_run(f"• {li.get_text()}")
                        run.font.name = '宋体'
                        run.font.size = Pt(12)

                elif element.name == 'img':
                    # 图片
                    self.process_image_element(doc, element)

            # 如果没有找到结构化元素，添加纯文本
            if not soup.find_all(['p', 'h1', 'h2', 'h3', 'h4', 'h5', 'h6']):
                text = soup.get_text()
                if text.strip():
                    para = doc.add_paragraph()
                    run = para.add_run(text.strip())
                    run.font.name = '宋体'
                    run.font.size = Pt(12)

        except Exception as e:
            logger.error(f"处理富文本内容失败: {e}")
            # 最终降级：纯文本
            clean_text = self.html_to_clean_text(html_content)
            para = doc.add_paragraph()
            run = para.add_run(clean_text)
            run.font.name = '宋体'
            run.font.size = Pt(12)

    def process_paragraph_content(self, para, element):
        """处理段落内容"""
        from docx.shared import Pt

        try:
            # 处理段落中的格式化文本
            for content in element.contents:
                if hasattr(content, 'name'):
                    if content.name == 'strong' or content.name == 'b':
                        run = para.add_run(content.get_text())
                        run.bold = True
                        run.font.name = '宋体'
                        run.font.size = Pt(12)
                    elif content.name == 'em' or content.name == 'i':
                        run = para.add_run(content.get_text())
                        run.italic = True
                        run.font.name = '宋体'
                        run.font.size = Pt(12)
                    else:
                        run = para.add_run(content.get_text())
                        run.font.name = '宋体'
                        run.font.size = Pt(12)
                else:
                    # 纯文本
                    text = str(content).strip()
                    if text:
                        run = para.add_run(text)
                        run.font.name = '宋体'
                        run.font.size = Pt(12)

        except Exception as e:
            logger.error(f"处理段落内容失败: {e}")
            # 降级：添加纯文本
            run = para.add_run(element.get_text())
            run.font.name = '宋体'
            run.font.size = Pt(12)

    def process_image_element(self, doc, img_element):
        """处理图片元素"""
        try:
            src = img_element.get('src', '')
            alt = img_element.get('alt', '图片')

            if src.startswith('data:image'):
                # 处理base64图片
                self.add_base64_image_enhanced(doc, src, alt)
            else:
                # 其他图片源，添加占位符
                para = doc.add_paragraph()
                run = para.add_run(f"[图片: {alt}]")
                run.font.name = '宋体'
                run.font.size = Pt(12)
                run.italic = True

        except Exception as e:
            logger.error(f"处理图片失败: {e}")

    def add_base64_image_enhanced(self, doc, data_url, alt_text="图片"):
        """增强的base64图片添加"""
        try:
            # 检查PIL是否可用
            try:
                from PIL import Image
            except ImportError:
                para = doc.add_paragraph()
                run = para.add_run(f"[图片: {alt_text} - PIL库未安装]")
                run.font.name = '宋体'
                run.font.size = Pt(12)
                return

            # 解析data URL
            header, data = data_url.split(',', 1)
            image_data = base64.b64decode(data)

            # 使用PIL处理图片
            image = Image.open(io.BytesIO(image_data))

            # 调整图片尺寸
            max_width = 400  # 最大宽度
            if image.width > max_width:
                ratio = max_width / image.width
                new_height = int(image.height * ratio)
                image = image.resize((max_width, new_height), Image.Resampling.LANCZOS)

            # 保存临时图片
            temp_image_path = os.path.join(settings.BASE_DIR, 'storage', 'temp_images', f'temp_{uuid.uuid4().hex[:8]}.png')
            os.makedirs(os.path.dirname(temp_image_path), exist_ok=True)
            image.save(temp_image_path, 'PNG')

            # 添加到文档
            para = doc.add_paragraph()
            run = para.add_run()
            run.add_picture(temp_image_path, width=Inches(min(6, image.width / 100)))

            # 清理临时文件
            try:
                os.remove(temp_image_path)
            except:
                pass

        except Exception as e:
            logger.error(f"添加base64图片失败: {e}")
            # 添加占位符
            para = doc.add_paragraph()
            run = para.add_run(f"[图片: {alt_text} - 处理失败]")
            run.font.name = '宋体'
            run.font.size = Pt(12)

    def generate_markdown_content(self, instance):
        """生成Markdown内容"""
        import re
        from bs4 import BeautifulSoup

        # 获取变量值
        variables_values = json.loads(instance.variables_values) if instance.variables_values else {}

        # 开始构建Markdown内容
        markdown_lines = []

        # 添加文档标题
        markdown_lines.append(f"# {instance.title}\n")
        markdown_lines.append("---\n")

        # 处理模板内容
        if instance.template and instance.template.template_file:
            template_content = self.extract_template_content(instance.template.template_file)

            # 替换变量
            for var_name, var_value in variables_values.items():
                placeholder = f"${{{var_name}}}"
                if isinstance(var_value, str) and self.is_html_content(var_value):
                    # 富文本变量转换为Markdown
                    markdown_value = self.html_to_markdown(var_value)
                    template_content = template_content.replace(placeholder, markdown_value)
                else:
                    # 普通变量直接替换
                    template_content = template_content.replace(placeholder, str(var_value))

            markdown_lines.append(template_content)

        # 添加富文本内容
        if instance.rich_content:
            markdown_lines.append("\n---\n")
            markdown_lines.append("## 附加内容\n")
            rich_markdown = self.html_to_markdown(instance.rich_content)
            markdown_lines.append(rich_markdown)

        return "\n".join(markdown_lines)

    def extract_template_content(self, template_path):
        """从Word模板中提取文本内容"""
        try:
            from docx import Document
            doc = Document(template_path)

            content_lines = []

            # 提取段落
            for paragraph in doc.paragraphs:
                if paragraph.text.strip():
                    content_lines.append(paragraph.text)

            # 提取表格
            for table in doc.tables:
                content_lines.append("\n")  # 表格前空行
                for row in table.rows:
                    row_cells = []
                    for cell in row.cells:
                        row_cells.append(cell.text.strip())
                    content_lines.append("| " + " | ".join(row_cells) + " |")
                content_lines.append("\n")  # 表格后空行

            return "\n".join(content_lines)

        except Exception as e:
            logger.error(f"提取模板内容失败: {e}")
            return f"模板内容提取失败: {str(e)}"

    def is_html_content(self, content):
        """检查内容是否包含HTML标签"""
        return isinstance(content, str) and ('<' in content and '>' in content)

    def html_to_markdown(self, html_content):
        """将HTML内容转换为Markdown"""
        try:
            import pypandoc

            # 处理图片
            html_content = self.process_images_in_html(html_content)

            # 使用pypandoc转换
            markdown = pypandoc.convert_text(html_content, 'md', format='html')
            return markdown

        except ImportError:
            logger.warning("pypandoc未安装，使用简单HTML转换")
            return self.simple_html_to_markdown(html_content)
        except Exception as e:
            logger.error(f"HTML转Markdown失败: {e}")
            return self.simple_html_to_markdown(html_content)

    def process_images_in_html(self, html_content):
        """处理HTML中的图片"""
        from bs4 import BeautifulSoup
        import base64
        import uuid

        soup = BeautifulSoup(html_content, 'html.parser')
        images = soup.find_all('img')

        for img in images:
            src = img.get('src', '')

            if src.startswith('data:image'):
                # 处理base64图片
                try:
                    # 解析data URL
                    header, data = src.split(',', 1)
                    image_data = base64.b64decode(data)

                    # 确定图片格式
                    if 'png' in header:
                        ext = 'png'
                    elif 'jpeg' in header or 'jpg' in header:
                        ext = 'jpg'
                    elif 'gif' in header:
                        ext = 'gif'
                    else:
                        ext = 'png'

                    # 保存图片文件
                    images_dir = os.path.join(settings.BASE_DIR, 'storage', 'temp_images')
                    os.makedirs(images_dir, exist_ok=True)

                    image_filename = f"image_{uuid.uuid4().hex[:8]}.{ext}"
                    image_path = os.path.join(images_dir, image_filename)

                    with open(image_path, 'wb') as f:
                        f.write(image_data)

                    # 更新img标签的src
                    img['src'] = image_path

                except Exception as e:
                    logger.error(f"处理base64图片失败: {e}")
                    # 替换为占位符
                    img.replace_with(f"[图片: {img.get('alt', '无法显示')}]")

        return str(soup)

    def simple_html_to_markdown(self, html_content):
        """简单的HTML到Markdown转换"""
        from bs4 import BeautifulSoup

        soup = BeautifulSoup(html_content, 'html.parser')

        # 简单的标签转换
        replacements = [
            ('<strong>', '**'), ('</strong>', '**'),
            ('<b>', '**'), ('</b>', '**'),
            ('<em>', '*'), ('</em>', '*'),
            ('<i>', '*'), ('</i>', '*'),
            ('<u>', '<u>'), ('</u>', '</u>'),
            ('<br>', '\n'), ('<br/>', '\n'), ('<br />', '\n'),
            ('<p>', '\n'), ('</p>', '\n'),
            ('<h1>', '# '), ('</h1>', '\n'),
            ('<h2>', '## '), ('</h2>', '\n'),
            ('<h3>', '### '), ('</h3>', '\n'),
            ('<h4>', '#### '), ('</h4>', '\n'),
            ('<h5>', '##### '), ('</h5>', '\n'),
            ('<h6>', '###### '), ('</h6>', '\n'),
        ]

        text = str(soup)
        for old, new in replacements:
            text = text.replace(old, new)

        # 处理列表
        text = re.sub(r'<li>(.*?)</li>', r'- \1\n', text, flags=re.DOTALL)
        text = re.sub(r'<ul>(.*?)</ul>', r'\1', text, flags=re.DOTALL)
        text = re.sub(r'<ol>(.*?)</ol>', r'\1', text, flags=re.DOTALL)

        # 清理剩余的HTML标签
        text = re.sub(r'<[^>]+>', '', text)

        # 清理多余的空行
        text = re.sub(r'\n\s*\n', '\n\n', text)

        return text.strip()

    def convert_markdown_to_word(self, markdown_content, title):
        """使用Pandoc将Markdown转换为Word文档"""
        try:
            import pypandoc

            # 生成输出文件路径
            output_dir = os.path.join(settings.BASE_DIR, 'storage', 'generated_documents')
            os.makedirs(output_dir, exist_ok=True)

            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            filename = f"{title}_{timestamp}.docx"
            output_path = os.path.join(output_dir, filename)

            # 使用pypandoc转换
            pypandoc.convert_text(
                markdown_content,
                'docx',
                format='md',
                outputfile=output_path,
                extra_args=[
                    '--reference-doc=' + self.get_reference_doc(),
                    '--toc',  # 生成目录
                    '--toc-depth=3',  # 目录深度
                    '--highlight-style=tango',  # 代码高亮样式
                ]
            )

            return output_path

        except ImportError:
            logger.error("pypandoc未安装，无法转换文档")
            raise Exception("pypandoc未安装，请安装pypandoc和pandoc")
        except Exception as e:
            logger.error(f"Pandoc转换失败: {e}")
            raise

    def get_reference_doc(self):
        """获取参考文档模板"""
        # 可以创建一个标准的Word模板作为样式参考
        reference_path = os.path.join(settings.BASE_DIR, 'storage', 'templates', 'reference.docx')

        if not os.path.exists(reference_path):
            # 如果没有参考文档，创建一个基本的
            self.create_reference_doc(reference_path)

        return reference_path

    def create_reference_doc(self, output_path):
        """创建参考文档模板"""
        try:
            from docx import Document
            from docx.shared import Inches

            os.makedirs(os.path.dirname(output_path), exist_ok=True)

            doc = Document()

            # 设置页面边距
            sections = doc.sections
            for section in sections:
                section.top_margin = Inches(1)
                section.bottom_margin = Inches(1)
                section.left_margin = Inches(1)
                section.right_margin = Inches(1)

            # 添加样式示例
            doc.add_heading('标题样式示例', 0)
            doc.add_heading('一级标题', level=1)
            doc.add_heading('二级标题', level=2)
            doc.add_heading('三级标题', level=3)

            doc.add_paragraph('这是正文段落的样式示例。')

            doc.save(output_path)

        except Exception as e:
            logger.error(f"创建参考文档失败: {e}")

    def generate_word_document_fallback(self, instance):
        """降级方案：使用原始的python-docx方法"""
        from docx import Document
        import re

        try:
            # 加载模板文档
            template_path = instance.template.template_file
            if not os.path.exists(template_path):
                raise FileNotFoundError(f"模板文件不存在: {template_path}")

            doc = Document(template_path)

            # 获取变量值
            variables_values = json.loads(instance.variables_values) if instance.variables_values else {}

            # 替换段落中的变量
            for paragraph in doc.paragraphs:
                self.replace_paragraph_variables_simple(paragraph, variables_values)

            # 替换表格中的变量
            for table in doc.tables:
                for row in table.rows:
                    for cell in row.cells:
                        for paragraph in cell.paragraphs:
                            self.replace_paragraph_variables_simple(paragraph, variables_values)

            # 如果有富文本内容，添加到文档末尾（简单处理）
            if instance.rich_content:
                self.add_simple_rich_content(doc, instance.rich_content)

            # 生成输出文件路径
            output_dir = os.path.join(settings.BASE_DIR, 'storage', 'generated_documents')
            os.makedirs(output_dir, exist_ok=True)

            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            filename = f"{instance.title}_{timestamp}_fallback.docx"
            output_path = os.path.join(output_dir, filename)

            # 保存文档
            doc.save(output_path)

            return output_path

        except Exception as e:
            logger.error(f"降级方案也失败: {e}")
            raise

    def replace_paragraph_variables_simple(self, paragraph, variables_values):
        """简单的变量替换，保持原始格式"""
        import re

        # 直接调用保持格式的替换方法
        self.replace_paragraph_variables_with_rich_text_support(paragraph, variables_values)

    def add_simple_rich_content(self, doc, rich_content):
        """简单处理富文本内容"""
        if not rich_content:
            return

        # 添加分隔符
        doc.add_paragraph().add_run("=" * 50)
        doc.add_paragraph().add_run("附加内容")
        doc.add_paragraph().add_run("=" * 50)

        # 简单处理富文本内容（去除HTML标签）
        import re
        clean_content = re.sub(r'<[^>]+>', '', rich_content)

        # 按行分割并添加到文档
        lines = clean_content.split('\n')
        for line in lines:
            if line.strip():
                doc.add_paragraph(line.strip())

    def replace_paragraph_variables(self, paragraph, variables_values):
        """替换段落中的变量"""
        import re

        # 支持 ${变量名} 格式
        pattern = r'\$\{([^}]+)\}'

        # 获取段落的完整文本
        full_text = paragraph.text

        # 查找所有变量
        matches = re.findall(pattern, full_text)

        if matches:
            # 检查是否有富文本变量
            has_rich_text = False
            for var_name in matches:
                var_value = variables_values.get(var_name, f'${{{var_name}}}')
                if isinstance(var_value, str) and ('<' in var_value and '>' in var_value):
                    has_rich_text = True
                    break

            if has_rich_text:
                # 如果包含富文本，需要特殊处理
                self.replace_paragraph_with_rich_content(paragraph, matches, variables_values, full_text)
            else:
                # 普通文本替换
                new_text = full_text
                for var_name in matches:
                    var_value = str(variables_values.get(var_name, f'${{{var_name}}}'))
                    new_text = new_text.replace(f'${{{var_name}}}', var_value)

                # 清空段落并添加新文本
                paragraph.clear()
                paragraph.add_run(new_text)

    def replace_paragraph_with_rich_content(self, paragraph, matches, variables_values, full_text):
        """替换包含富文本的段落"""
        import re

        # 获取段落的父文档
        doc = paragraph._element.getparent()
        while doc.tag != '{http://schemas.openxmlformats.org/wordprocessingml/2006/main}document':
            doc = doc.getparent()
            if doc is None:
                break

        if doc is None:
            # 降级处理
            new_text = full_text
            for var_name in matches:
                var_value = str(variables_values.get(var_name, f'${{{var_name}}}'))
                # 去除HTML标签
                clean_value = re.sub(r'<[^>]+>', '', var_value)
                new_text = new_text.replace(f'${{{var_name}}}', clean_value)
            paragraph.clear()
            paragraph.add_run(new_text)
            return

        # 找到段落在文档中的位置
        parent = paragraph._element.getparent()
        paragraph_index = list(parent).index(paragraph._element)

        # 处理每个变量
        current_text = full_text
        for var_name in matches:
            var_value = variables_values.get(var_name, f'${{{var_name}}}')

            if isinstance(var_value, str) and ('<' in var_value and '>' in var_value):
                # 富文本变量
                # 分割文本
                parts = current_text.split(f'${{{var_name}}}', 1)
                if len(parts) == 2:
                    before_text, after_text = parts

                    # 更新当前段落为前面的文本
                    if before_text.strip():
                        paragraph.clear()
                        paragraph.add_run(before_text)
                    else:
                        # 如果前面没有文本，移除这个段落
                        parent.remove(paragraph._element)
                        paragraph_index -= 1

                    # 插入富文本内容
                    from docx import Document
                    temp_doc = Document()
                    self.parse_html_to_docx(temp_doc, var_value)

                    # 将临时文档的内容插入到原文档
                    for temp_paragraph in temp_doc.paragraphs:
                        if temp_paragraph.text.strip():  # 只插入非空段落
                            new_p = parent.insert(paragraph_index + 1, temp_paragraph._element)
                            paragraph_index += 1

                    # 处理剩余文本
                    current_text = after_text
                    if after_text.strip():
                        # 创建新段落放置剩余文本
                        from docx.oxml import OxmlElement
                        new_p_element = OxmlElement('w:p')
                        parent.insert(paragraph_index + 1, new_p_element)
                        # 这里需要更复杂的处理，暂时简化
                        break
            else:
                # 普通文本变量
                current_text = current_text.replace(f'${{{var_name}}}', str(var_value))

        # 如果还有剩余的普通文本，更新段落
        if current_text != full_text and not ('<' in current_text and '>' in current_text):
            paragraph.clear()
            paragraph.add_run(current_text)

    def add_rich_content_to_document(self, doc, rich_content):
        """添加富文本内容到文档"""
        if not rich_content:
            return

        # 添加分隔符
        doc.add_paragraph().add_run("=" * 50)
        doc.add_paragraph().add_run("附加内容")
        doc.add_paragraph().add_run("=" * 50)

        # 使用HTML解析器处理富文本内容
        self.parse_html_to_docx(doc, rich_content)

    def parse_html_to_docx(self, doc, html_content):
        """将HTML内容转换为Word文档格式"""
        from bs4 import BeautifulSoup
        from docx.shared import Inches, RGBColor, Pt
        from docx.enum.text import WD_ALIGN_PARAGRAPH
        from docx.oxml.shared import OxmlElement, qn
        import base64
        import io
        import re
        import uuid

        try:
            from PIL import Image
        except ImportError:
            Image = None

        try:
            # 解析HTML
            soup = BeautifulSoup(html_content, 'html.parser')

            # 处理每个元素
            for element in soup.find_all():
                if element.name == 'p':
                    self.add_paragraph_from_html(doc, element)
                elif element.name in ['h1', 'h2', 'h3', 'h4', 'h5', 'h6']:
                    self.add_heading_from_html(doc, element)
                elif element.name == 'img':
                    self.add_image_from_html(doc, element)
                elif element.name in ['ul', 'ol']:
                    self.add_list_from_html(doc, element)
                elif element.name == 'table':
                    self.add_table_from_html(doc, element)
                elif element.name == 'div':
                    self.add_div_from_html(doc, element)

            # 如果没有找到结构化元素，直接处理文本
            if not soup.find_all(['p', 'h1', 'h2', 'h3', 'h4', 'h5', 'h6', 'div']):
                text_content = soup.get_text()
                if text_content.strip():
                    lines = text_content.split('\n')
                    for line in lines:
                        if line.strip():
                            doc.add_paragraph(line.strip())

        except Exception as e:
            logger.error(f"解析HTML内容失败: {e}")
            # 降级处理：去除HTML标签
            import re
            clean_content = re.sub(r'<[^>]+>', '', html_content)
            lines = clean_content.split('\n')
            for line in lines:
                if line.strip():
                    doc.add_paragraph(line.strip())

    def add_paragraph_from_html(self, doc, p_element):
        """从HTML段落元素添加Word段落"""
        paragraph = doc.add_paragraph()

        # 处理段落对齐 - 默认左对齐
        align = p_element.get('align', '').lower()
        if align == 'center':
            paragraph.alignment = WD_ALIGN_PARAGRAPH.CENTER
        elif align == 'right':
            paragraph.alignment = WD_ALIGN_PARAGRAPH.RIGHT
        elif align == 'justify':
            paragraph.alignment = WD_ALIGN_PARAGRAPH.JUSTIFY
        else:
            # 默认左对齐
            paragraph.alignment = WD_ALIGN_PARAGRAPH.LEFT

        # 处理段落内容
        self.process_inline_elements(paragraph, p_element)

    def add_heading_from_html(self, doc, h_element):
        """从HTML标题元素添加Word标题"""
        level = int(h_element.name[1])  # h1->1, h2->2, etc.
        heading = doc.add_heading(level=level)
        heading.text = h_element.get_text()

    def add_image_from_html(self, doc, img_element):
        """从HTML图片元素添加Word图片"""
        try:
            src = img_element.get('src', '')

            if src.startswith('data:image'):
                # 处理base64图片
                self.add_base64_image(doc, src)
            elif src.startswith('http'):
                # 处理网络图片
                self.add_url_image(doc, src)
            else:
                # 处理本地文件路径
                self.add_local_image(doc, src)

        except Exception as e:
            logger.error(f"添加图片失败: {e}")
            # 添加图片占位符
            doc.add_paragraph(f"[图片: {img_element.get('alt', '无法显示')}]")

    def add_base64_image(self, doc, data_url):
        """添加base64编码的图片"""
        try:
            if not Image:
                doc.add_paragraph("[图片: PIL库未安装，无法处理图片]")
                return

            # 解析data URL
            header, data = data_url.split(',', 1)
            image_data = base64.b64decode(data)

            # 使用PIL处理图片
            image = Image.open(io.BytesIO(image_data))

            # 保存临时文件
            temp_path = os.path.join(settings.BASE_DIR, 'temp', f'temp_image_{datetime.now().strftime("%Y%m%d_%H%M%S_%f")}.png')
            os.makedirs(os.path.dirname(temp_path), exist_ok=True)

            # 转换为RGB模式（如果需要）
            if image.mode in ('RGBA', 'LA', 'P'):
                background = Image.new('RGB', image.size, (255, 255, 255))
                if image.mode == 'P':
                    image = image.convert('RGBA')
                background.paste(image, mask=image.split()[-1] if image.mode == 'RGBA' else None)
                image = background

            image.save(temp_path, 'PNG')

            # 添加到文档
            paragraph = doc.add_paragraph()
            run = paragraph.runs[0] if paragraph.runs else paragraph.add_run()

            # 计算合适的图片尺寸
            max_width = Inches(6)  # 最大宽度6英寸
            max_height = Inches(4)  # 最大高度4英寸

            width_ratio = max_width / image.width * 96  # 96 DPI
            height_ratio = max_height / image.height * 96
            ratio = min(width_ratio, height_ratio, 1)  # 不放大图片

            final_width = Inches(image.width * ratio / 96)
            final_height = Inches(image.height * ratio / 96)

            run.add_picture(temp_path, width=final_width, height=final_height)

            # 清理临时文件
            try:
                os.remove(temp_path)
            except:
                pass

        except Exception as e:
            logger.error(f"处理base64图片失败: {e}")
            doc.add_paragraph("[图片: base64图片无法显示]")

    def add_url_image(self, doc, url):
        """添加网络图片"""
        try:
            import requests
            response = requests.get(url, timeout=10)
            response.raise_for_status()

            # 保存临时文件
            temp_path = os.path.join(settings.BASE_DIR, 'temp', f'temp_url_image_{datetime.now().strftime("%Y%m%d_%H%M%S_%f")}.jpg')
            os.makedirs(os.path.dirname(temp_path), exist_ok=True)

            with open(temp_path, 'wb') as f:
                f.write(response.content)

            # 添加到文档
            paragraph = doc.add_paragraph()
            run = paragraph.runs[0] if paragraph.runs else paragraph.add_run()
            run.add_picture(temp_path, width=Inches(4))

            # 清理临时文件
            try:
                os.remove(temp_path)
            except:
                pass

        except Exception as e:
            logger.error(f"处理网络图片失败: {e}")
            doc.add_paragraph(f"[图片: {url} 无法加载]")

    def add_local_image(self, doc, path):
        """添加本地图片"""
        try:
            if os.path.exists(path):
                paragraph = doc.add_paragraph()
                run = paragraph.runs[0] if paragraph.runs else paragraph.add_run()
                run.add_picture(path, width=Inches(4))
            else:
                doc.add_paragraph(f"[图片: {path} 文件不存在]")
        except Exception as e:
            logger.error(f"处理本地图片失败: {e}")
            doc.add_paragraph(f"[图片: {path} 无法显示]")

    def process_inline_elements(self, paragraph, element):
        """处理段落内的行内元素"""
        for content in element.contents:
            if hasattr(content, 'name'):
                # HTML元素
                if content.name == 'strong' or content.name == 'b':
                    run = paragraph.add_run(content.get_text())
                    run.bold = True
                elif content.name == 'em' or content.name == 'i':
                    run = paragraph.add_run(content.get_text())
                    run.italic = True
                elif content.name == 'u':
                    run = paragraph.add_run(content.get_text())
                    run.underline = True
                elif content.name == 'span':
                    run = paragraph.add_run(content.get_text())
                    # 处理样式
                    style = content.get('style', '')
                    if 'color:' in style:
                        # 简单的颜色处理
                        pass
                elif content.name == 'br':
                    paragraph.add_run('\n')
                elif content.name == 'img':
                    # 在段落中的图片
                    self.add_image_from_html(paragraph._element.getparent().getparent(), content)
                else:
                    # 其他元素，提取文本
                    text = content.get_text()
                    if text.strip():
                        paragraph.add_run(text)
            else:
                # 纯文本
                text = str(content).strip()
                if text:
                    paragraph.add_run(text)

    def add_list_from_html(self, doc, list_element):
        """从HTML列表元素添加Word列表"""
        is_ordered = list_element.name == 'ol'

        for i, li in enumerate(list_element.find_all('li', recursive=False)):
            paragraph = doc.add_paragraph()

            if is_ordered:
                paragraph.style = 'List Number'
            else:
                paragraph.style = 'List Bullet'

            # 处理列表项内容
            self.process_inline_elements(paragraph, li)

    def add_table_from_html(self, doc, table_element):
        """从HTML表格元素添加Word表格"""
        try:
            rows = table_element.find_all('tr')
            if not rows:
                return

            # 计算列数
            max_cols = max(len(row.find_all(['td', 'th'])) for row in rows)

            # 创建表格
            table = doc.add_table(rows=len(rows), cols=max_cols)
            table.style = 'Table Grid'

            for row_idx, tr in enumerate(rows):
                cells = tr.find_all(['td', 'th'])
                for col_idx, cell in enumerate(cells):
                    if col_idx < max_cols:
                        table_cell = table.cell(row_idx, col_idx)
                        table_cell.text = cell.get_text().strip()

                        # 如果是表头
                        if cell.name == 'th':
                            for paragraph in table_cell.paragraphs:
                                for run in paragraph.runs:
                                    run.bold = True

        except Exception as e:
            logger.error(f"处理表格失败: {e}")
            doc.add_paragraph(f"[表格: 无法显示]")

    def add_div_from_html(self, doc, div_element):
        """从HTML div元素添加Word段落"""
        # 检查div的对齐方式
        align = div_element.get('align', '').lower()

        paragraph = doc.add_paragraph()

        if align == 'center':
            paragraph.alignment = WD_ALIGN_PARAGRAPH.CENTER
        elif align == 'right':
            paragraph.alignment = WD_ALIGN_PARAGRAPH.RIGHT
        elif align == 'justify':
            paragraph.alignment = WD_ALIGN_PARAGRAPH.JUSTIFY
        else:
            # 默认左对齐
            paragraph.alignment = WD_ALIGN_PARAGRAPH.LEFT

        # 处理div内容
        self.process_inline_elements(paragraph, div_element)


@method_decorator(csrf_exempt, name='dispatch')
class DocumentGenerateView(View):
    """文档生成API"""

    @auth('document_management.document_generator.generate|model_storage.storage.add')
    def post(self, request):
        """生成Word文档"""
        try:
            data = json.loads(request.body)
            instance_id = data.get('instance_id')

            instance = DocumentInstance.objects.get(id=instance_id)

            # 生成文档
            generated_file_path = self.generate_word_document(instance)

            # 更新实例信息
            instance.generated_file_path = generated_file_path
            instance.file_size = os.path.getsize(generated_file_path) if os.path.exists(generated_file_path) else 0
            instance.status = 'generated'
            instance.save()

            # 更新模板使用次数
            if instance.template:
                from django.utils import timezone
                template = instance.template
                template.usage_count = (template.usage_count or 0) + 1
                template.last_used_at = timezone.now()
                template.save()

            return JsonResponse({
                'data': {
                    'id': instance.id,
                    'file_path': generated_file_path,
                    'message': '文档生成成功'
                },
                'error': ''
            })

        except DocumentInstance.DoesNotExist:
            return JsonResponse({'data': None, 'error': '文档实例不存在'}, status=404)
        except Exception as e:
            logger.error(f"生成Word文档失败: {e}")
            return JsonResponse({'data': None, 'error': str(e)}, status=500)

    def generate_word_document(self, instance):
        """生成Word文档的核心逻辑"""
        from docx import Document
        import re
        from datetime import datetime

        try:
            # 加载模板文档
            template_path = instance.template.template_file
            if not os.path.exists(template_path):
                raise FileNotFoundError(f"模板文件不存在: {template_path}")

            doc = Document(template_path)

            # 获取变量值
            variables_values = json.loads(instance.variables_values) if instance.variables_values else {}

            # 替换段落中的变量
            for paragraph in doc.paragraphs:
                self.replace_paragraph_variables(paragraph, variables_values)

            # 替换表格中的变量
            for table in doc.tables:
                for row in table.rows:
                    for cell in row.cells:
                        for paragraph in cell.paragraphs:
                            self.replace_paragraph_variables(paragraph, variables_values)

            # 如果有富文本内容，添加到文档末尾
            if instance.rich_content:
                self.add_rich_content_to_document(doc, instance.rich_content)

            # 生成输出文件路径
            output_dir = os.path.join(settings.BASE_DIR, 'storage', 'generated_documents')
            os.makedirs(output_dir, exist_ok=True)

            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            filename = f"{instance.title}_{timestamp}.docx"
            output_path = os.path.join(output_dir, filename)

            # 保存文档
            doc.save(output_path)

            return output_path

        except Exception as e:
            logger.error(f"生成Word文档失败: {e}")
            raise

    def replace_paragraph_variables(self, paragraph, variables_values):
        """替换段落中的变量，保持原始格式"""
        # 直接调用保持格式的替换方法
        self.replace_paragraph_variables_with_rich_text_support(paragraph, variables_values)

    def convert_html_to_word_text(self, html_content):
        """将HTML内容转换为Word可读的文本格式"""
        import re

        if not html_content:
            return ""

        # 移除HTML标签，保留文本内容
        text = re.sub(r'<[^>]+>', '', html_content)

        # 处理HTML实体
        text = text.replace('&nbsp;', ' ')
        text = text.replace('&lt;', '<')
        text = text.replace('&gt;', '>')
        text = text.replace('&amp;', '&')
        text = text.replace('&quot;', '"')

        # 处理换行
        text = text.replace('\n', ' ').replace('\r', ' ')

        # 清理多余的空格
        text = re.sub(r'\s+', ' ', text).strip()

        return text

    def add_rich_content_to_document(self, doc, rich_content):
        """将富文本内容添加到文档"""
        try:
            # 添加分页符
            doc.add_page_break()

            # 添加标题
            doc.add_heading('补充内容', level=1)

            # 简单处理富文本内容（这里可以根据需要扩展）
            # 目前只是添加纯文本，后续可以解析HTML格式
            clean_content = self.convert_html_to_word_text(rich_content)
            doc.add_paragraph(clean_content)

        except Exception as e:
            logger.error(f"添加富文本内容失败: {e}")


@method_decorator(csrf_exempt, name='dispatch')
class DocumentDownloadView(View):
    """文档下载API"""

    @auth('document_management.document_generator.download|document_management.document_instance.download|model_storage.storage.view')
    def get(self, request):
        """下载生成的Word文档"""
        try:
            instance_id = request.GET.get('instance_id')

            instance = DocumentInstance.objects.get(id=instance_id)

            if not instance.generated_file_path or not os.path.exists(instance.generated_file_path):
                return JsonResponse({'data': None, 'error': '文档文件不存在'}, status=404)

            # 更新导出统计
            instance.export_count += 1
            instance.last_exported_at = timezone.now()
            instance.status = 'exported'
            instance.save()

            # 读取文件内容
            with open(instance.generated_file_path, 'rb') as f:
                file_content = f.read()

            # 创建响应
            response = HttpResponse(file_content, content_type='application/vnd.openxmlformats-officedocument.wordprocessingml.document')

            # 设置文件名
            filename = f"{instance.title}.docx"
            encoded_filename = urllib.parse.quote(filename)
            response['Content-Disposition'] = f'attachment; filename="{filename}"; filename*=UTF-8\'\'{encoded_filename}'
            response['Content-Length'] = len(file_content)

            # 添加CORS头
            response['Access-Control-Allow-Origin'] = '*'
            response['Access-Control-Allow-Methods'] = 'GET, OPTIONS'
            response['Access-Control-Allow-Headers'] = 'Origin, Content-Type, Accept, X-Token'

            return response

        except DocumentInstance.DoesNotExist:
            return JsonResponse({'data': None, 'error': '文档实例不存在'}, status=404)
        except Exception as e:
            logger.error(f"下载文档失败: {e}")
            return JsonResponse({'data': None, 'error': str(e)}, status=500)


class DocumentTempDownloadView(View):
    """临时文档下载API（不保存实例）"""

    @auth('document_management.document_generator.download|model_storage.storage.view')
    def get(self, request):
        """下载临时生成的Word文档"""
        try:
            file_path = request.GET.get('file_path')
            temp_id = request.GET.get('temp_id')

            if not file_path or not temp_id:
                return JsonResponse({'data': None, 'error': '缺少必要参数'}, status=400)

            if not os.path.exists(file_path):
                return JsonResponse({'data': None, 'error': '临时文档文件不存在'}, status=404)

            # 读取文件内容
            with open(file_path, 'rb') as f:
                file_content = f.read()

            # 创建响应
            response = HttpResponse(file_content, content_type='application/vnd.openxmlformats-officedocument.wordprocessingml.document')

            # 设置文件名（从文件路径提取）
            filename = os.path.basename(file_path)
            encoded_filename = urllib.parse.quote(filename)
            response['Content-Disposition'] = f'attachment; filename="{filename}"; filename*=UTF-8\'\'\'{encoded_filename}\''
            response['Content-Length'] = len(file_content)

            # 添加CORS头
            response['Access-Control-Allow-Origin'] = '*'
            response['Access-Control-Allow-Methods'] = 'GET, OPTIONS'
            response['Access-Control-Allow-Headers'] = 'Origin, Content-Type, Accept, X-Token'

            # 下载后删除临时文件
            try:
                os.remove(file_path)
            except:
                pass  # 忽略删除失败

            return response

        except Exception as e:
            logger.error(f"下载临时文档失败: {e}")
            return JsonResponse({'data': None, 'error': str(e)}, status=500)


@method_decorator(csrf_exempt, name='dispatch')
class ModelDataView(View):
    """模型数据管理API"""

    @auth('model_storage.model_data.view')
    def get(self, request, pk=None):
        """获取模型数据列表或单个模型数据"""
        if pk:
            try:
                model_data = ModelData.objects.get(pk=pk)
                return json_response(model_data.to_dict())
            except ModelData.DoesNotExist:
                return json_response(error=f"模型数据 with id {pk} does not exist.", status=404)

        # 可选过滤参数
        model_name = request.GET.get('model_name')
        framework = request.GET.get('framework')
        status = request.GET.get('status')

        # 构建查询集
        model_data_list = ModelData.objects.all()
        if model_name:
            model_data_list = model_data_list.filter(model_name__icontains=model_name)
        if framework:
            model_data_list = model_data_list.filter(framework=framework)
        if status:
            model_data_list = model_data_list.filter(status=status)

        model_data_list = model_data_list.order_by('-created_at')
        data_list = [md.to_dict() for md in model_data_list]
        return json_response(data_list)

    @auth('model_storage.model_data.add')
    def post(self, request):
        """创建新模型数据"""
        try:
            data = json.loads(request.body)
            required_fields = ['model_name']
            if not all(field in data for field in required_fields):
                return json_response(error="缺少必要字段：模型名称", status=400)

            # 检查是否已存在相同名称和版本的模型
            existing = ModelData.objects.filter(
                model_name=data['model_name'],
                model_version=data.get('model_version', 'v1.0')
            ).first()

            if existing:
                return json_response(error="相同名称和版本的模型已存在", status=400)

            model_data = ModelData.objects.create(
                model_name=data['model_name'],
                model_version=data.get('model_version', 'v1.0'),
                model_size=data.get('model_size', ''),
                framework=data.get('framework', 'pytorch'),
                accuracy=data.get('accuracy'),
                inference_speed=data.get('inference_speed', ''),
                gpu_memory=data.get('gpu_memory', ''),
                dataset=data.get('dataset', ''),
                status=data.get('status', 'development'),
                description=data.get('description', ''),
                parameters_count=data.get('parameters_count'),
                training_time=data.get('training_time', ''),
                model_file_path=data.get('model_file_path', ''),
                config_file_path=data.get('config_file_path', ''),
                latency=data.get('latency'),
                throughput=data.get('throughput'),
                memory_usage=data.get('memory_usage', ''),
                created_by=data.get('created_by', ''),
                tags=json.dumps(data.get('tags', []))
            )
            return json_response(model_data.to_dict(), status=201)
        except json.JSONDecodeError:
            return json_response(error="无效的JSON格式", status=400)
        except Exception as e:
            return json_response(error=f"创建失败: {e}", status=500)

    @auth('model_storage.model_data.edit')
    def put(self, request, pk=None):
        """更新模型数据"""
        try:
            data = json.loads(request.body)
            model_data_id = pk or data.get('id')

            if not model_data_id:
                return json_response(error="缺少模型数据ID", status=400)

            model_data = ModelData.objects.get(pk=model_data_id)

            # 更新字段
            updatable_fields = [
                'model_name', 'model_version', 'model_size', 'framework',
                'accuracy', 'inference_speed', 'gpu_memory', 'dataset',
                'status', 'description', 'parameters_count', 'training_time',
                'model_file_path', 'config_file_path', 'latency', 'throughput',
                'memory_usage', 'created_by'
            ]

            for field in updatable_fields:
                if field in data:
                    setattr(model_data, field, data[field])

            # 特殊处理tags字段
            if 'tags' in data:
                model_data.tags = json.dumps(data['tags'])

            model_data.save()
            return json_response(model_data.to_dict())

        except ModelData.DoesNotExist:
            return json_response(error="模型数据不存在", status=404)
        except json.JSONDecodeError:
            return json_response(error="无效的JSON格式", status=400)
        except Exception as e:
            return json_response(error=f"更新失败: {e}", status=500)

    @auth('model_storage.model_data.del')
    def delete(self, request, pk=None):
        """删除模型数据"""
        try:
            data = json.loads(request.body) if request.body else {}
            model_data_id = pk or data.get('id')

            if not model_data_id:
                return json_response(error="缺少模型数据ID", status=400)

            model_data = ModelData.objects.get(pk=model_data_id)
            model_data.delete()
            return json_response({'message': '删除成功'})

        except ModelData.DoesNotExist:
            return json_response(error="模型数据不存在", status=404)
        except Exception as e:
            return json_response(error=f"删除失败: {e}", status=500)


@method_decorator(csrf_exempt, name='dispatch')
class PerformanceTestDataView(View):
    """性能测试数据管理API"""

    def _safe_int(self, value, default=0):
        """安全的整数转换"""
        if value is None or value == '':
            return default
        try:
            return max(0, int(float(value)))  # 先转float再转int，处理小数字符串
        except (ValueError, TypeError):
            return default

    def _safe_float(self, value, default=0.0):
        """安全的浮点数转换"""
        if value is None or value == '':
            return default
        try:
            return max(0.0, float(value))
        except (ValueError, TypeError):
            return default

    def _validate_performance_data(self, data, is_update=False):
        """验证性能测试数据 - 简化版本，只做基本验证"""
        errors = []

        # 只验证文件名长度，不要求必填
        if data.get('filename'):
            filename = data['filename'].strip()
            if len(filename) > 256:
                errors.append("文件名长度不能超过256个字符")

        # 模型名称长度验证
        if data.get('model_name'):
            model_name = data['model_name'].strip()
            if len(model_name) > 256:
                errors.append("模型名称长度不能超过256个字符")

        # 简化的数值验证 - 只检查是否为负数，不限制上限
        numeric_fields = {
            'success_requests': ('成功请求数', int),
            'benchmark_duration': ('基准时长', float),
            'input_tokens': ('输入Token总数', int),
            'output_tokens': ('生成Token总数', int),
            'request_throughput': ('请求吞吐量', float),
            'output_token_throughput': ('输出Token吞吐量', float),
            'total_token_throughput': ('总Token吞吐量', float),
            'avg_ttft': ('平均TTFT', float),
            'median_ttft': ('中位TTFT', float),
            'p99_ttft': ('P99 TTFT', float),
            'avg_tpot': ('平均TPOT', float),
            'median_tpot': ('中位TPOT', float),
            'p99_tpot': ('P99 TPOT', float),
        }

        for field, (display_name, data_type) in numeric_fields.items():
            if field in data and data[field] is not None:
                try:
                    value = data_type(data[field])
                    # 只检查是否为负数
                    if value < 0:
                        errors.append(f"{display_name}不能为负数")
                except (ValueError, TypeError):
                    # 如果转换失败，忽略错误，让数据库处理
                    pass

        return errors

    @auth('model_storage.performance_test_data.view')
    def get(self, request, pk=None):
        """获取性能测试数据列表或单个数据"""
        if pk:
            try:
                data = PerformanceTestData.objects.get(pk=pk)
                return json_response(data.to_dict())
            except PerformanceTestData.DoesNotExist:
                return json_response(error=f"性能测试数据 with id {pk} does not exist.", status=404)

        # 获取性能测试数据，支持按模型名称过滤
        queryset = PerformanceTestData.objects.all()

        # 如果提供了model_name参数，则按模型名称过滤
        model_name = request.GET.get('model_name')
        if model_name:
            queryset = queryset.filter(model_name=model_name)

        data_list = queryset.order_by('-created_at')
        result = [data.to_dict() for data in data_list]
        return json_response(result)

    @auth('model_storage.performance_test_data.add')
    def post(self, request):
        """创建新性能测试数据"""
        try:
            data = json.loads(request.body)

            # 数据验证
            validation_errors = self._validate_performance_data(data)
            if validation_errors:
                return json_response(error=f"数据验证失败: {'; '.join(validation_errors)}", status=400)

            # 检查重复数据
            if data.get('filename'):
                existing = PerformanceTestData.objects.filter(
                    model_name=data.get('model_name', ''),
                    filename=data.get('filename')
                ).first()
                if existing:
                    return json_response(error=f"文件名 '{data.get('filename')}' 已存在", status=400)

            performance_data = PerformanceTestData.objects.create(
                model_name=data.get('model_name', ''),
                filename=data.get('filename', ''),
                success_requests=self._safe_int(data.get('success_requests')),
                benchmark_duration=self._safe_float(data.get('benchmark_duration')),
                input_tokens=self._safe_int(data.get('input_tokens')),
                output_tokens=self._safe_int(data.get('output_tokens')),
                request_throughput=self._safe_float(data.get('request_throughput')),
                output_token_throughput=self._safe_float(data.get('output_token_throughput')),
                total_token_throughput=self._safe_float(data.get('total_token_throughput')),
                avg_ttft=self._safe_float(data.get('avg_ttft')),
                median_ttft=self._safe_float(data.get('median_ttft')),
                p99_ttft=self._safe_float(data.get('p99_ttft')),
                avg_tpot=self._safe_float(data.get('avg_tpot')),
                median_tpot=self._safe_float(data.get('median_tpot')),
                p99_tpot=self._safe_float(data.get('p99_tpot')),
                # 模型基础信息部分
                machine_model=data.get('machine_model', ''),
                dataset=data.get('dataset', ''),
                data_type=data.get('data_type', ''),
                framework=data.get('framework', ''),
                framework_version=data.get('framework_version', ''),
                # 系统信息部分
                topology=data.get('topology', ''),
                cpu=data.get('cpu', ''),
                memory=data.get('memory', ''),
                fan_mode=data.get('fan_mode', ''),
                iommu_status=data.get('iommu_status', ''),
                network_card=data.get('network_card', ''),
                cpu_mode=data.get('cpu_mode', ''),
                os_kernel=data.get('os_kernel', ''),
                network_type=data.get('network_type', ''),
                bios_version=data.get('bios_version', ''),
                gpu_firmware_version=data.get('gpu_firmware_version', ''),
                gpu_driver_version=data.get('gpu_driver_version', ''),
                image_version_info=data.get('image_version_info', ''),
                remarks=data.get('remarks', '')
            )

            return json_response(performance_data.to_dict())
        except ValueError as e:
            return json_response(error=f"数据类型错误: {e}", status=400)
        except Exception as e:
            return json_response(error=f"创建失败: {e}", status=500)

    @auth('model_storage.performance_test_data.edit')
    def put(self, request, pk):
        """更新性能测试数据"""
        try:
            data = json.loads(request.body)
            performance_data = PerformanceTestData.objects.get(pk=pk)

            # 数据验证
            validation_errors = self._validate_performance_data(data, is_update=True)
            if validation_errors:
                return json_response(error=f"数据验证失败: {'; '.join(validation_errors)}", status=400)

            # 检查文件名重复（排除自身）
            if data.get('filename') and data.get('filename') != performance_data.filename:
                existing = PerformanceTestData.objects.filter(
                    model_name=data.get('model_name', performance_data.model_name),
                    filename=data.get('filename')
                ).exclude(pk=pk).first()
                if existing:
                    return json_response(error=f"文件名 '{data.get('filename')}' 已存在", status=400)

            # 更新字段（使用安全转换）
            performance_data.model_name = data.get('model_name', performance_data.model_name)
            performance_data.filename = data.get('filename', performance_data.filename)

            # 只有当字段存在于请求数据中时才更新
            if 'success_requests' in data:
                performance_data.success_requests = self._safe_int(data.get('success_requests'), performance_data.success_requests)
            if 'benchmark_duration' in data:
                performance_data.benchmark_duration = self._safe_float(data.get('benchmark_duration'), performance_data.benchmark_duration)
            if 'input_tokens' in data:
                performance_data.input_tokens = self._safe_int(data.get('input_tokens'), performance_data.input_tokens)
            if 'output_tokens' in data:
                performance_data.output_tokens = self._safe_int(data.get('output_tokens'), performance_data.output_tokens)
            if 'request_throughput' in data:
                performance_data.request_throughput = self._safe_float(data.get('request_throughput'), performance_data.request_throughput)
            if 'output_token_throughput' in data:
                performance_data.output_token_throughput = self._safe_float(data.get('output_token_throughput'), performance_data.output_token_throughput)
            if 'total_token_throughput' in data:
                performance_data.total_token_throughput = self._safe_float(data.get('total_token_throughput'), performance_data.total_token_throughput)
            if 'avg_ttft' in data:
                performance_data.avg_ttft = self._safe_float(data.get('avg_ttft'), performance_data.avg_ttft)
            if 'median_ttft' in data:
                performance_data.median_ttft = self._safe_float(data.get('median_ttft'), performance_data.median_ttft)
            if 'p99_ttft' in data:
                performance_data.p99_ttft = self._safe_float(data.get('p99_ttft'), performance_data.p99_ttft)
            if 'avg_tpot' in data:
                performance_data.avg_tpot = self._safe_float(data.get('avg_tpot'), performance_data.avg_tpot)
            if 'median_tpot' in data:
                performance_data.median_tpot = self._safe_float(data.get('median_tpot'), performance_data.median_tpot)
            if 'p99_tpot' in data:
                performance_data.p99_tpot = self._safe_float(data.get('p99_tpot'), performance_data.p99_tpot)

            # 更新模型基础信息字段
            if 'machine_model' in data:
                performance_data.machine_model = data.get('machine_model', performance_data.machine_model)
            if 'dataset' in data:
                performance_data.dataset = data.get('dataset', performance_data.dataset)
            if 'data_type' in data:
                performance_data.data_type = data.get('data_type', performance_data.data_type)
            if 'framework' in data:
                performance_data.framework = data.get('framework', performance_data.framework)
            if 'framework_version' in data:
                performance_data.framework_version = data.get('framework_version', performance_data.framework_version)

            # 更新系统信息字段
            if 'topology' in data:
                performance_data.topology = data.get('topology', performance_data.topology)
            if 'cpu' in data:
                performance_data.cpu = data.get('cpu', performance_data.cpu)
            if 'memory' in data:
                performance_data.memory = data.get('memory', performance_data.memory)
            if 'fan_mode' in data:
                performance_data.fan_mode = data.get('fan_mode', performance_data.fan_mode)
            if 'iommu_status' in data:
                performance_data.iommu_status = data.get('iommu_status', performance_data.iommu_status)
            if 'network_card' in data:
                performance_data.network_card = data.get('network_card', performance_data.network_card)
            if 'cpu_mode' in data:
                performance_data.cpu_mode = data.get('cpu_mode', performance_data.cpu_mode)
            if 'os_kernel' in data:
                performance_data.os_kernel = data.get('os_kernel', performance_data.os_kernel)
            if 'network_type' in data:
                performance_data.network_type = data.get('network_type', performance_data.network_type)
            if 'bios_version' in data:
                performance_data.bios_version = data.get('bios_version', performance_data.bios_version)
            if 'gpu_firmware_version' in data:
                performance_data.gpu_firmware_version = data.get('gpu_firmware_version', performance_data.gpu_firmware_version)
            if 'gpu_driver_version' in data:
                performance_data.gpu_driver_version = data.get('gpu_driver_version', performance_data.gpu_driver_version)
            if 'image_version_info' in data:
                performance_data.image_version_info = data.get('image_version_info', performance_data.image_version_info)
            if 'remarks' in data:
                performance_data.remarks = data.get('remarks', performance_data.remarks)

            performance_data.save()
            return json_response(performance_data.to_dict())
        except PerformanceTestData.DoesNotExist:
            return json_response(error="性能测试数据不存在", status=404)
        except ValueError as e:
            return json_response(error=f"数据类型错误: {e}", status=400)
        except Exception as e:
            return json_response(error=f"更新失败: {e}", status=500)

    @auth('model_storage.performance_test_data.del')
    def delete(self, request, pk):
        """删除性能测试数据"""
        try:
            performance_data = PerformanceTestData.objects.get(pk=pk)
            performance_data.delete()
            return json_response({'message': '删除成功'})
        except PerformanceTestData.DoesNotExist:
            return json_response(error="性能测试数据不存在", status=404)
        except Exception as e:
            return json_response(error=f"删除失败: {e}", status=500)


@method_decorator(csrf_exempt, name='dispatch')
class PerformanceTestDataBatchView(View):
    """性能测试数据批量操作API"""

    @auth('model_storage.performance_test_data.add')
    def post(self, request):
        """批量创建性能测试数据"""
        from django.db import transaction

        try:
            request_data = json.loads(request.body)
            data_list = request_data.get('data', [])

            if not data_list:
                return json_response(error="没有要创建的数据", status=400)

            if len(data_list) > 1000:
                return json_response(error="批量创建数据不能超过1000条", status=400)

            created_objects = []
            errors = []

            # 预验证所有数据
            for i, data in enumerate(data_list):
                validation_errors = self._validate_performance_data(data)
                if validation_errors:
                    errors.append(f"第{i+1}条数据验证失败: {'; '.join(validation_errors)}")

            # 如果有验证错误，直接返回
            if errors:
                return json_response({
                    'created': [],
                    'errors': errors,
                    'message': f'数据验证失败，共{len(errors)}个错误'
                }, status=400)

            # 使用事务批量创建
            with transaction.atomic():
                for i, data in enumerate(data_list):
                    try:
                        # 检查重复数据
                        if data.get('filename'):
                            existing = PerformanceTestData.objects.filter(
                                model_name=data.get('model_name', ''),
                                filename=data.get('filename')
                            ).first()
                            if existing:
                                errors.append(f"第{i+1}条数据：文件名 '{data.get('filename')}' 已存在")
                                continue

                        # 需要获取 PerformanceTestDataView 实例来使用安全转换方法
                        view_instance = PerformanceTestDataView()
                        performance_data = PerformanceTestData.objects.create(
                            model_name=data.get('model_name', ''),
                            filename=data.get('filename', ''),
                            success_requests=view_instance._safe_int(data.get('success_requests')),
                            benchmark_duration=view_instance._safe_float(data.get('benchmark_duration')),
                            input_tokens=view_instance._safe_int(data.get('input_tokens')),
                            output_tokens=view_instance._safe_int(data.get('output_tokens')),
                            request_throughput=view_instance._safe_float(data.get('request_throughput')),
                            output_token_throughput=view_instance._safe_float(data.get('output_token_throughput')),
                            total_token_throughput=view_instance._safe_float(data.get('total_token_throughput')),
                            avg_ttft=view_instance._safe_float(data.get('avg_ttft')),
                            median_ttft=view_instance._safe_float(data.get('median_ttft')),
                            p99_ttft=view_instance._safe_float(data.get('p99_ttft')),
                            avg_tpot=view_instance._safe_float(data.get('avg_tpot')),
                            median_tpot=view_instance._safe_float(data.get('median_tpot')),
                            p99_tpot=view_instance._safe_float(data.get('p99_tpot')),
                            # 模型基础信息部分
                            machine_model=data.get('machine_model', ''),
                            dataset=data.get('dataset', ''),
                            data_type=data.get('data_type', ''),
                            framework=data.get('framework', ''),
                            framework_version=data.get('framework_version', ''),
                            # 系统信息部分
                            topology=data.get('topology', ''),
                            cpu=data.get('cpu', ''),
                            memory=data.get('memory', ''),
                            fan_mode=data.get('fan_mode', ''),
                            iommu_status=data.get('iommu_status', ''),
                            network_card=data.get('network_card', ''),
                            cpu_mode=data.get('cpu_mode', ''),
                            os_kernel=data.get('os_kernel', ''),
                            network_type=data.get('network_type', ''),
                            bios_version=data.get('bios_version', ''),
                            gpu_firmware_version=data.get('gpu_firmware_version', ''),
                            gpu_driver_version=data.get('gpu_driver_version', ''),
                            image_version_info=data.get('image_version_info', ''),
                            remarks=data.get('remarks', '')
                        )
                        created_objects.append(performance_data.to_dict())
                    except Exception as e:
                        errors.append(f"第{i+1}条数据创建失败: {str(e)}")

            if errors:
                return json_response({
                    'created': created_objects,
                    'errors': errors,
                    'message': f'批量创建完成，成功{len(created_objects)}条，失败{len(errors)}条'
                }, status=207)  # 207 Multi-Status

            return json_response({
                'created': created_objects,
                'message': f'批量创建成功，共{len(created_objects)}条'
            })

        except Exception as e:
            return json_response(error=f"批量创建失败: {e}", status=500)

    @auth('model_storage.performance_test_data.edit')
    def put(self, request):
        """批量更新性能测试数据"""
        from django.db import transaction

        try:
            request_data = json.loads(request.body)
            data_list = request_data.get('data', [])

            if not data_list:
                return json_response(error="没有要更新的数据", status=400)

            if len(data_list) > 1000:
                return json_response(error="批量更新数据不能超过1000条", status=400)

            updated_objects = []
            errors = []

            # 预验证所有数据
            for i, data in enumerate(data_list):
                data_id = data.get('id')
                if not data_id:
                    errors.append(f"第{i+1}条数据缺少ID")
                    continue

                validation_errors = self._validate_performance_data(data, is_update=True)
                if validation_errors:
                    errors.append(f"第{i+1}条数据验证失败: {'; '.join(validation_errors)}")

            # 如果有验证错误，直接返回
            if errors:
                return json_response({
                    'updated': [],
                    'errors': errors,
                    'message': f'数据验证失败，共{len(errors)}个错误'
                }, status=400)

            # 使用事务批量更新
            with transaction.atomic():
                for i, data in enumerate(data_list):
                    try:
                        data_id = data.get('id')
                        performance_data = PerformanceTestData.objects.get(pk=data_id)

                        # 检查文件名重复（排除自身）
                        if data.get('filename') and data.get('filename') != performance_data.filename:
                            existing = PerformanceTestData.objects.filter(
                                model_name=data.get('model_name', performance_data.model_name),
                                filename=data.get('filename')
                            ).exclude(pk=data_id).first()
                            if existing:
                                errors.append(f"第{i+1}条数据：文件名 '{data.get('filename')}' 已存在")
                                continue

                        # 更新字段（使用安全转换）
                        view_instance = PerformanceTestDataView()
                        performance_data.model_name = data.get('model_name', performance_data.model_name)
                        performance_data.filename = data.get('filename', performance_data.filename)

                        # 只有当字段存在于请求数据中时才更新
                        if 'success_requests' in data:
                            performance_data.success_requests = view_instance._safe_int(data.get('success_requests'), performance_data.success_requests)
                        if 'benchmark_duration' in data:
                            performance_data.benchmark_duration = view_instance._safe_float(data.get('benchmark_duration'), performance_data.benchmark_duration)
                        if 'input_tokens' in data:
                            performance_data.input_tokens = view_instance._safe_int(data.get('input_tokens'), performance_data.input_tokens)
                        if 'output_tokens' in data:
                            performance_data.output_tokens = view_instance._safe_int(data.get('output_tokens'), performance_data.output_tokens)
                        if 'request_throughput' in data:
                            performance_data.request_throughput = view_instance._safe_float(data.get('request_throughput'), performance_data.request_throughput)
                        if 'output_token_throughput' in data:
                            performance_data.output_token_throughput = view_instance._safe_float(data.get('output_token_throughput'), performance_data.output_token_throughput)
                        if 'total_token_throughput' in data:
                            performance_data.total_token_throughput = view_instance._safe_float(data.get('total_token_throughput'), performance_data.total_token_throughput)
                        if 'avg_ttft' in data:
                            performance_data.avg_ttft = view_instance._safe_float(data.get('avg_ttft'), performance_data.avg_ttft)
                        if 'median_ttft' in data:
                            performance_data.median_ttft = view_instance._safe_float(data.get('median_ttft'), performance_data.median_ttft)
                        if 'p99_ttft' in data:
                            performance_data.p99_ttft = view_instance._safe_float(data.get('p99_ttft'), performance_data.p99_ttft)
                        if 'avg_tpot' in data:
                            performance_data.avg_tpot = view_instance._safe_float(data.get('avg_tpot'), performance_data.avg_tpot)
                        if 'median_tpot' in data:
                            performance_data.median_tpot = view_instance._safe_float(data.get('median_tpot'), performance_data.median_tpot)
                        if 'p99_tpot' in data:
                            performance_data.p99_tpot = view_instance._safe_float(data.get('p99_tpot'), performance_data.p99_tpot)

                        performance_data.save()
                        updated_objects.append(performance_data.to_dict())

                    except PerformanceTestData.DoesNotExist:
                        errors.append(f"第{i+1}条数据不存在 (ID: {data.get('id')})")
                    except ValueError as e:
                        errors.append(f"第{i+1}条数据类型错误: {str(e)}")
                    except Exception as e:
                        errors.append(f"第{i+1}条数据更新失败: {str(e)}")

            if errors:
                return json_response({
                    'updated': updated_objects,
                    'errors': errors,
                    'message': f'批量更新完成，成功{len(updated_objects)}条，失败{len(errors)}条'
                }, status=207)  # 207 Multi-Status

            return json_response({
                'updated': updated_objects,
                'message': f'批量更新成功，共{len(updated_objects)}条'
            })

        except Exception as e:
            return json_response(error=f"批量更新失败: {e}", status=500)


@method_decorator(csrf_exempt, name='dispatch')
class ModelPerformanceDataView(View):
    """模型性能数据管理API - 一个模型一条记录"""

    @auth('model_storage.model_performance_data.view')
    def get(self, request, pk=None):
        """获取模型性能数据列表或单个数据"""
        if pk:
            try:
                data = ModelPerformanceData.objects.get(pk=pk)
                return json_response(data.to_dict())
            except ModelPerformanceData.DoesNotExist:
                return json_response(error=f"模型性能数据 with id {pk} does not exist.", status=404)

        # 获取模型性能数据，支持按模型名称过滤
        queryset = ModelPerformanceData.objects.all()

        # 如果提供了model_name参数，则按模型名称过滤
        model_name = request.GET.get('model_name')
        if model_name:
            queryset = queryset.filter(model_name=model_name)

        data_list = queryset.order_by('-updated_at')
        result = [data.to_dict() for data in data_list]
        return json_response(result)

    @auth('model_storage.model_performance_data.add')
    def post(self, request):
        """创建新模型性能数据"""
        try:
            data = json.loads(request.body)
            model_name = data.get('model_name')

            if not model_name:
                return json_response(error="缺少必要字段：模型名称", status=400)

            # 检查是否已存在相同名称的模型
            if ModelPerformanceData.objects.filter(model_name=model_name).exists():
                return json_response(error="相同名称的模型已存在", status=400)

            # 处理测试结果数据
            test_results = data.get('test_results', [])

            model_data = ModelPerformanceData.objects.create(
                model_name=model_name,
                framework=data.get('framework', ''),
                framework_version=data.get('framework_version', ''),
                model_version=data.get('model_version', ''),
                total_test_files=len(test_results),
                last_test_date=timezone.now() if test_results else None
            )
            model_data.set_test_results(test_results)

            return json_response(model_data.to_dict(), status=201)

        except json.JSONDecodeError:
            return json_response(error="无效的JSON数据", status=400)
        except Exception as e:
            return json_response(error=f"创建失败: {e}", status=500)

    @auth('model_storage.model_performance_data.edit')
    def put(self, request, pk=None):
        """更新模型性能数据"""
        try:
            data = json.loads(request.body)
            model_data_id = pk or data.get('id')

            if not model_data_id:
                return json_response(error="缺少模型数据ID", status=400)

            model_data = ModelPerformanceData.objects.get(pk=model_data_id)

            # 更新字段
            if 'model_name' in data:
                # 检查新名称是否与其他记录冲突
                if (data['model_name'] != model_data.model_name and
                    ModelPerformanceData.objects.filter(model_name=data['model_name']).exists()):
                    return json_response(error="模型名称已存在", status=400)
                model_data.model_name = data['model_name']

            if 'framework' in data:
                model_data.framework = data['framework']
            if 'framework_version' in data:
                model_data.framework_version = data['framework_version']
            if 'model_version' in data:
                model_data.model_version = data['model_version']

            # 更新测试结果
            if 'test_results' in data:
                model_data.set_test_results(data['test_results'])
                model_data.update_statistics()

            model_data.save()
            return json_response(model_data.to_dict())

        except ModelPerformanceData.DoesNotExist:
            return json_response(error="模型性能数据不存在", status=404)
        except json.JSONDecodeError:
            return json_response(error="无效的JSON数据", status=400)
        except Exception as e:
            return json_response(error=f"更新失败: {e}", status=500)

    @auth('model_storage.model_performance_data.del')
    def delete(self, request, pk=None):
        """删除模型性能数据"""
        try:
            data = json.loads(request.body) if request.body else {}
            model_data_id = pk or data.get('id')

            if not model_data_id:
                return json_response(error="缺少模型数据ID", status=400)

            model_data = ModelPerformanceData.objects.get(pk=model_data_id)
            model_data.delete()
            return json_response({'message': '删除成功'})

        except ModelPerformanceData.DoesNotExist:
            return json_response(error="模型性能数据不存在", status=404)
        except Exception as e:
            return json_response(error=f"删除失败: {e}", status=500)


@method_decorator(csrf_exempt, name='dispatch')
class ModelPerformanceDataBatchView(View):
    """模型性能数据批量操作API"""

    @auth('model_storage.model_performance_data.edit')
    def post(self, request):
        """批量导入/覆盖模型性能数据"""
        try:
            data = json.loads(request.body)
            model_name = data.get('model_name')
            test_results = data.get('test_results', [])

            if not model_name:
                return json_response(error="缺少模型名称", status=400)

            if not isinstance(test_results, list):
                return json_response(error="test_results必须是数组", status=400)

            # 过滤空行和无效数据
            filtered_results = []
            for result in test_results:
                if not isinstance(result, dict):
                    continue

                # 检查关键字段是否为空
                filename = result.get('filename', '').strip()
                success_requests = result.get('success_requests')

                # 跳过空行：文件名为空或者成功请求数为空/0
                if not filename or success_requests is None or success_requests == 0:
                    continue

                # 清理数据：去除空字符串，转换数据类型
                cleaned_result = {}
                for key, value in result.items():
                    if isinstance(value, str):
                        cleaned_value = value.strip()
                        # 空字符串保持为空字符串，不转换为None
                        cleaned_result[key] = cleaned_value
                    elif isinstance(value, (int, float)):
                        # 数值类型保持原样
                        cleaned_result[key] = value
                    else:
                        cleaned_result[key] = value

                filtered_results.append(cleaned_result)

            # 如果过滤后没有有效数据
            if not filtered_results:
                return json_response(error="没有有效的测试数据，请检查Excel文件内容", status=400)

            # 获取或创建模型记录
            model_data, created = ModelPerformanceData.objects.get_or_create(
                model_name=model_name,
                defaults={
                    'framework': data.get('framework', ''),
                    'framework_version': data.get('framework_version', ''),
                    'model_version': data.get('model_version', ''),
                    'total_test_files': len(filtered_results),
                    'last_test_date': timezone.now() if filtered_results else None
                }
            )

            if created:
                model_data.set_test_results(filtered_results)

            if not created:
                # 覆盖现有数据
                model_data.replace_all_results(filtered_results)
                if 'framework' in data:
                    model_data.framework = data['framework']
                if 'framework_version' in data:
                    model_data.framework_version = data['framework_version']
                if 'model_version' in data:
                    model_data.model_version = data['model_version']
                model_data.save()

            # 统计信息
            original_count = len(test_results)
            filtered_count = len(filtered_results)
            skipped_count = original_count - filtered_count

            message = f"{'创建' if created else '更新'}成功，共{filtered_count}条有效测试结果"
            if skipped_count > 0:
                message += f"，跳过{skipped_count}条空行"

            return json_response({
                'model_data': model_data.to_dict(),
                'created': created,
                'message': message,
                'statistics': {
                    'total_rows': original_count,
                    'valid_rows': filtered_count,
                    'skipped_rows': skipped_count
                }
            })

        except json.JSONDecodeError:
            return json_response(error="无效的JSON数据", status=400)
        except Exception as e:
            return json_response(error=f"批量操作失败: {e}", status=500)


@method_decorator(csrf_exempt, name='dispatch')
class GPURankingView(View):
    """GPU排行榜API"""

    def get(self, request):
        """获取GPU排行榜数据"""
        # 检查开发者后门token
        token = request.GET.get('token') or request.META.get('HTTP_TOKEN')
        if token != '1':
            # 正常权限验证
            # @auth('model_storage.performance_test_data.view')
            pass

        try:
            # 避免函数内后导入导致的未绑定错误，提前导入模型
            from .models import GPUDevice, TestTask, PerformanceTestData
            # 获取查询参数
            incoming_ranking_type = request.GET.get('ranking_type', 'tokens_per_second')
            # 兼容前端与旧版参数命名
            ranking_type_alias = {
                'tokens_per_second': 'output_token_throughput',
                'ttft_ms': 'avg_ttft',
                'output_token_throughput': 'output_token_throughput',
                'avg_ttft': 'avg_ttft',
            }
            ranking_type = ranking_type_alias.get(incoming_ranking_type, 'output_token_throughput')
            
            logger.info(f"[GPU排行榜] 开始获取排行榜数据，排行类型: {ranking_type}")
            
            # 从GPUDevice表获取有性能数据的GPU设备
            gpu_devices = GPUDevice.objects.all()
            logger.info(f"[GPU排行榜] GPUDevice表中共有{gpu_devices.count()}条记录")
            
            # 不以GPUDevice表内置字段过滤，全部纳入待评估列表，后续以最佳指标判断是否有效
            valid_gpus = list(gpu_devices)
            logger.info(f"[GPU排行榜] 参与评估的GPU数量: {len(valid_gpus)}")
            
            if not valid_gpus:
                logger.warning(f"[GPU排行榜] 没有找到有效的GPU性能数据")
                return json_response({
                    'ranking_type': ranking_type,
                    'rankings': [],
                    'total_gpus': 0,
                    'ranking_description': '暂无有效的GPU性能数据用于排行榜'
                })
            
            # 构建排行榜数据（以每个GPU最佳模型数据作为参赛依据）
            from django.db.models import Max, Min

            rankings = []

            for gpu in valid_gpus:
                # 统计该GPU相关的测试任务
                try:
                    task_qs = TestTask.objects.filter(gpu_model=gpu.name)
                except Exception:
                    task_qs = TestTask.objects.none()

                # 从 TestTask 取最佳指标（tok/s 越大越好；ttft 越小越好）
                best_tps = None
                best_ttft = None
                test_tasks_with_metrics = 0

                if task_qs.exists():
                    def _to_pos_float(v):
                        try:
                            f = float(v)
                            return f if f > 0 else None
                        except Exception:
                            return None
                    vals = task_qs.values('tokens_per_second', 'ttft_ms')
                    tps_list = [v for v in (_to_pos_float(x['tokens_per_second']) for x in vals) if v is not None]
                    ttft_list = [v for v in (_to_pos_float(x['ttft_ms']) for x in vals) if v is not None]
                    best_tps = max(tps_list) if tps_list else best_tps
                    best_ttft = min(ttft_list) if ttft_list else best_ttft
                    test_tasks_with_metrics = max(len(tps_list), len(ttft_list))

                # 若 TestTask 没有数据，则回退到 PerformanceTestData（优先外键gpu_device，其次machine_model）
                if (best_tps is None or best_tps == 0) or (best_ttft is None or best_ttft == 0):
                    try:
                        from django.db.models import Q as _Q
                        # 先尝试严格匹配，再尝试忽略大小写和空白
                        perf_qs = PerformanceTestData.objects.filter(_Q(gpu_device=gpu) | _Q(machine_model=gpu.name))
                        if not perf_qs.exists():
                            perf_qs = PerformanceTestData.objects.filter(machine_model__iexact=gpu.name)
                    except Exception:
                        perf_qs = PerformanceTestData.objects.none()

                    if (best_tps is None or best_tps == 0) and perf_qs.exists():
                        best_tps = perf_qs.filter(output_token_throughput__gt=0).aggregate(m=Max('output_token_throughput'))['m']
                    if (best_ttft is None or best_ttft == 0) and perf_qs.exists():
                        best_ttft = perf_qs.filter(avg_ttft__gt=0).aggregate(m=Min('avg_ttft'))['m']

                # 最后回退到 ModelPerformanceData.test_results（按 machine_model 字符串匹配）
                if (best_tps is None or best_tps == 0) or (best_ttft is None or best_ttft == 0):
                    try:
                        from .models import ModelPerformanceData
                        def _norm(s: str) -> str:
                            return ''.join(ch for ch in (s or '').lower() if ch.isalnum())
                        target = _norm(gpu.name)
                        for mpd in ModelPerformanceData.objects.all():
                            for row in mpd.get_test_results() or []:
                                if _norm(str(row.get('machine_model', ''))) == target:
                                    tps = row.get('output_token_throughput')
                                    ttft = row.get('avg_ttft')
                                    if isinstance(tps, (int, float)) and tps > 0:
                                        best_tps = max(best_tps or 0, float(tps))
                                    if isinstance(ttft, (int, float)) and ttft > 0:
                                        best_ttft = min(best_ttft or float(ttft), float(ttft)) if best_ttft else float(ttft)
                    except Exception:
                        pass

                # 不再回退到GPU表字段，避免伪数据；无数据则记为0，后续会被过滤
                if best_tps is None:
                    best_tps = 0
                if best_ttft is None:
                    best_ttft = 0

                tested_models = gpu.tested_models_auto
                test_count = test_tasks_with_metrics if test_tasks_with_metrics else (len(tested_models) if tested_models else 0)

                ranking_data = {
                    # 前端字段
                    'gpu_model': gpu.name,
                    'vendor': gpu.vendor,
                    'best_output_token_throughput': round(float(best_tps or 0), 2),
                    'best_ttft_ms': round(float(best_ttft or 0), 2),
                    'test_count': int(test_count or 0),
                    'tested_models': tested_models[:3] if tested_models else [],
                    # 兼容旧字段（可选）
                    'tokens_per_second': round(float(best_tps or 0), 2),
                    'ttft_ms': round(float(best_ttft or 0), 2),
                    'description': f'厂商: {gpu.vendor}, 测试模型数: {test_count}'
                }
                rankings.append(ranking_data)

                logger.info(
                    f"[GPU排行榜] GPU {gpu.name}: best_tokens_per_second={ranking_data['best_output_token_throughput']}, best_ttft_ms={ranking_data['best_ttft_ms']}, 测试数={ranking_data['test_count']}"
                )
            
            # 根据排行类型进行排序和过滤
            if ranking_type == 'output_token_throughput':
                rankings = [r for r in rankings if r['best_output_token_throughput'] > 0]
                rankings.sort(key=lambda x: x['best_output_token_throughput'], reverse=True)
                logger.info(f"[GPU排行榜] 按best_output_token_throughput降序排序，有效记录数: {len(rankings)}")
            else:
                rankings = [r for r in rankings if r['best_ttft_ms'] > 0]
                rankings.sort(key=lambda x: x['best_ttft_ms'])
                logger.info(f"[GPU排行榜] 按best_ttft_ms升序排序，有效记录数: {len(rankings)}")
            
            # 限制排行榜最大显示10名选手
            if len(rankings) > 10:
                rankings = rankings[:10]
                logger.info(f"[GPU排行榜] 限制显示前10名选手")
            
            # 添加排名
            for i, ranking in enumerate(rankings):
                ranking['rank'] = i + 1
            
            logger.info(f"[GPU排行榜] 最终排行榜包含{len(rankings)}个GPU")
            
            result = {
                'ranking_type': ranking_type,
                'rankings': rankings,
                'total_gpus': len(rankings),
                'ranking_description': '排行依据：取每个GPU下最佳模型的性能（最大tokens/s或最小TTFT），最多显示前10名'
            }
            
            logger.info(f"[GPU排行榜] 返回结果: total_gpus={len(rankings)}")
            if rankings:
                logger.info(f"[GPU排行榜] 第一名: {rankings[0]}")

            return json_response(result)

        except Exception as e:
            logger.error(f"获取GPU排行榜失败: {e}")
            return json_response(error=f"获取GPU排行榜失败: {e}", status=500)



@method_decorator(csrf_exempt, name='dispatch')
class AverageStatsView(View):
    """平均值统计API"""

    def post(self, request):
        """保存平均值统计数据"""
        # 检查开发者后门token
        token = request.GET.get('token') or request.META.get('HTTP_TOKEN')
        if token != '1':
            # 正常权限验证
            # @auth('model_storage.performance_test_data.add')
            pass

        try:
            data = json.loads(request.body)
            
            # 验证必要字段
            required_fields = ['model_name', 'avg_output_token_throughput', 'avg_ttft', 'data_count']
            if not all(field in data for field in required_fields):
                return json_response(error="缺少必要字段", status=400)

            # 创建或更新平均值统计记录
            avg_stats, created = AverageStats.objects.update_or_create(
                model_name=data['model_name'],
                defaults={
                    'avg_output_token_throughput': data['avg_output_token_throughput'],
                    'avg_ttft': data['avg_ttft'],
                    'data_count': data['data_count']
                }
            )

            logger.info(f"{'创建' if created else '更新'}平均值统计: 模型={data['model_name']}, "
                       f"平均输出token吞吐量={data['avg_output_token_throughput']}, "
                       f"平均TTFT={data['avg_ttft']}, "
                       f"数据条数={data['data_count']}")

            return json_response({
                'message': f'平均值统计{"创建" if created else "更新"}成功',
                'data': avg_stats.to_dict()
            }, status=201)
            
        except json.JSONDecodeError:
            return json_response(error="无效的JSON格式", status=400)
        except Exception as e:
            logger.error(f"保存平均值统计失败: {e}")
            return json_response(error=f"保存平均值统计失败: {e}", status=500)