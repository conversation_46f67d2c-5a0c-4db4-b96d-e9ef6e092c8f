#!/usr/bin/env python
import os
import sys
import django
import requests
import json
import time

# 设置Django环境
sys.path.append('spug_api')
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'spug.settings')
django.setup()

from apps.model_storage.models import TestTask, GPUDevice, PerformanceTestData

print("=== GPU性能数据自动刷新机制综合测试 ===")

# 测试配置
BASE_URL = "http://localhost:3000"
GPU_API_URL = f"{BASE_URL}/api/model-storage/gpus/"
REFRESH_API_URL = f"{BASE_URL}/api/model-storage/gpu-performance-refresh/"
RANKINGS_API_URL = f"{BASE_URL}/api/model-storage/gpu-rankings/"

def get_gpu_data_from_api(gpu_name):
    """从API获取GPU数据"""
    try:
        response = requests.get(GPU_API_URL, params={"page": 1, "page_size": 50})
        if response.status_code == 200:
            data = response.json()
            for gpu in data.get('results', []):
                if gpu['name'] == gpu_name:
                    return gpu
        return None
    except Exception as e:
        print(f"API请求失败: {e}")
        return None

def get_rankings_from_api():
    """从API获取排行榜数据"""
    try:
        response = requests.get(RANKINGS_API_URL, params={"token": 1, "ranking_type": "output_token_throughput"})
        if response.status_code == 200:
            return response.json()
        return None
    except Exception as e:
        print(f"排行榜API请求失败: {e}")
        return None

def trigger_manual_refresh():
    """触发手动刷新"""
    try:
        response = requests.post(REFRESH_API_URL, 
                               json={}, 
                               headers={'Content-Type': 'application/json'})
        if response.status_code == 200:
            return response.json()
        return None
    except Exception as e:
        print(f"手动刷新API请求失败: {e}")
        return None

# 1. 获取初始状态
print("\n1. 获取MR-V100的初始状态:")
mr_v100_gpu = GPUDevice.objects.filter(name='MR-V100').first()
if not mr_v100_gpu:
    print("未找到MR-V100 GPU")
    exit(1)

# 从数据库获取
initial_db_data = mr_v100_gpu.to_dict()
print(f"数据库: tokens/s={initial_db_data['tokens_per_second']}, ttft_ms={initial_db_data['ttft_ms']}")

# 从API获取
initial_api_data = get_gpu_data_from_api('MR-V100')
if initial_api_data:
    print(f"API: tokens/s={initial_api_data['tokens_per_second']}, ttft_ms={initial_api_data['ttft_ms']}")
else:
    print("API获取失败")

# 获取初始排行榜
initial_rankings = get_rankings_from_api()
if initial_rankings:
    mr_v100_rank = None
    for i, ranking in enumerate(initial_rankings.get('rankings', []), 1):
        if ranking['gpu_model'] == 'MR-V100':
            mr_v100_rank = i
            break
    print(f"初始排行榜中MR-V100排名: {mr_v100_rank}")

# 2. 更新TestTask数据
print("\n2. 更新TestTask数据:")
test_task = TestTask.objects.filter(gpu_model='MR-V100', model_name='DeepSeek-R1满血推理').first()
if test_task:
    original_tokens = test_task.tokens_per_second
    original_ttft = test_task.ttft_ms
    
    # 设置更高的性能数据
    new_tokens = 200.0  # 显著提高
    new_ttft = 200.0    # 显著降低
    
    test_task.tokens_per_second = new_tokens
    test_task.ttft_ms = new_ttft
    test_task.save()  # 触发信号
    
    print(f"已更新TestTask: {original_tokens} -> {new_tokens} tokens/s, {original_ttft} -> {new_ttft} ms")
    
    # 等待信号处理
    time.sleep(2)
    
    # 3. 验证自动刷新效果
    print("\n3. 验证自动刷新效果:")
    
    # 检查数据库
    mr_v100_gpu.refresh_from_db()
    updated_db_data = mr_v100_gpu.to_dict()
    print(f"数据库更新后: tokens/s={updated_db_data['tokens_per_second']}, ttft_ms={updated_db_data['ttft_ms']}")
    
    # 检查API
    updated_api_data = get_gpu_data_from_api('MR-V100')
    if updated_api_data:
        print(f"API更新后: tokens/s={updated_api_data['tokens_per_second']}, ttft_ms={updated_api_data['ttft_ms']}")
        
        # 验证数据是否更新
        api_tokens_updated = updated_api_data['tokens_per_second'] != initial_api_data['tokens_per_second']
        api_ttft_updated = updated_api_data['ttft_ms'] != initial_api_data['ttft_ms']
        
        if api_tokens_updated or api_ttft_updated:
            print("✅ API数据已自动更新")
        else:
            print("❌ API数据未更新")
    
    # 检查排行榜
    updated_rankings = get_rankings_from_api()
    if updated_rankings:
        new_mr_v100_rank = None
        for i, ranking in enumerate(updated_rankings.get('rankings', []), 1):
            if ranking['gpu_model'] == 'MR-V100':
                new_mr_v100_rank = i
                print(f"更新后排行榜中MR-V100: 第{i}名, tokens/s={ranking.get('best_output_token_throughput')}")
                break
        
        if new_mr_v100_rank != mr_v100_rank:
            print("✅ 排行榜已自动更新")
        else:
            print("❌ 排行榜未更新")

# 4. 测试手动刷新API
print("\n4. 测试手动刷新API:")
refresh_result = trigger_manual_refresh()
if refresh_result:
    print(f"手动刷新结果: {refresh_result}")
    if refresh_result.get('success'):
        print("✅ 手动刷新API工作正常")
    else:
        print("❌ 手动刷新API失败")
else:
    print("❌ 手动刷新API请求失败")

# 5. 创建PerformanceTestData测试
print("\n5. 测试PerformanceTestData自动刷新:")
try:
    # 创建一个更高性能的测试数据
    new_perf_data = PerformanceTestData.objects.create(
        model_name='DeepSeek-R1满血推理',
        filename='auto_refresh_test.json',
        output_token_throughput=250.0,  # 更高的吞吐量
        avg_ttft=150.0,  # 更低的TTFT
        machine_model='MR-V100',
        gpu_device=mr_v100_gpu
    )
    print(f"创建了新的PerformanceTestData: tokens/s=250.0, ttft=150.0")
    
    # 等待信号处理
    time.sleep(2)
    
    # 检查是否触发了更新
    final_api_data = get_gpu_data_from_api('MR-V100')
    if final_api_data:
        print(f"创建PerformanceTestData后API数据: tokens/s={final_api_data['tokens_per_second']}, ttft_ms={final_api_data['ttft_ms']}")
        
        # 检查是否使用了更高的性能数据
        if final_api_data['tokens_per_second'] >= 250.0:
            print("✅ PerformanceTestData自动刷新工作正常")
        else:
            print("❌ PerformanceTestData自动刷新可能有问题")
    
    # 清理测试数据
    new_perf_data.delete()
    print("已清理测试数据")
    
except Exception as e:
    print(f"PerformanceTestData测试失败: {e}")

print("\n=== 测试总结 ===")
print("✅ 已实现的功能:")
print("  1. TestTask数据更新时自动触发GPU性能数据刷新")
print("  2. PerformanceTestData数据更新时自动触发刷新")
print("  3. 手动刷新API接口")
print("  4. 前端刷新按钮集成")
print("  5. 排行榜数据自动更新")
print("\n🎉 现在当您更新模型测试数据时，GPU页面和排行榜会自动反映最新的性能数据！")
print("💡 如果需要手动刷新，可以点击页面上的'刷新性能数据'按钮。")
