# Generated by Django 2.2.28 on 2025-08-08 15:11

from django.db import migrations, models
import libs.mixins


class Migration(migrations.Migration):

    dependencies = [
        ('model_storage', '0022_auto_20250807_1729'),
    ]

    operations = [
        migrations.CreateModel(
            name='AverageStats',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('model_name', models.CharField(max_length=255, unique=True, verbose_name='模型名称')),
                ('avg_output_token_throughput', models.FloatField(help_text='平均输出Token吞吐量，单位：tokens/s', verbose_name='平均输出Token吞吐量')),
                ('avg_ttft', models.FloatField(help_text='平均首字符时间，单位：毫秒', verbose_name='平均TTFT')),
                ('data_count', models.IntegerField(help_text='用于计算平均值的数据条数', verbose_name='数据条数')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
            ],
            options={
                'verbose_name': '平均值统计',
                'verbose_name_plural': '平均值统计',
                'db_table': 'model_storage_average_stats',
                'ordering': ('-updated_at',),
            },
            bases=(models.Model, libs.mixins.ModelMixin),
        ),
    ]
