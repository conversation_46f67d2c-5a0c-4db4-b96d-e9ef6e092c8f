import os
import sys
import django

# 设置Django环境
sys.path.append('spug_api')
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'spug.settings')
django.setup()

from apps.model_storage.models import AverageStats, PerformanceTestData

print("=== AverageStats表数据 ===")
for stats in AverageStats.objects.all():
    print(f"模型: {stats.model_name}, 吞吐量: {stats.avg_output_token_throughput}")

print("\n=== PerformanceTestData表数据 ===")
print(f"总记录数: {PerformanceTestData.objects.count()}")

print("\n=== 高吞吐量数据 (>100 tokens/s) ===")
high_throughput = PerformanceTestData.objects.filter(output_token_throughput__gt=100)
print(f"高吞吐量数据数量: {high_throughput.count()}")
for data in high_throughput:
    print(f"模型: {data.model_name}, 机器: {data.machine_model}, 吞吐量: {data.output_token_throughput}")

print("\n=== 所有PerformanceTestData数据 ===")
for data in PerformanceTestData.objects.all():
    print(f"ID: {data.id}, 模型: {data.model_name}, 机器: {data.machine_model}, 吞吐量: {data.output_token_throughput}")