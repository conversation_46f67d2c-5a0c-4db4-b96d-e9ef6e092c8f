import React, { useState, useEffect } from 'react';
import { observer } from 'mobx-react';
import { Drawer, Spin, Table, Tag, Descriptions, Button, Modal, Form, Input, Select, message, Card, Row, Col, Statistic, Progress } from 'antd';
import { PlusOutlined, <PERSON>boltOutlined, ExperimentOutlined, UserOutlined, CalendarOutlined, FileTextOutlined, EditOutlined, DeleteOutlined, SaveOutlined, CloseOutlined } from '@ant-design/icons';
import { hasPermission } from 'libs';
import http from 'libs/http';
import TestStepsSidebar from '../../exec/test-case-sets/TestStepsSidebar';
import styles from './Detail.module.less';

const MODEL_TYPES = [
  { value: 'inference', label: '推理' },
  { value: 'training', label: '训练' },
  { value: 'fine_tuning', label: '微调' },
  { value: 'pre-training', label: '预训练' },
  { value: 'optimization', label: '优化' },
];

function GpuDetail({ store }) {
  const [loading, setLoading] = useState(true);
  const [tasks, setTasks] = useState([]);
  const [formVisible, setFormVisible] = useState(false);
  const [refreshKey, setRefreshKey] = useState(0);
  const [form] = Form.useForm();
  const [sidebarVisible, setSidebarVisible] = useState(false);
  const [selectedCaseSet, setSelectedCaseSet] = useState(null);
  const [editingModel, setEditingModel] = useState(null); // 当前正在编辑的模型
  const [editModelName, setEditModelName] = useState(''); // 编辑中的模型名称
  const [addingModel, setAddingModel] = useState(false); // 是否正在添加新模型
  const [newModelName, setNewModelName] = useState(''); // 新模型名称
  const [editTaskVisible, setEditTaskVisible] = useState(false); // 编辑任务弹窗
  const [editingTask, setEditingTask] = useState(null); // 当前编辑的任务
  const [editTaskForm] = Form.useForm(); // 编辑任务表单
  const gpu = store.record;

  // 获取任务数据
  const fetchTasks = () => {
    if (gpu.name) {
      setLoading(true);
      http.get('/api/model-storage/test-tasks/', { params: { gpu_model: gpu.name } })
        .then(res => setTasks(res))
        .finally(() => setLoading(false));
    }
  };

  useEffect(() => {
    fetchTasks();
  }, [gpu.name, refreshKey]);

  const handleAddTask = () => {
    form.validateFields().then(values => {
      const payload = {
        ...values,
        gpu_model: gpu.name,
      };
      http.post('/api/model-storage/test-tasks/', payload)
        .then(() => {
          message.success('添加成功');
          setFormVisible(false);
          form.resetFields();
          setRefreshKey(prev => prev + 1); // 触发刷新
        });
    });
  };

  // 处理测试用例集点击
  const handleCaseSetClick = async (task) => {
    if (!task.test_case_set_info) {
      message.warning('该任务未关联测试用例集');
      return;
    }

    try {
      // 获取完整的测试用例集信息
      const response = await http.get(`/api/exec/test-case-sets/${task.test_case_set_info.id}/`);
      const caseSetData = response.data || response;

      // 将任务的步骤完成状态和进度合并到用例集数据中
      const mergedData = {
        ...caseSetData,
        step_completion_status: task.step_completion_status || {},
        progress: task.progress || 0,
        task_id: task.id  // 保存任务ID用于更新
      };

      setSelectedCaseSet(mergedData);
      setSidebarVisible(true);
    } catch (error) {
      console.error('获取测试用例集详情失败:', error);
      message.error('获取测试用例集详情失败');
    }
  };

  // 更新测试用例集进度
  const handleCaseSetUpdate = (updatedData) => {
    setSelectedCaseSet(updatedData);
    // 刷新任务列表以显示最新进度
    setRefreshKey(prev => prev + 1);
  };

  // 开始编辑模型
  const handleEditModel = (model) => {
    setEditingModel(model);
    setEditModelName(model);
  };

  // 保存编辑的模型
  const handleSaveModel = (originalModel) => {
    if (!editModelName.trim()) {
      message.warning('请输入模型名称');
      return;
    }

    const currentModels = gpu.tested_models_manual || [];

    // 检查是否与其他模型重复（排除自己）
    if (editModelName.trim() !== originalModel && currentModels.includes(editModelName.trim())) {
      message.warning('该模型名称已存在');
      return;
    }

    const updatedModels = currentModels.map(model =>
      model === originalModel ? editModelName.trim() : model
    );
    const manual_models = updatedModels.join(',');

    http.patch(`/api/model-storage/gpus/${gpu.id}/`, { manual_models })
      .then(() => {
        message.success('修改成功');
        setEditingModel(null);
        setEditModelName('');
        // 更新本地数据
        store.record = {
          ...store.record,
          manual_models,
          tested_models_manual: updatedModels
        };
      })
      .catch(() => {
        message.error('修改失败');
      });
  };

  // 取消编辑
  const handleCancelEdit = () => {
    setEditingModel(null);
    setEditModelName('');
  };

  // 添加新模型
  const handleAddModel = () => {
    if (!newModelName.trim()) {
      message.warning('请输入模型名称');
      return;
    }

    const currentModels = gpu.tested_models_manual || [];
    if (currentModels.includes(newModelName.trim())) {
      message.warning('该模型已存在');
      return;
    }

    const updatedModels = [...currentModels, newModelName.trim()];
    const manual_models = updatedModels.join(',');

    http.patch(`/api/model-storage/gpus/${gpu.id}/`, { manual_models })
      .then(() => {
        message.success('添加成功');
        setNewModelName('');
        setAddingModel(false);
        // 更新本地数据
        store.record = {
          ...store.record,
          manual_models,
          tested_models_manual: updatedModels
        };
      })
      .catch(() => {
        message.error('添加失败');
      });
  };

  // 删除模型
  const handleDeleteModel = (modelToDelete) => {
    const currentModels = gpu.tested_models_manual || [];
    const updatedModels = currentModels.filter(model => model !== modelToDelete);
    const manual_models = updatedModels.join(',');

    http.patch(`/api/model-storage/gpus/${gpu.id}/`, { manual_models })
      .then(() => {
        message.success('删除成功');
        // 更新本地数据
        store.record = {
          ...store.record,
          manual_models,
          tested_models_manual: updatedModels
        };
      })
      .catch(() => {
        message.error('删除失败');
      });
  };

  // 编辑测试任务
  const handleEditTask = (task) => {
    setEditingTask(task);
    editTaskForm.setFieldsValue({
      model_name: task.model_name,
      tester: task.tester,
      model_type: task.model_type,
      gpu_model: task.gpu_model_display || task.gpu_model, // GPU型号字段
      priority: task.priority,
      test_status: task.test_status,
      start_date: task.start_date,
      end_date: task.end_date,
      notes: task.notes
    });
    setEditTaskVisible(true);
  };

  // 保存编辑的任务
  const handleSaveEditTask = () => {
    editTaskForm.validateFields().then(values => {
      const payload = {
        ...values,
        id: editingTask.id,
        // 确保GPU型号字段被包含
        gpu_model: values.gpu_model || editingTask.gpu_model
      };

      http.patch(`/api/model-storage/test-tasks/${editingTask.id}/`, payload)
        .then(() => {
          message.success('修改成功');
          setEditTaskVisible(false);
          setEditingTask(null);
          editTaskForm.resetFields();
          fetchTasks(); // 重新加载任务列表
        })
        .catch(() => {
          message.error('修改失败');
        });
    });
  };

  // 删除测试任务
  const handleDeleteTask = (task) => {
    Modal.confirm({
      title: '确认删除',
      content: `确定要删除测试任务 "${task.model_name}" 吗？`,
      okText: '确定',
      cancelText: '取消',
      onOk: () => {
        http.delete(`/api/model-storage/test-tasks/${task.id}/`)
          .then(() => {
            message.success('删除成功');
            // 重新加载任务列表
            fetchTasks();
          })
          .catch(() => {
            message.error('删除失败');
          });
      }
    });
  };

  const columns = [
    {
      title: '模型名称',
      dataIndex: 'model_name',
      key: 'model_name',
      render: (text, record) => {
        // 拼接GPU型号到模型名称前面
        const displayName = record.gpu_model_display ?
          `${record.gpu_model_display}-${text}` : text;
        return (
          <a
            href={`/model-storage/model-data/${encodeURIComponent(text)}`}
            target="_blank"
            rel="noopener noreferrer"
            style={{ color: '#1890ff', textDecoration: 'none' }}
            onMouseEnter={(e) => e.target.style.textDecoration = 'underline'}
            onMouseLeave={(e) => e.target.style.textDecoration = 'none'}
          >
            {displayName}
          </a>
        );
      }
    },
    {
      title: '关键指标',
      key: 'performance_metrics',
      width: 180,
      render: (_, record) => {
        return (
          <div style={{ display: 'flex', flexDirection: 'column', gap: '4px' }}>
            <div style={{ fontSize: '12px' }}>
              <span style={{ color: '#666' }}>吞吐量: </span>
              <span style={{ fontWeight: 'bold', color: '#1890ff' }}>
                {record.tokens_per_second ? `${record.tokens_per_second} tokens/s` : '暂无'}
              </span>
            </div>
            <div style={{ fontSize: '12px' }}>
              <span style={{ color: '#666' }}>首字符: </span>
              <span style={{ fontWeight: 'bold', color: '#52c41a' }}>
                {record.ttft_ms ? `${record.ttft_ms} ms` : '暂无'}
              </span>
            </div>
          </div>
        );
      }
    },
    { title: '测试人员', dataIndex: 'tester', key: 'tester' },
    { title: '模型类型', dataIndex: 'model_type_display', key: 'model_type_display' },
    { title: '优先级', dataIndex: 'priority_display', key: 'priority_display' },
    { title: '测试状态', dataIndex: 'test_status_display', key: 'test_status_display', render: text => <Tag>{text}</Tag> },
    // {
    //   title: '测试用例集',
    //   key: 'test_case_set',
    //   render: (_, record) => {
    //     if (record.test_case_set_info) {
    //       return (
    //         <div style={{ cursor: 'pointer' }} onClick={() => handleCaseSetClick(record)}>
    //           <Tag
    //             color="blue"
    //             icon={<FileTextOutlined />}
    //             style={{
    //               cursor: 'pointer',
    //               transition: 'all 0.3s ease',
    //               ':hover': { backgroundColor: '#1890ff' }
    //             }}
    //           >
    //             {record.test_case_set_info.name}
    //           </Tag>
    //           {record.test_case_set_info.progress_percentage !== undefined && (
    //             <div style={{ marginTop: 4, width: 80 }}>
    //               <Progress
    //                 percent={record.test_case_set_info.progress_percentage}
    //                 size="small"
    //                 strokeColor="#52c41a"
    //                 showInfo={false}
    //               />
    //               <div style={{ fontSize: '11px', color: '#666', textAlign: 'center' }}>
    //                 {record.test_case_set_info.progress_percentage}%
    //               </div>
    //             </div>
    //           )}
    //         </div>
    //       );
    //     }
    //     return <Tag color="default">未关联</Tag>;
    //   }
    // },
    { title: '开始日期', dataIndex: 'start_date', key: 'start_date' },
    { title: '结束日期', dataIndex: 'end_date', key: 'end_date' },
    {
      title: '操作',
      key: 'actions',
      width: 120,
      render: (_, record) => (
        hasPermission('model_storage.test_tasks.edit') && (
          <div style={{ display: 'flex', gap: 8 }}>
            <Button
              type="link"
              size="small"
              icon={<EditOutlined />}
              onClick={() => handleEditTask(record)}
              style={{ color: '#1890ff', padding: 0 }}
            />
            <Button
              type="link"
              size="small"
              icon={<DeleteOutlined />}
              onClick={() => handleDeleteTask(record)}
              style={{ color: '#ff4d4f', padding: 0 }}
            />
          </div>
        )
      )
    }
  ];

  // 计算统计数据
  const getTaskStats = () => {
    const totalTasks = tasks.length;
    const completedTasks = tasks.filter(task => task.status === 'completed').length;
    const runningTasks = tasks.filter(task => task.status === 'running').length;
    const failedTasks = tasks.filter(task => task.status === 'failed').length;
    return { totalTasks, completedTasks, runningTasks, failedTasks };
  };

  const taskStats = getTaskStats();

  return (
    <>
      <Drawer
        title={
          <div className={styles.drawerTitle}>
            <ThunderboltOutlined style={{ marginRight: 8, color: '#667eea' }} />
            GPU设备详情: {gpu.name}
          </div>
        }
        placement="right"
        width={900}
        onClose={() => store.detailVisible = false}
        visible={store.detailVisible}
        className={styles.gpuDetailDrawer}
      >
        <Spin spinning={loading}>
          {/* 基本信息卡片 */}
          <Card className={styles.infoCard} title="基本信息">
            <Row gutter={[24, 16]}>
              <Col span={12}>
                <div className={styles.infoItem}>
                  <span className={styles.infoLabel}>GPU名称</span>
                  <span className={styles.infoValue}>{gpu.name}</span>
                </div>
              </Col>
              <Col span={12}>
                <div className={styles.infoItem}>
                  <span className={styles.infoLabel}>厂商</span>
                  <Tag className={styles.vendorTag}>{gpu.vendor}</Tag>
                </div>
              </Col>
              <Col span={12}>
                <div className={styles.infoItem}>
                  <span className={styles.infoLabel}>吞吐量</span>
                  <span className={styles.infoValue}>
                    {gpu.tokens_per_second ? `${gpu.tokens_per_second} tokens/s` : '暂无数据'}
                  </span>
                </div>
              </Col>
              <Col span={12}>
                <div className={styles.infoItem}>
                  <span className={styles.infoLabel}>首字符时间</span>
                  <span className={styles.infoValue}>
                    {gpu.ttft_ms ? `${gpu.ttft_ms} ms` : '暂无数据'}
                  </span>
                </div>
              </Col>
              <Col span={12}>
                <div className={styles.infoItem}>
                  <span className={styles.infoLabel}>创建时间</span>
                  <span className={styles.infoValue}>{gpu.created_at}</span>
                </div>
              </Col>
              {gpu.description && (
                <Col span={24}>
                  <div className={styles.infoItem}>
                    <span className={styles.infoLabel}>设备描述</span>
                    <div className={styles.description}>{gpu.description}</div>
                  </div>
                </Col>
              )}
            </Row>
          </Card>

          {/* 模型信息卡片 */}
          <Card
            className={styles.modelCard}
            title="关联模型"
            extra={
              hasPermission('model_storage.gpu_management.edit') && (
                <Button
                  type="link"
                  icon={<PlusOutlined />}
                  onClick={() => setAddingModel(true)}
                  style={{ color: '#1890ff' }}
                >
                  添加模型
                </Button>
              )
            }
          >
            <Row gutter={[0, 16]}>
              <Col span={24}>
                <div className={styles.modelSection}>
                  <h4>手动录入模型</h4>
                  <div className={styles.modelTags}>
                    {gpu.tested_models_manual && gpu.tested_models_manual.length > 0 ?
                      gpu.tested_models_manual.map((model, index) => (
                        <div key={`manual-${model}-${index}`} className={styles.modelTagWrapper}>
                          {editingModel === model ? (
                            // 编辑模式
                            <div className={styles.editingTag}>
                              <Input
                                value={editModelName}
                                onChange={(e) => setEditModelName(e.target.value)}
                                onPressEnter={() => handleSaveModel(model)}
                                size="small"
                                className={styles.modelEditInput}
                                style={{ width: 120 }}
                              />
                              <Button
                                type="link"
                                size="small"
                                icon={<SaveOutlined />}
                                onClick={() => handleSaveModel(model)}
                                style={{ color: '#52c41a', padding: '0 4px' }}
                              />
                              <Button
                                type="link"
                                size="small"
                                icon={<CloseOutlined />}
                                onClick={handleCancelEdit}
                                style={{ color: '#ff4d4f', padding: '0 4px' }}
                              />
                            </div>
                          ) : (
                            // 显示模式
                            <div className={styles.modelTagDisplay}>
                              <Tag className={styles.manualModelTag}>
                                {model}
                              </Tag>
                              {hasPermission('model_storage.gpu_management.edit') && (
                                <div className={styles.modelActions}>
                                  <Button
                                    type="link"
                                    size="small"
                                    icon={<EditOutlined />}
                                    onClick={() => handleEditModel(model)}
                                    style={{ color: '#1890ff', padding: '0 4px' }}
                                  />
                                  <Button
                                    type="link"
                                    size="small"
                                    icon={<DeleteOutlined />}
                                    onClick={() => handleDeleteModel(model)}
                                    style={{ color: '#ff4d4f', padding: '0 4px' }}
                                  />
                                </div>
                              )}
                            </div>
                          )}
                        </div>
                      )) :
                      <span className={styles.emptyText}>暂无手动录入模型</span>
                    }

                    {/* 添加新模型的输入框 */}
                    {addingModel && (
                      <div className={styles.addingModelWrapper}>
                        <Input
                          placeholder="输入模型名称"
                          value={newModelName}
                          onChange={(e) => setNewModelName(e.target.value)}
                          onPressEnter={handleAddModel}
                          size="small"
                          className={styles.modelEditInput}
                          style={{ width: 150 }}
                        />
                        <Button
                          type="link"
                          size="small"
                          icon={<SaveOutlined />}
                          onClick={handleAddModel}
                          style={{ color: '#52c41a', padding: '0 4px' }}
                        />
                        <Button
                          type="link"
                          size="small"
                          icon={<CloseOutlined />}
                          onClick={() => {
                            setAddingModel(false);
                            setNewModelName('');
                          }}
                          style={{ color: '#ff4d4f', padding: '0 4px' }}
                        />
                      </div>
                    )}
                  </div>
                </div>
              </Col>
              <Col span={24}>
                <div className={styles.modelSection}>
                  <h4>自动关联模型</h4>
                  <div className={styles.modelTags}>
                    {gpu.tested_models_auto && gpu.tested_models_auto.length > 0 ?
                      gpu.tested_models_auto.map((model, index) => (
                        <Tag key={`${model}-${index}`} className={styles.autoModelTag}>{model}</Tag>
                      )) :
                      <span className={styles.emptyText}>暂无自动关联模型</span>
                    }
                  </div>
                </div>
              </Col>
            </Row>
          </Card>

          {/* 任务统计卡片 */}
          <Row gutter={[16, 16]} style={{ marginBottom: 24 }}>
            <Col span={6}>
              <Card className={styles.statsCard}>
                <Statistic
                  title="总任务数"
                  value={taskStats.totalTasks}
                  prefix={<ExperimentOutlined style={{ color: '#1890ff' }} />}
                  valueStyle={{ color: '#1890ff' }}
                />
              </Card>
            </Col>
            <Col span={6}>
              <Card className={styles.statsCard}>
                <Statistic
                  title="已完成"
                  value={taskStats.completedTasks}
                  prefix={<ExperimentOutlined style={{ color: '#52c41a' }} />}
                  valueStyle={{ color: '#52c41a' }}
                />
              </Card>
            </Col>
            <Col span={6}>
              <Card className={styles.statsCard}>
                <Statistic
                  title="运行中"
                  value={taskStats.runningTasks}
                  prefix={<ExperimentOutlined style={{ color: '#fa8c16' }} />}
                  valueStyle={{ color: '#fa8c16' }}
                />
              </Card>
            </Col>
            <Col span={6}>
              <Card className={styles.statsCard}>
                <Statistic
                  title="失败"
                  value={taskStats.failedTasks}
                  prefix={<ExperimentOutlined style={{ color: '#ff4d4f' }} />}
                  valueStyle={{ color: '#ff4d4f' }}
                />
              </Card>
            </Col>
          </Row>

          {/* 测试任务表格 */}
          <Card
            className={styles.taskCard}
            title="相关测试任务"
            extra={
              <Button
                type="primary"
                icon={<PlusOutlined />}
                onClick={() => setFormVisible(true)}
                className={styles.gradientButton}
              >
                新增任务
              </Button>
            }
          >
            <Table
              rowKey="id"
              dataSource={tasks}
              columns={columns}
              pagination={{ pageSize: 10 }}
              className={styles.taskTable}
            />
          </Card>
        </Spin>
      </Drawer>

      <Modal
        title={
          <div className={styles.modalTitle}>
            <ExperimentOutlined style={{ marginRight: 8, color: '#667eea' }} />
            新增测试任务
          </div>
        }
        visible={formVisible}
        onCancel={() => setFormVisible(false)}
        maskClosable={false}
        destroyOnClose
        width={600}
        className={styles.taskModal}
        footer={[
          <Button key="cancel" onClick={() => setFormVisible(false)}>
            取消
          </Button>,
          <Button
            key="submit"
            type="primary"
            onClick={handleAddTask}
            className={styles.gradientButton}
          >
            创建任务
          </Button>
        ]}
      >
        <Form form={form} layout="vertical" className={styles.taskForm}>
          <Form.Item label="GPU型号">
            <Input
              value={gpu.name}
              disabled
              size="large"
              prefix={<ThunderboltOutlined />}
            />
          </Form.Item>
          <Form.Item
            label="模型名称"
            name="model_name"
            rules={[{ required: true, message: '请输入模型名称' }]}
          >
            <Input
              placeholder="请输入要测试的模型名称"
              size="large"
              prefix={<ExperimentOutlined />}
            />
          </Form.Item>
          <Form.Item
            label="测试人员"
            name="tester"
            rules={[{ required: true, message: '请输入测试人员姓名' }]}
          >
            <Input
              placeholder="请输入测试人员姓名"
              size="large"
              prefix={<UserOutlined />}
            />
          </Form.Item>
          <Form.Item
            label="模型类型"
            name="model_type"
            initialValue="inference"
          >
            <Select size="large" placeholder="请选择模型类型">
              {MODEL_TYPES.map(item => (
                <Select.Option key={item.value} value={item.value}>
                  {item.label}
                </Select.Option>
              ))}
            </Select>
          </Form.Item>
        </Form>
      </Modal>

      {/* 编辑测试任务弹窗 */}
      <Modal
        title="编辑测试任务"
        visible={editTaskVisible}
        onOk={handleSaveEditTask}
        onCancel={() => {
          setEditTaskVisible(false);
          setEditingTask(null);
          editTaskForm.resetFields();
        }}
        width={600}
        okText="保存"
        cancelText="取消"
      >
        <Form
          form={editTaskForm}
          layout="vertical"
          style={{ marginTop: 20 }}
        >
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                label="模型名称"
                name="model_name"
                rules={[{ required: true, message: '请输入模型名称' }]}
              >
                <Input placeholder="请输入模型名称" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                label="测试人员"
                name="tester"
                rules={[{ required: true, message: '请输入测试人员' }]}
              >
                <Input placeholder="请输入测试人员" />
              </Form.Item>
            </Col>
          </Row>
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                label="模型类型"
                name="model_type"
              >
                <Select placeholder="请选择模型类型">
                  {MODEL_TYPES.map(item => (
                    <Select.Option key={item.value} value={item.value}>
                      {item.label}
                    </Select.Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                label="GPU型号"
                name="gpu_model"
              >
                <Input
                  placeholder="GPU型号"
                  disabled
                />
              </Form.Item>
            </Col>
          </Row>
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                label="优先级"
                name="priority"
              >
                <Select placeholder="请选择优先级">
                  <Select.Option value="p1">P1-高</Select.Option>
                  <Select.Option value="p2">P2-中</Select.Option>
                  <Select.Option value="p3">P3-低</Select.Option>
                </Select>
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                label="测试状态"
                name="test_status"
              >
                <Select placeholder="请选择测试状态">
                  <Select.Option value="pending">待开始</Select.Option>
                  <Select.Option value="in_progress">进行中</Select.Option>
                  <Select.Option value="completed">已完成</Select.Option>
                  <Select.Option value="cancelled">已取消</Select.Option>
                  <Select.Option value="blocked">阻塞中</Select.Option>
                  <Select.Option value="delayed">已延期</Select.Option>
                </Select>
              </Form.Item>
            </Col>
          </Row>
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                label="开始日期"
                name="start_date"
              >
                <Input type="date" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                label="结束日期"
                name="end_date"
              >
                <Input type="date" />
              </Form.Item>
            </Col>
          </Row>
          <Form.Item
            label="备注"
            name="notes"
          >
            <Input.TextArea rows={3} placeholder="请输入备注信息" />
          </Form.Item>
        </Form>
      </Modal>

      {/* 测试步骤侧边栏 */}
      <TestStepsSidebar
        visible={sidebarVisible}
        onClose={() => setSidebarVisible(false)}
        caseSet={selectedCaseSet}
        onUpdate={handleCaseSetUpdate}
        taskId={selectedCaseSet?.task_id}
        mode="task"
      />
    </>
  );
}

export default observer(GpuDetail);