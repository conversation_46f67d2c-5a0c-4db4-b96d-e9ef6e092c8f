import React, { useState } from 'react';
import { Button, message, Tooltip } from 'antd';
import { ReloadOutlined } from '@ant-design/icons';
import http from 'libs/http';

function RefreshButton({ onRefresh, gpuNames = null, size = 'default' }) {
  const [loading, setLoading] = useState(false);

  const handleRefresh = async () => {
    setLoading(true);
    try {
      const payload = gpuNames ? { gpu_names: gpuNames } : {};
      
      const response = await http.post('/api/model-storage/gpu-performance-refresh/', payload);
      
      if (response.success) {
        message.success(response.message || 'GPU性能数据刷新成功');
        
        // 调用父组件的刷新回调
        if (onRefresh) {
          onRefresh();
        }
      } else {
        message.error(response.error || 'GPU性能数据刷新失败');
      }
    } catch (error) {
      console.error('刷新GPU性能数据失败:', error);
      message.error('刷新失败，请稍后重试');
    } finally {
      setLoading(false);
    }
  };

  const tooltipTitle = gpuNames 
    ? `刷新指定GPU (${Array.isArray(gpuNames) ? gpuNames.join(', ') : gpuNames}) 的性能数据`
    : '刷新所有GPU的性能数据和排行榜';

  return (
    <Tooltip title={tooltipTitle}>
      <Button
        type="default"
        size={size}
        icon={<ReloadOutlined />}
        loading={loading}
        onClick={handleRefresh}
      >
        {size === 'small' ? '' : '刷新性能数据'}
      </Button>
    </Tooltip>
  );
}

export default RefreshButton;
