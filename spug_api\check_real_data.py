import os
import sys
import django

# 设置Django环境
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'spug.settings')
django.setup()

from apps.model_storage.models import AverageStats, GPUDevice, PerformanceTestData

print('=== 检查AverageStats表数据 ===')
stats = AverageStats.objects.all()
print(f'AverageStats总记录数: {stats.count()}')
for stat in stats:
    print(f'模型: {stat.model_name}, tokens_per_second: {stat.avg_output_token_throughput}, ttft_ms: {stat.avg_ttft}')

print('\n=== 检查GPUDevice表数据 ===')
gpus = GPUDevice.objects.all()
print(f'GPUDevice总记录数: {gpus.count()}')
for gpu in gpus:
    print(f'GPU: {gpu.name}, 厂商: {gpu.vendor}')

print('\n=== 检查PerformanceTestData表数据 ===')
perf_data = PerformanceTestData.objects.all()
print(f'PerformanceTestData总记录数: {perf_data.count()}')
for data in perf_data[:10]:  # 只显示前10条
    print(f'机器模型: {data.machine_model}, 输出吞吐量: {data.output_token_throughput}, TTFT: {data.ttft_ms}')

print('\n=== 搜索K100相关数据 ===')
k100_gpus = GPUDevice.objects.filter(model_name__icontains='K100')
print(f'K100相关GPU数量: {k100_gpus.count()}')
for gpu in k100_gpus:
    print(f'K100 GPU: {gpu.model_name}, 厂商: {gpu.vendor}')

k100_perf = PerformanceTestData.objects.filter(machine_model__icontains='K100')
print(f'K100相关性能数据数量: {k100_perf.count()}')
for data in k100_perf:
    print(f'K100性能: 机器模型={data.machine_model}, 输出吞吐量={data.output_token_throughput}')

k100_stats = AverageStats.objects.filter(model_name__icontains='K100')
print(f'K100相关平均统计数量: {k100_stats.count()}')
for stat in k100_stats:
    print(f'K100统计: 模型={stat.model_name}, tokens_per_second={stat.avg_output_token_throughput}')