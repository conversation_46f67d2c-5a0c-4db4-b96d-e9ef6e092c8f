import requests
import json

# 测试用户提到的具体API
url = 'http://localhost:8000/api/model-storage/gpu-rankings/'
params = {
    'token': '1',
    'ranking_type': 'output_token_throughput',
    'scope': 'all',
    'group_by': 'overall'
}

print("=== 测试用户提到的具体API ===")
print(f"URL: {url}")
print(f"参数: {params}")

try:
    response = requests.get(url, params=params)
    print(f"\n状态码: {response.status_code}")
    
    if response.status_code == 200:
        data = response.json()
        print(f"\n排行类型: {data.get('data', {}).get('ranking_type')}")
        print(f"范围: {data.get('data', {}).get('scope')}")
        print(f"分组方式: {data.get('data', {}).get('group_by')}")
        print(f"总GPU数: {data.get('data', {}).get('total_gpus')}")
        
        rankings = data.get('data', {}).get('rankings', [])
        print(f"\n找到 {len(rankings)} 个GPU:")
        
        # 查找K100-AI
        k100_ai_found = False
        for gpu in rankings:
            if 'K100-AI' in gpu.get('gpu_model', ''):
                k100_ai_found = True
                print(f"\n✅ 找到K100-AI相关GPU:")
                print(f"  GPU型号: {gpu.get('gpu_model')}")
                print(f"  厂商: {gpu.get('vendor')}")
                print(f"  最佳吞吐量: {gpu.get('best_output_token_throughput')} tokens/s")
                print(f"  最佳TTFT: {gpu.get('best_ttft_ms')} ms")
                print(f"  排名: {gpu.get('rank')}")
                print(f"  测试模型: {gpu.get('tested_models')}")
                print(f"  最佳吞吐量模型: {gpu.get('best_throughput_model')}")
                print(f"  最佳TTFT模型: {gpu.get('best_ttft_model')}")
        
        if not k100_ai_found:
            print("\n❌ 未找到K100-AI相关GPU")
            print("\n所有GPU列表:")
            for i, gpu in enumerate(rankings, 1):
                print(f"  {i}. {gpu.get('gpu_model')} ({gpu.get('vendor')}) - 吞吐量: {gpu.get('best_output_token_throughput')}")
        
    else:
        print(f"\n错误响应: {response.text}")
        
except Exception as e:
    print(f"请求失败: {e}")