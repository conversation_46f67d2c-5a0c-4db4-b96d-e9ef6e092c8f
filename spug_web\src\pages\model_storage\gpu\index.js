import React, { useState, useEffect } from 'react';
import { observer } from 'mobx-react';
import { Card, Button, Input, Row, Col, Modal, Form, message, Spin, Empty, Tag, Popover, PageHeader, Breadcrumb, Statistic, Space, Pagination, Select, Tabs, Collapse, Divider } from 'antd';
import { PlusOutlined, EditOutlined, DeleteOutlined, SearchOutlined, HomeOutlined, DatabaseOutlined, ThunderboltOutlined, ReloadOutlined, AppstoreOutlined, UnorderedListOutlined, GroupOutlined } from '@ant-design/icons';
import http from 'libs/http';
import store from './store';
import Detail from './Detail';
import GpuForm from './Form';
import RefreshButton from './RefreshButton';
import styles from './index.module.less';

const { Option } = Select;
const { TabPane } = Tabs;
const { Panel } = Collapse;

export default observer(function GpuIndex() {
  const [loading, setLoading] = useState(true);
  const [search, setSearch] = useState({});
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 12,
    total: 0
  });
  const [viewMode, setViewMode] = useState('card'); // 'card' | 'group'
  const [groupBy, setGroupBy] = useState('vendor'); // 'vendor' | 'model_type'

  useEffect(() => {
    // 首次加载时明确传递分页参数
    fetchGpus({
      page: pagination.current,
      page_size: pagination.pageSize
    });
  }, []);

  // 设置页面背景
  useEffect(() => {
    // 保存原始背景
    const originalBackground = document.body.style.background;
    const originalMinHeight = document.body.style.minHeight;

    // 设置背景
    document.body.style.background = 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)';
    document.body.style.minHeight = '100vh';

    // 清理函数：组件卸载时恢复原始背景
    return () => {
      document.body.style.background = originalBackground;
      document.body.style.minHeight = originalMinHeight;
    };
  }, []);

  const fetchGpus = (params = {}) => {
    setLoading(true);

    // 使用传入的参数优先，如果没有传入则使用当前分页状态
    const currentPage = params.page || pagination.current;
    const currentPageSize = params.page_size || pagination.pageSize;

    const requestParams = {
      ...search,
      ...params,
      page: currentPage,
      page_size: currentPageSize
    };

    http.get('/api/model-storage/gpus/', { params: requestParams })
      .then(res => {
        if (res.results) {
          // 分页数据格式
          store.gpus = res.results;
          setPagination(prev => ({
            ...prev,
            total: res.count,
            current: res.current_page || currentPage,
            pageSize: currentPageSize
          }));
        } else {
          // 非分页数据格式（兼容旧接口）
          store.gpus = res;
          setPagination(prev => ({
            ...prev,
            total: res.length,
            current: currentPage,
            pageSize: currentPageSize
          }));
        }
      })
      .catch(error => {
        console.error('fetchGpus error:', error);
        message.error('获取GPU数据失败');
      })
      .finally(() => setLoading(false));
  };

  const handleSearch = () => {
    setPagination(prev => ({ ...prev, current: 1 }));
    fetchGpus({
      ...search,
      page: 1,
      page_size: pagination.pageSize
    });
  };

  const handleReset = () => {
    setSearch({});
    setPagination(prev => ({ ...prev, current: 1 }));
    fetchGpus({
      page: 1,
      page_size: pagination.pageSize
    });
  };

  const handleRefresh = () => {
    fetchGpus({
      page: pagination.current,
      page_size: pagination.pageSize
    });
  };

  const handlePageChange = (page, pageSize) => {

    // 先更新分页状态
    setPagination(prev => ({
      ...prev,
      current: page,
      pageSize: pageSize || prev.pageSize
    }));

    // 然后调用API，明确传递分页参数
    fetchGpus({
      page,
      page_size: pageSize || pagination.pageSize
    });
  };

  const handleViewModeChange = (mode) => {
    setViewMode(mode);
  };

  const handleGroupByChange = (value) => {
    setGroupBy(value);
  };

  const handleDelete = (gpu) => {
    Modal.confirm({
      title: `确定删除GPU: ${gpu.name} ?`,
      content: '删除后无法恢复，请谨慎操作。',
      onOk: () => {
        return http.delete(`/api/model-storage/gpus/${gpu.id}/`)
          .then(() => {
            message.success('删除成功');
            fetchGpus();
          });
      }
    });
  };

  const openForm = (gpu = {}) => {
    store.record = gpu;
    store.formVisible = true;
  };

  const openDetail = (gpu) => {
    store.record = gpu;
    store.detailVisible = true;
  };

  // 计算统计数据
  const getStats = () => {
    const totalGpus = pagination.total || store.gpus.length;
    const vendors = [...new Set(store.gpus.map(gpu => gpu.vendor))].length;
    const totalModels = store.gpus.reduce((sum, gpu) => sum + (gpu.tested_models_count || 0), 0);
    return { totalGpus, vendors, totalModels };
  };

  // 按厂商分组GPU数据
  const getGroupedGpus = () => {
    if (groupBy === 'vendor') {
      const grouped = {};
      store.gpus.forEach(gpu => {
        const vendor = gpu.vendor || '未知厂商';
        if (!grouped[vendor]) {
          grouped[vendor] = [];
        }
        grouped[vendor].push(gpu);
      });
      return grouped;
    }
    return {};
  };

  // 渲染GPU卡片
  const renderGpuCard = (gpu) => (
    <Col key={gpu.id} xs={24} sm={12} md={8} lg={6}>
      <Card
        className={styles.gpuCard}
        hoverable
        onClick={() => openDetail(gpu)}
        actions={[
          <EditOutlined
            key="edit"
            onClick={(e) => { e.stopPropagation(); openForm(gpu); }}
            style={{ color: '#1890ff' }}
          />,
          <DeleteOutlined
            key="delete"
            onClick={(e) => { e.stopPropagation(); handleDelete(gpu); }}
            style={{ color: '#ff4d4f' }}
          />,
        ]}
      >
        <div className={styles.gpuCardHeader}>
          <ThunderboltOutlined className={styles.gpuIcon} />
          <div className={styles.gpuName}>{gpu.name}</div>
        </div>

        <div className={styles.gpuInfo}>
          <div className={styles.infoItem}>
            <span className={styles.infoLabel}>厂商</span>
            <Tag className={styles.vendorTag}>{gpu.vendor}</Tag>
          </div>

          <div className={styles.infoItem}>
            <span className={styles.infoLabel}>测试模型</span>
            <Tag className={styles.countTag}>{gpu.tested_models_count || 0}</Tag>
          </div>

          {gpu.tested_models && gpu.tested_models.length > 0 && (
            <div className={styles.infoItem}>
              <Popover
                content={
                  <div style={{ maxWidth: 300 }}>
                    <div className={styles.modelList}>
                      {gpu.tested_models.map((model, index) => (
                        <Tag key={index} className={styles.modelTag}>
                          {model}
                        </Tag>
                      ))}
                    </div>
                  </div>
                }
                title="测试过的模型"
                placement="topLeft"
              >
                <div className={styles.modelPreview}>
                  <span className={styles.infoLabel}>模型列表</span>
                  <div className={styles.modelText}>
                    {gpu.tested_models.slice(0, 2).join(', ')}
                    {gpu.tested_models.length > 2 && '...'}
                  </div>
                </div>
              </Popover>
            </div>
          )}

          {gpu.description && (
            <div className={styles.description}>
              {gpu.description}
            </div>
          )}
        </div>
      </Card>
    </Col>
  );

  const stats = getStats();
  const groupedGpus = getGroupedGpus();

  return (
    <div className={styles.container}>
      {/* 页面头部 */}
      <div className={styles.header}>
        <Breadcrumb style={{ marginBottom: 16 }}>
          <Breadcrumb.Item>
            <HomeOutlined />
            <span>首页</span>
          </Breadcrumb.Item>
          <Breadcrumb.Item>
            <DatabaseOutlined />
            <span>模型存储</span>
          </Breadcrumb.Item>
          <Breadcrumb.Item>
            <ThunderboltOutlined />
            <span>GPU管理</span>
          </Breadcrumb.Item>
        </Breadcrumb>

        {/* 页面标题 */}
        <div className={styles.pageHeader}>
          <h1 className={styles.headerTitle}>
            <ThunderboltOutlined className={styles.headerIcon} />
            GPU设备管理
          </h1>
          <p className={styles.headerSubtitle}>
            智能化GPU设备管理平台，支持设备监控、模型关联和性能分析
          </p>
        </div>
      </div>

      {/* 统计卡片 */}
      <Row gutter={[24, 24]} style={{ marginBottom: 24 }}>
        <Col xs={24} sm={8}>
          <Card className={styles.statsCard}>
            <Statistic
              title="GPU设备总数"
              value={stats.totalGpus}
              prefix={<ThunderboltOutlined style={{ color: '#1890ff' }} />}
              valueStyle={{ color: '#1890ff', fontSize: '28px', fontWeight: 'bold' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={8}>
          <Card className={styles.statsCard}>
            <Statistic
              title="厂商数量"
              value={stats.vendors}
              prefix={<DatabaseOutlined style={{ color: '#52c41a' }} />}
              valueStyle={{ color: '#52c41a', fontSize: '28px', fontWeight: 'bold' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={8}>
          <Card className={styles.statsCard}>
            <Statistic
              title="测试模型总数"
              value={stats.totalModels}
              prefix={<SearchOutlined style={{ color: '#fa8c16' }} />}
              valueStyle={{ color: '#fa8c16', fontSize: '28px', fontWeight: 'bold' }}
            />
          </Card>
        </Col>
      </Row>

      {/* 搜索和操作区域 */}
      <Card title="智能搜索" className={styles.searchCard}>
        <Row gutter={[16, 16]}>
          {/* 搜索区域 */}
          <Col xs={24} lg={14}>
            <Space size="middle" wrap>
              <Input
                style={{ width: 180 }}
                placeholder="搜索GPU名称"
                value={search.name}
                onChange={e => setSearch({ ...search, name: e.target.value })}
                allowClear
                className={styles.searchInput}
                size="large"
              />
              <Input
                style={{ width: 180 }}
                placeholder="搜索厂商"
                value={search.vendor}
                onChange={e => setSearch({ ...search, vendor: e.target.value })}
                allowClear
                className={styles.searchInput}
                size="large"
              />
              <Input
                style={{ width: 180 }}
                placeholder="搜索测试模型"
                value={search.model_name}
                onChange={e => setSearch({ ...search, model_name: e.target.value })}
                allowClear
                className={styles.searchInput}
                size="large"
              />
              <Button
                type="primary"
                icon={<SearchOutlined />}
                onClick={handleSearch}
                className={styles.primaryButton}
                size="large"
              >
                搜索
              </Button>
              <Button
                onClick={handleReset}
                className={styles.secondaryButton}
                size="large"
              >
                重置
              </Button>
            </Space>
          </Col>

          {/* 视图控制区域 */}
          <Col xs={24} lg={10}>
            <Row justify="end" align="middle" gutter={[8, 8]}>
              <Col>
                <Space size="middle">
                  <span style={{ color: '#666', fontSize: '14px', fontWeight: 500 }}>视图模式:</span>
                  <Button.Group>
                    <Button
                      type={viewMode === 'card' ? 'primary' : 'default'}
                      icon={<AppstoreOutlined />}
                      onClick={() => handleViewModeChange('card')}
                      className={viewMode === 'card' ? styles.primaryButton : styles.secondaryButton}
                    >
                      卡片
                    </Button>
                    <Button
                      type={viewMode === 'group' ? 'primary' : 'default'}
                      icon={<GroupOutlined />}
                      onClick={() => handleViewModeChange('group')}
                      className={viewMode === 'group' ? styles.primaryButton : styles.secondaryButton}
                    >
                      分组
                    </Button>
                  </Button.Group>
                </Space>
              </Col>

              {viewMode === 'group' && (
                <Col>
                  <Space size="middle">
                    <span style={{ color: '#666', fontSize: '14px', fontWeight: 500 }}>分组方式:</span>
                    <Select
                      value={groupBy}
                      onChange={handleGroupByChange}
                      style={{ width: 120 }}
                      className={styles.searchSelect}
                      size="large"
                    >
                      <Option value="vendor">厂商</Option>
                    </Select>
                  </Space>
                </Col>
              )}

              <Col>
                <Space size="middle">
                  <RefreshButton
                    onRefresh={handleRefresh}
                    size="large"
                  />
                  <Button
                    type="primary"
                    icon={<PlusOutlined />}
                    onClick={() => openForm()}
                    className={styles.primaryButton}
                    size="large"
                  >
                    新建GPU
                  </Button>
                </Space>
              </Col>
            </Row>
          </Col>
        </Row>
      </Card>

      {/* GPU设备列表 */}
      {loading ? (
        <div style={{ textAlign: 'center', padding: '60px 0' }}>
          <Spin size="large" tip="加载中..." />
        </div>
      ) : store.gpus.length > 0 ? (
        <>
          {viewMode === 'card' ? (
            /* 卡片视图 */
            <Row gutter={[24, 24]}>
              {store.gpus.map(gpu => renderGpuCard(gpu))}
            </Row>
          ) : (
            /* 分组视图 */
            <div className={styles.groupView}>
              {Object.keys(groupedGpus).length > 0 ? (
                <Collapse
                  defaultActiveKey={Object.keys(groupedGpus)}
                  className={styles.groupCollapse}
                >
                  {Object.entries(groupedGpus).map(([vendor, gpus]) => (
                    <Panel
                      key={vendor}
                      header={
                        <div className={styles.groupHeader}>
                          <div className={styles.groupTitle}>
                            <DatabaseOutlined style={{ marginRight: 8, color: '#667eea' }} />
                            {vendor}
                          </div>
                          <Tag className={styles.groupCount}>{gpus.length} 台设备</Tag>
                        </div>
                      }
                      className={styles.groupPanel}
                    >
                      <Row gutter={[16, 16]}>
                        {gpus.map(gpu => renderGpuCard(gpu))}
                      </Row>
                    </Panel>
                  ))}
                </Collapse>
              ) : (
                <div className={styles.emptyGroup}>
                  <Empty description="当前筛选条件下暂无数据" />
                </div>
              )}
            </div>
          )}

          {/* 分页组件 */}
          <div className={styles.paginationContainer}>
            <Pagination
              current={pagination.current}
              pageSize={pagination.pageSize}
              total={pagination.total}
              onChange={handlePageChange}
              onShowSizeChange={handlePageChange}
              showSizeChanger
              showQuickJumper
              showTotal={(total, range) =>
                `第 ${range[0]}-${range[1]} 条，共 ${total} 条`
              }
              pageSizeOptions={['12', '24', '48', '96']}
              className={styles.pagination}
            />
          </div>
        </>
      ) : (
        /* 空状态 */
        <div className={styles.emptyContainer}>
          <Empty
            image={Empty.PRESENTED_IMAGE_SIMPLE}
            description={
              <div>
                <p>暂无GPU设备</p>
                <Button
                  type="primary"
                  icon={<PlusOutlined />}
                  onClick={() => openForm()}
                  className={styles.gradientButton}
                >
                  立即添加
                </Button>
              </div>
            }
          />
        </div>
      )}

      {store.formVisible && <GpuForm store={store} fetchGpus={fetchGpus} />}
      {store.detailVisible && <Detail store={store} />}
    </div>
  );
}); 