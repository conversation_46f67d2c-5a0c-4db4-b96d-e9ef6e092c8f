import requests
import json

# 测试GPU排行榜API
url = 'http://localhost:8000/api/model-storage/gpu-rankings/'
params = {
    'token': '1',
    'ranking_type': 'output_token_throughput',
    'scope': 'all',
    'group_by': 'overall'
}

print("=== 调用GPU排行榜API ===")
print(f"URL: {url}")
print(f"参数: {params}")

try:
    response = requests.get(url, params=params)
    print(f"\n状态码: {response.status_code}")
    print(f"响应头: {dict(response.headers)}")
    
    if response.status_code == 200:
        try:
            data = response.json()
            print(f"\n完整响应数据:")
            print(json.dumps(data, indent=2, ensure_ascii=False))
        except json.JSONDecodeError:
            print(f"\n响应内容 (非JSON): {response.text}")
    else:
        print(f"\n错误响应: {response.text}")
        
except Exception as e:
    print(f"请求失败: {e}")

# 同时测试throughput参数
print("\n" + "="*50)
params2 = {
    'token': '1',
    'ranking_type': 'throughput',
    'scope': 'all',
    'group_by': 'overall'
}

print("=== 测试throughput参数 ===")
print(f"参数: {params2}")

try:
    response2 = requests.get(url, params=params2)
    print(f"\n状态码: {response2.status_code}")
    
    if response2.status_code == 200:
        try:
            data2 = response2.json()
            print(f"\n完整响应数据:")
            print(json.dumps(data2, indent=2, ensure_ascii=False))
        except json.JSONDecodeError:
            print(f"\n响应内容 (非JSON): {response2.text}")
    else:
        print(f"\n错误响应: {response2.text}")
        
except Exception as e:
    print(f"请求失败: {e}")