#!/usr/bin/env python
import os
import sys
import django

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 设置Django环境
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'spug.settings')
django.setup()

from apps.model_storage.models import GPUDevice

print("=== 更新K100-AI GPU性能数据 ===")

# 查找K100-AI GPU
k100_gpu = GPUDevice.objects.filter(name='K100-AI').first()
if k100_gpu:
    print(f"找到GPU: {k100_gpu.name}, 当前吞吐量: {k100_gpu.tokens_per_second}, 当前TTFT: {k100_gpu.ttft_ms}")
    
    # 更新性能数据（示例数据）
    k100_gpu.tokens_per_second = 150.5  # 设置一个较高的吞吐量
    k100_gpu.ttft_ms = 45.2  # 设置一个较好的首字符时间
    k100_gpu.save()
    
    print(f"已更新GPU: {k100_gpu.name}, 新吞吐量: {k100_gpu.tokens_per_second}, 新TTFT: {k100_gpu.ttft_ms}")
else:
    print("未找到K100-AI GPU")

# 查找K100-AI-128cu GPU
k100_128cu_gpu = GPUDevice.objects.filter(name='K100-AI-128cu').first()
if k100_128cu_gpu:
    print(f"找到GPU: {k100_128cu_gpu.name}, 当前吞吐量: {k100_128cu_gpu.tokens_per_second}, 当前TTFT: {k100_128cu_gpu.ttft_ms}")
    
    # 更新性能数据（示例数据，比K100-AI稍高一些）
    k100_128cu_gpu.tokens_per_second = 180.8  # 设置更高的吞吐量
    k100_128cu_gpu.ttft_ms = 38.5  # 设置更好的首字符时间
    k100_128cu_gpu.save()
    
    print(f"已更新GPU: {k100_128cu_gpu.name}, 新吞吐量: {k100_128cu_gpu.tokens_per_second}, 新TTFT: {k100_128cu_gpu.ttft_ms}")
else:
    print("未找到K100-AI-128cu GPU")

print("\n=== 性能数据更新完成 ===")

# 验证更新结果
print("\n=== 验证更新结果 ===")
k100_gpus = GPUDevice.objects.filter(name__icontains='K100')
for gpu in k100_gpus:
    print(f"GPU: {gpu.name}, 厂商: {gpu.vendor}, 吞吐量: {gpu.tokens_per_second}, TTFT: {gpu.ttft_ms}")