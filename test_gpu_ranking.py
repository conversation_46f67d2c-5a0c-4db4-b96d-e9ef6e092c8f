import requests
import json

# 测试GPU排行榜API
url = 'http://localhost:8000/api/model-storage/gpu-rankings/'
headers = {'Authorization': 'Bearer 1'}

# 测试不同的参数组合
test_cases = [
    {'ranking_type': 'throughput', 'scope': 'all', 'group_by': 'overall'},
    {'ranking_type': 'ttft', 'scope': 'all', 'group_by': 'overall'},
    {'ranking_type': 'throughput', 'scope': 'domestic', 'group_by': 'overall'},
    {'ranking_type': 'throughput', 'scope': 'all', 'group_by': 'vendor'}
]

for i, params in enumerate(test_cases, 1):
    print(f"\n=== 测试用例 {i}: {params} ===")
    try:
        response = requests.get(url, headers=headers, params=params)
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"排行类型: {data.get('ranking_type')}")
            print(f"范围: {data.get('scope')}")
            print(f"分组方式: {data.get('group_by')}")
            print(f"总GPU数: {data.get('total_gpus', 0)}")
            print(f"描述: {data.get('ranking_description')}")
            
            if 'rankings' in data:
                print("\n前3名GPU:")
                for gpu in data['rankings'][:3]:
                    print(f"  {gpu.get('rank', '?')}. {gpu.get('gpu_model')} ({gpu.get('vendor')})")
                    print(f"     吞吐量: {gpu.get('best_output_token_throughput')} tokens/s")
                    print(f"     TTFT: {gpu.get('best_ttft_ms')} ms")
                    print(f"     测试模型数: {gpu.get('model_count', 0)}")
                    print(f"     测试数据条数: {gpu.get('test_count', 0)}")
                    print(f"     最佳吞吐量模型: {gpu.get('best_throughput_model', '未知')}")
                    print(f"     最佳TTFT模型: {gpu.get('best_ttft_model', '未知')}")
                    print()
            
            if 'vendor_groups' in data:
                print(f"\n厂商分组数: {len(data['vendor_groups'])}")
                for vendor_group in data['vendor_groups'][:2]:  # 只显示前2个厂商
                    print(f"  厂商: {vendor_group.get('vendor')}")
                    print(f"  GPU数量: {len(vendor_group.get('gpus', []))}")
        else:
            print(f"错误: {response.text}")
            
    except Exception as e:
        print(f"请求失败: {e}")

print("\n测试完成!")