.container {
  padding: 24px;
  background: #f5f5f5;
  min-height: 100vh;
}

.tableWrapper {
  overflow: auto;
  max-height: 70vh;
  border: 1px solid #d9d9d9;
  border-radius: 6px;
  background: white;
}

.table {
  width: 100%;
  border-collapse: collapse;
  font-size: 12px;
  
  th, td {
    border: 1px solid #f0f0f0;
    padding: 8px 12px;
    text-align: left;
    vertical-align: middle;
  }
}

.tableHeader {
  background: #fafafa;
  font-weight: 600;
  position: sticky;
  top: 0;
  z-index: 10;
  
  .headerContent {
    display: flex;
    align-items: center;
    justify-content: space-between;
    
    &.sortable {
      cursor: pointer;
      user-select: none;
      
      &:hover {
        background: #f0f0f0;
        border-radius: 4px;
        padding: 2px 4px;
        margin: -2px -4px;
      }
    }
  }
  
  .sortIcon {
    margin-left: 4px;
    color: #1890ff;
    font-weight: bold;
  }
}

.tableRow {
  &:hover {
    background: #f5f5f5;
  }
  
  &.modifiedRow {
    background: #fff7e6;
    border-left: 3px solid #faad14;
    
    &:hover {
      background: #fff1b8;
    }
  }
}

.tableCell {
  position: relative;
  
  .editableCell {
    min-height: 20px;
    cursor: pointer;
    padding: 4px;
    border-radius: 2px;
    transition: all 0.2s;
    
    &:hover {
      background: #e6f7ff;
      border: 1px dashed #1890ff;
    }
  }
}

// 响应式设计
@media (max-width: 1200px) {
  .tableWrapper {
    max-height: 60vh;
  }
  
  .table {
    font-size: 11px;
    
    th, td {
      padding: 6px 8px;
    }
  }
}

@media (max-width: 768px) {
  .container {
    padding: 16px;
  }
  
  .tableWrapper {
    max-height: 50vh;
  }
  
  .table {
    font-size: 10px;
    
    th, td {
      padding: 4px 6px;
    }
  }
}

// 高亮列样式
.highlightedColumn {
  th {
    background: #e6f7ff !important;
    font-weight: bold;
    color: #1890ff;
  }
  
  td {
    background: #f6ffed;
    font-weight: bold;
  }
}

// 统计卡片样式
.statsCard {
  .ant-statistic-title {
    font-size: 14px;
    font-weight: 500;
  }
  
  .ant-statistic-content {
    font-size: 20px;
  }
}
