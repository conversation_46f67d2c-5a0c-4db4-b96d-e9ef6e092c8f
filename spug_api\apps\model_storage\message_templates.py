# -*- coding: utf-8 -*-
"""
企业微信消息模板管理
定义各种场景下的消息模板
"""

from libs.utils import human_datetime


class MessageTemplates:
    """消息模板管理器"""
    
    @staticmethod
    def task_assignment_template(task_data, assignee_name, assigned_by, message_content=""):
        """任务分配消息模板"""
        
        # 状态和优先级映射
        status_map = {
            'pending': '⏳ 待开始',
            'in_progress': '🔄 进行中', 
            'completed': '✅ 已完成',
            'cancelled': '❌ 已取消',
            'blocked': '🚫 阻塞中'
        }
        
        priority_map = {
            'p1': '🔴 P1-高',
            'p2': '🟡 P2-中',
            'p3': '🟢 P3-低'
        }
        
        # 计算任务紧急程度
        urgency_icon = "🔥" if task_data.get('priority') == 'p1' else "📋"
        
        texts = [
            f"## {urgency_icon} 模型测试任务分配通知",
            "",
            f"**👋 Hi {assignee_name}，您有新的任务分配！**",
            "",
            "### 📋 任务概览",
            f"**🤖 模型名称：** `{task_data.get('model_name', 'N/A')}`",
            f"**🏷️ 模型类型：** {task_data.get('model_type', 'N/A')}",
            f"**💻 GPU型号：** {task_data.get('gpu_model', 'N/A')}",
            f"**⚡ 优先级：** {priority_map.get(task_data.get('priority', 'p2'), '🟡 P2-中')}",
            f"**📊 当前状态：** {status_map.get(task_data.get('test_status', 'pending'), '⏳ 待开始')}",
            f"**📈 当前进度：** {task_data.get('progress', 0)}%",
            "",
            "### 📅 时间安排",
            f"**🚀 开始时间：** {task_data.get('start_date', 'N/A')}",
            f"**🏁 结束时间：** {task_data.get('end_date', 'N/A')}",
            "",
            "### 👨‍💼 分配信息",
            f"**分配人：** {assigned_by}",
            f"**分配时间：** {human_datetime()}",
        ]
        
        # 添加分配说明
        if message_content:
            texts.extend([
                "",
                "### 💬 分配说明",
                f"```",
                f"{message_content}",
                f"```"
            ])
        
        # 添加操作指引
        texts.extend([
            "",
            "### 🎯 下一步操作",
            "• 🔗 登录 **Spug运维平台** 查看详细信息",
            "• 📞 如有疑问请及时与分配人沟通",
            "• ⏰ 请按时完成任务并及时更新进度",
            "• 📊 完成后请在系统中标记任务状态",
            "",
            "---",
            "> 📱 来自 **Spug模型测试管理平台** | 🕐 " + human_datetime()
        ])
        
        return '\n'.join(texts)
    
    @staticmethod
    def task_progress_template(task_data, progress_info):
        """任务进度更新消息模板"""
        
        old_progress = progress_info.get('old_progress', 0)
        new_progress = progress_info.get('new_progress', 0)
        progress_diff = new_progress - old_progress
        
        # 进度图标
        if new_progress >= 100:
            progress_icon = "🎉"
            progress_status = "任务完成"
        elif new_progress >= 80:
            progress_icon = "🚀"
            progress_status = "即将完成"
        elif new_progress >= 50:
            progress_icon = "📈"
            progress_status = "进展顺利"
        elif new_progress >= 20:
            progress_icon = "🔄"
            progress_status = "正在进行"
        else:
            progress_icon = "⏳"
            progress_status = "刚刚开始"
        
        texts = [
            f"## {progress_icon} 任务进度更新通知",
            "",
            f"**📊 {progress_status}**",
            "",
            "### 📋 任务信息",
            f"**🤖 模型名称：** `{task_data.get('model_name', 'N/A')}`",
            f"**👤 负责人：** {task_data.get('tester', 'N/A')}",
            "",
            "### 📈 进度详情",
            f"**当前进度：** {new_progress}%",
            f"**进度变化：** +{progress_diff}%" if progress_diff > 0 else f"**进度变化：** {progress_diff}%",
            f"**更新时间：** {human_datetime()}",
        ]
        
        # 添加进度条可视化
        progress_bar = MessageTemplates._generate_progress_bar(new_progress)
        texts.extend([
            "",
            "### 📊 进度可视化",
            f"`{progress_bar}`"
        ])
        
        # 添加进度说明
        if progress_info.get('description'):
            texts.extend([
                "",
                "### 💬 进度说明",
                f"{progress_info.get('description')}"
            ])
        
        texts.extend([
            "",
            "---",
            "> 📱 来自 **Spug模型测试管理平台** | 🕐 " + human_datetime()
        ])
        
        return '\n'.join(texts)
    
    @staticmethod
    def task_completion_template(task_data, completion_info):
        """任务完成消息模板"""
        
        texts = [
            "## 🎉 任务完成通知",
            "",
            f"**恭喜！任务已成功完成！**",
            "",
            "### 📋 任务信息",
            f"**🤖 模型名称：** `{task_data.get('model_name', 'N/A')}`",
            f"**👤 完成人：** {task_data.get('tester', 'N/A')}",
            f"**📅 完成时间：** {human_datetime()}",
            f"**⏱️ 计划时间：** {task_data.get('start_date', 'N/A')} ~ {task_data.get('end_date', 'N/A')}",
            "",
            "### 📊 完成统计",
            f"**最终进度：** 100%",
            f"**任务状态：** ✅ 已完成",
        ]
        
        # 添加完成总结
        if completion_info.get('summary'):
            texts.extend([
                "",
                "### 📝 完成总结",
                f"{completion_info.get('summary')}"
            ])
        
        texts.extend([
            "",
            "### 🎯 后续操作",
            "• 📊 请在系统中确认任务完成状态",
            "• 📄 如需要，请提交相关测试报告",
            "• 🔄 准备接收下一个任务分配",
            "",
            "---",
            "> 📱 来自 **Spug模型测试管理平台** | 🕐 " + human_datetime()
        ])
        
        return '\n'.join(texts)
    
    @staticmethod
    def task_overdue_template(task_data, overdue_info):
        """任务逾期提醒消息模板"""
        
        overdue_days = overdue_info.get('overdue_days', 0)
        
        texts = [
            "## ⚠️ 任务逾期提醒",
            "",
            f"**注意：您有任务已逾期 {overdue_days} 天！**",
            "",
            "### 📋 任务信息",
            f"**🤖 模型名称：** `{task_data.get('model_name', 'N/A')}`",
            f"**👤 负责人：** {task_data.get('tester', 'N/A')}",
            f"**📅 计划结束：** {task_data.get('end_date', 'N/A')}",
            f"**📈 当前进度：** {task_data.get('progress', 0)}%",
            f"**⏰ 逾期天数：** {overdue_days} 天",
            "",
            "### 🚨 紧急操作",
            "• 🔥 请立即处理此任务",
            "• 📞 如有困难请及时沟通",
            "• 📊 请更新任务进度和状态",
            "• ⏰ 如需延期请申请调整计划",
            "",
            "---",
            "> 📱 来自 **Spug模型测试管理平台** | 🕐 " + human_datetime()
        ]
        
        return '\n'.join(texts)
    
    @staticmethod
    def _generate_progress_bar(progress, length=20):
        """生成进度条"""
        filled = int(progress / 100 * length)
        bar = '█' * filled + '░' * (length - filled)
        return f"{bar} {progress}%"


# 便捷函数
def get_task_assignment_message(task_data, assignee_name, assigned_by, message_content=""):
    """获取任务分配消息"""
    return MessageTemplates.task_assignment_template(task_data, assignee_name, assigned_by, message_content)


def get_task_progress_message(task_data, progress_info):
    """获取任务进度消息"""
    return MessageTemplates.task_progress_template(task_data, progress_info)


def get_task_completion_message(task_data, completion_info):
    """获取任务完成消息"""
    return MessageTemplates.task_completion_template(task_data, completion_info)


def get_task_overdue_message(task_data, overdue_info):
    """获取任务逾期消息"""
    return MessageTemplates.task_overdue_template(task_data, overdue_info)
