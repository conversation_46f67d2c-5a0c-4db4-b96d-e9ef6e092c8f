#!/usr/bin/env python
import os
import sys
import django

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 设置Django环境
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'spug.settings')
django.setup()

from apps.model_storage.models import AverageStats, PerformanceTestData, GPUDevice

print("=== 查找K100-AI相关数据 ===")

# 查找GPUDevice表中的K100-AI
k100_gpus = GPUDevice.objects.filter(name__icontains='K100')
print(f"GPUDevice表中K100相关GPU数量: {k100_gpus.count()}")
for gpu in k100_gpus:
    print(f"GPU: {gpu.name}, 厂商: {gpu.vendor}, 吞吐量: {gpu.tokens_per_second}, TTFT: {gpu.ttft_ms}")

# 查找PerformanceTestData表中machine_model字段包含K100的数据
k100_performance = PerformanceTestData.objects.filter(machine_model__icontains='K100')
print(f"\nPerformanceTestData表中K100相关数据数量: {k100_performance.count()}")
for perf in k100_performance:
    print(f"ID: {perf.id}, 模型: {perf.model_name}, 机器: {perf.machine_model}, 吞吐量: {perf.output_token_throughput}")

# 查找所有非空machine_model字段的数据
non_empty_machine = PerformanceTestData.objects.exclude(machine_model__isnull=True).exclude(machine_model='')
print(f"\n所有非空machine_model字段的数据数量: {non_empty_machine.count()}")
for perf in non_empty_machine:
    print(f"ID: {perf.id}, 模型: {perf.model_name}, 机器: {perf.machine_model}, 吞吐量: {perf.output_token_throughput}")

# 查找最高吞吐量的数据
highest_throughput = PerformanceTestData.objects.filter(output_token_throughput__gt=0).order_by('-output_token_throughput').first()
if highest_throughput:
    print(f"\n最高吞吐量数据:")
    print(f"ID: {highest_throughput.id}, 模型: {highest_throughput.model_name}, 机器: {highest_throughput.machine_model}, 吞吐量: {highest_throughput.output_token_throughput}")

# 查找所有吞吐量大于100的数据
high_throughput = PerformanceTestData.objects.filter(output_token_throughput__gt=100)
print(f"\n所有吞吐量>100的数据数量: {high_throughput.count()}")
for perf in high_throughput:
    print(f"ID: {perf.id}, 模型: {perf.model_name}, 机器: {perf.machine_model}, 吞吐量: {perf.output_token_throughput}")