#!/usr/bin/env python
import os
import sys
import django

# 设置Django环境
sys.path.append('spug_api')
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'spug.settings')
django.setup()

from apps.model_storage.models import PerformanceTestData, GPUDevice, TestTask

print("=== 修复PerformanceTestData的machine_model字段 ===")

# 1. 检查当前状态
print("\n1. 检查当前状态:")
total_perf_data = PerformanceTestData.objects.count()
empty_machine_model = PerformanceTestData.objects.filter(machine_model='').count()
print(f"PerformanceTestData总数: {total_perf_data}")
print(f"machine_model为空的数量: {empty_machine_model}")

# 2. 根据模型名称推断GPU型号
print("\n2. 根据模型名称推断GPU型号:")

# 创建模型名称到GPU型号的映射
model_to_gpu_mapping = {}

# 从TestTask中获取模型名称和GPU型号的映射关系
test_tasks = TestTask.objects.exclude(gpu_model__isnull=True).exclude(gpu_model='')
for task in test_tasks:
    if task.model_name and task.gpu_model:
        model_to_gpu_mapping[task.model_name] = task.gpu_model

print(f"从TestTask中获取到的映射关系:")
for model_name, gpu_model in model_to_gpu_mapping.items():
    print(f"  {model_name} -> {gpu_model}")

# 3. 更新PerformanceTestData的machine_model字段
print("\n3. 更新PerformanceTestData的machine_model字段:")
updated_count = 0

for perf_data in PerformanceTestData.objects.filter(machine_model=''):
    model_name = perf_data.model_name
    if model_name in model_to_gpu_mapping:
        gpu_model = model_to_gpu_mapping[model_name]
        perf_data.machine_model = gpu_model
        perf_data.save(update_fields=['machine_model'])
        updated_count += 1
        print(f"  更新记录ID {perf_data.id}: {model_name} -> machine_model='{gpu_model}'")

print(f"共更新了 {updated_count} 条PerformanceTestData记录")

# 4. 重新关联gpu_device_id
print("\n4. 重新关联gpu_device_id:")
gpu_device_updated = 0

for perf_data in PerformanceTestData.objects.filter(gpu_device__isnull=True):
    machine_model = (perf_data.machine_model or '').strip()
    if machine_model:
        gpu_device = GPUDevice.objects.filter(name=machine_model).first()
        if gpu_device:
            perf_data.gpu_device = gpu_device
            perf_data.save(update_fields=['gpu_device'])
            gpu_device_updated += 1
            print(f"  关联记录ID {perf_data.id}: machine_model='{machine_model}' -> gpu_device_id={gpu_device.id}")

print(f"共关联了 {gpu_device_updated} 条PerformanceTestData记录到GPUDevice")

# 5. 验证修复结果
print("\n5. 验证修复结果:")
mr_v100_gpu = GPUDevice.objects.filter(name='MR-V100').first()
if mr_v100_gpu:
    print(f"MR-V100 GPU (ID: {mr_v100_gpu.id}):")
    
    # 检查关联的PerformanceTestData
    related_perf_data = PerformanceTestData.objects.filter(gpu_device=mr_v100_gpu)
    print(f"  通过外键关联的PerformanceTestData数量: {related_perf_data.count()}")
    
    # 检查通过machine_model关联的数据
    machine_model_data = PerformanceTestData.objects.filter(machine_model='MR-V100')
    print(f"  通过machine_model关联的PerformanceTestData数量: {machine_model_data.count()}")
    
    # 重新计算性能数据
    print(f"  重新计算MR-V100的性能数据:")
    gpu_dict = mr_v100_gpu.to_dict()
    print(f"    tokens_per_second: {gpu_dict['tokens_per_second']}")
    print(f"    ttft_ms: {gpu_dict['ttft_ms']}")

# 6. 显示所有GPU的性能数据
print("\n6. 显示所有GPU的性能数据:")
for gpu in GPUDevice.objects.all():
    gpu_dict = gpu.to_dict()
    print(f"  {gpu.name} ({gpu.vendor}): tokens/s={gpu_dict['tokens_per_second']}, ttft_ms={gpu_dict['ttft_ms']}")

print("\n=== 修复完成 ===")
