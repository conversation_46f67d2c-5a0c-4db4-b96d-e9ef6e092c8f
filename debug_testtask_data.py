#!/usr/bin/env python
import os
import sys
import django

# 设置Django环境
sys.path.append('spug_api')
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'spug.settings')
django.setup()

from apps.model_storage.models import TestTask, GPUDevice, PerformanceTestData, AverageStats

print("=== 调试TestTask数据不一致问题 ===")

# 1. 检查特定的TestTask记录
print("\n1. 检查ID=105的TestTask记录:")
task_105 = TestTask.objects.filter(id=105).first()
if task_105:
    print(f"数据库中的原始数据:")
    print(f"  ID: {task_105.id}")
    print(f"  model_name: {task_105.model_name}")
    print(f"  gpu_model: {task_105.gpu_model}")
    print(f"  tokens_per_second: {task_105.tokens_per_second} (类型: {type(task_105.tokens_per_second)})")
    print(f"  ttft_ms: {task_105.ttft_ms} (类型: {type(task_105.ttft_ms)})")
    
    print(f"\nto_dict()方法返回的数据:")
    task_dict = task_105.to_dict()
    print(f"  tokens_per_second: {task_dict.get('tokens_per_second')}")
    print(f"  ttft_ms: {task_dict.get('ttft_ms')}")
else:
    print("未找到ID=105的TestTask记录")

# 2. 检查AverageStats表
print("\n2. 检查AverageStats表:")
avg_stats = AverageStats.objects.filter(model_name='DeepSeek-R1满血推理').first()
if avg_stats:
    print(f"找到AverageStats记录:")
    print(f"  model_name: {avg_stats.model_name}")
    print(f"  avg_output_token_throughput: {avg_stats.avg_output_token_throughput}")
    print(f"  avg_ttft: {avg_stats.avg_ttft}")
else:
    print("未找到DeepSeek-R1满血推理的AverageStats记录")

# 3. 检查PerformanceTestData表
print("\n3. 检查PerformanceTestData表:")
perf_data = PerformanceTestData.objects.filter(model_name__icontains='DeepSeek').first()
if perf_data:
    print(f"找到PerformanceTestData记录:")
    print(f"  model_name: {perf_data.model_name}")
    print(f"  output_token_throughput: {perf_data.output_token_throughput}")
    print(f"  avg_ttft: {perf_data.avg_ttft}")
    print(f"  machine_model: {perf_data.machine_model}")
    print(f"  gpu_device_id: {perf_data.gpu_device_id}")
else:
    print("未找到DeepSeek相关的PerformanceTestData记录")

# 4. 检查所有AverageStats数据
print("\n4. 检查所有AverageStats数据:")
all_avg_stats = AverageStats.objects.all()
print(f"AverageStats表总记录数: {all_avg_stats.count()}")
for stats in all_avg_stats[:5]:  # 只显示前5条
    print(f"  模型: {stats.model_name}, tokens/s: {stats.avg_output_token_throughput}, ttft: {stats.avg_ttft}")

# 5. 检查所有PerformanceTestData数据
print("\n5. 检查所有PerformanceTestData数据:")
all_perf_data = PerformanceTestData.objects.all()
print(f"PerformanceTestData表总记录数: {all_perf_data.count()}")
for data in all_perf_data[:5]:  # 只显示前5条
    print(f"  模型: {data.model_name}, tokens/s: {data.output_token_throughput}, ttft: {data.avg_ttft}, 机器: {data.machine_model}")

# 6. 手动更新TestTask数据进行测试
print("\n6. 手动更新TestTask数据进行测试:")
if task_105:
    print("更新TestTask ID=105的性能数据...")
    task_105.tokens_per_second = 88.0
    task_105.ttft_ms = 424.0
    task_105.save(update_fields=['tokens_per_second', 'ttft_ms'])
    print("更新完成")
    
    # 重新检查
    task_105.refresh_from_db()
    print(f"更新后的数据:")
    print(f"  tokens_per_second: {task_105.tokens_per_second}")
    print(f"  ttft_ms: {task_105.ttft_ms}")

print("\n=== 调试完成 ===")
