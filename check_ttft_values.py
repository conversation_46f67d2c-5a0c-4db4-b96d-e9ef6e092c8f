import os
import sys
import django

# 设置Django环境
sys.path.append('spug_api')
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'spug.settings')
django.setup()

from apps.model_storage.models import PerformanceTestData

print("=== TTFT值检查 ===")
print(f"总数据条数: {PerformanceTestData.objects.count()}")

print("\n=== 前10条数据的TTFT值 ===")
for data in PerformanceTestData.objects.all()[:10]:
    print(f"ID: {data.id}, model_name: {data.model_name}, ttft_ms: {data.ttft_ms}, output_token_throughput: {data.output_token_throughput}")

print("\n=== TTFT值统计 ===")
ttft_zero = PerformanceTestData.objects.filter(ttft_ms=0).count()
ttft_null = PerformanceTestData.objects.filter(ttft_ms__isnull=True).count()
ttft_positive = PerformanceTestData.objects.filter(ttft_ms__gt=0).count()

print(f"TTFT为0的数据: {ttft_zero}条")
print(f"TTFT为NULL的数据: {ttft_null}条")
print(f"TTFT大于0的数据: {ttft_positive}条")

print("\n=== 输出吞吐量统计 ===")
throughput_zero = PerformanceTestData.objects.filter(output_token_throughput=0).count()
throughput_null = PerformanceTestData.objects.filter(output_token_throughput__isnull=True).count()
throughput_positive = PerformanceTestData.objects.filter(output_token_throughput__gt=0).count()

print(f"输出吞吐量为0的数据: {throughput_zero}条")
print(f"输出吞吐量为NULL的数据: {throughput_null}条")
print(f"输出吞吐量大于0的数据: {throughput_positive}条")

print("\n=== 所有数据的详细信息 ===")
for data in PerformanceTestData.objects.all():
    print(f"ID: {data.id}, model: {data.model_name}, ttft_ms: {data.ttft_ms}, throughput: {data.output_token_throughput}")