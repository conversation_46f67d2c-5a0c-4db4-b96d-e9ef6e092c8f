#!/usr/bin/env python
import os
import sys
import django

# 设置Django环境
sys.path.append('spug_api')
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'spug.settings')
django.setup()

from apps.model_storage.models import TestTask, GPUDevice, PerformanceTestData

print("=== 调试聚合逻辑问题 ===")

# 1. 检查所有MR-V100的TestTask数据，按性能排序
print("\n1. 检查所有MR-V100的TestTask数据（按性能排序）:")
mr_v100_tasks = TestTask.objects.filter(gpu_model='MR-V100').order_by('-tokens_per_second', 'ttft_ms')

print(f"MR-V100相关的TestTask数量: {mr_v100_tasks.count()}")
print("按吞吐量降序排列:")

for task in mr_v100_tasks:
    print(f"  任务ID: {task.id} - {task.model_name}")
    print(f"    tokens_per_second: {task.tokens_per_second} (类型: {type(task.tokens_per_second)})")
    print(f"    ttft_ms: {task.ttft_ms} (类型: {type(task.ttft_ms)})")
    print(f"    更新时间: {task.updated_at}")
    print()

# 2. 手动重现聚合逻辑
print("\n2. 手动重现聚合逻辑:")

def _to_pos_float(v):
    try:
        f = float(v)
        return f if f > 0 else None
    except Exception:
        return None

# 获取所有有效的性能数据
task_qs = TestTask.objects.filter(gpu_model='MR-V100').values('id', 'model_name', 'tokens_per_second', 'ttft_ms')
print("所有TestTask的原始数据:")
for task in task_qs:
    print(f"  ID {task['id']}: {task['model_name']} - tokens/s: {task['tokens_per_second']}, ttft: {task['ttft_ms']}")

# 转换为有效的浮点数
tps_data = []
ttft_data = []

for task in task_qs:
    tps = _to_pos_float(task['tokens_per_second'])
    ttft = _to_pos_float(task['ttft_ms'])
    
    if tps is not None:
        tps_data.append((task['id'], task['model_name'], tps))
    if ttft is not None:
        ttft_data.append((task['id'], task['model_name'], ttft))

print(f"\n有效的tokens_per_second数据: {len(tps_data)}条")
for task_id, model_name, tps in tps_data:
    print(f"  ID {task_id}: {model_name} - {tps} tokens/s")

print(f"\n有效的ttft_ms数据: {len(ttft_data)}条")
for task_id, model_name, ttft in ttft_data:
    print(f"  ID {task_id}: {model_name} - {ttft} ms")

# 计算最大值和最小值
if tps_data:
    max_tps = max(tps_data, key=lambda x: x[2])
    print(f"\n最高吞吐量: {max_tps[2]} tokens/s (来自任务ID {max_tps[0]}: {max_tps[1]})")
else:
    print("\n没有有效的吞吐量数据")

if ttft_data:
    min_ttft = min(ttft_data, key=lambda x: x[2])
    print(f"最低TTFT: {min_ttft[2]} ms (来自任务ID {min_ttft[0]}: {min_ttft[1]})")
else:
    print("没有有效的TTFT数据")

# 3. 检查GPUDevice.to_dict()的实际计算过程
print("\n3. 检查GPUDevice.to_dict()的实际计算:")
mr_v100_gpu = GPUDevice.objects.filter(name='MR-V100').first()
if mr_v100_gpu:
    # 直接调用to_dict方法
    gpu_dict = mr_v100_gpu.to_dict()
    print(f"GPUDevice.to_dict()结果:")
    print(f"  tokens_per_second: {gpu_dict['tokens_per_second']}")
    print(f"  ttft_ms: {gpu_dict['ttft_ms']}")
    
    # 检查是否与我们手动计算的结果一致
    if tps_data:
        expected_max_tps = max(tps_data, key=lambda x: x[2])[2]
        if gpu_dict['tokens_per_second'] == expected_max_tps:
            print(f"  ✅ 吞吐量聚合正确: {expected_max_tps}")
        else:
            print(f"  ❌ 吞吐量聚合错误: 期望 {expected_max_tps}, 实际 {gpu_dict['tokens_per_second']}")
    
    if ttft_data:
        expected_min_ttft = min(ttft_data, key=lambda x: x[2])[2]
        if gpu_dict['ttft_ms'] == expected_min_ttft:
            print(f"  ✅ TTFT聚合正确: {expected_min_ttft}")
        else:
            print(f"  ❌ TTFT聚合错误: 期望 {expected_min_ttft}, 实际 {gpu_dict['ttft_ms']}")

# 4. 检查是否有缓存问题
print("\n4. 检查缓存问题:")
from django.core.cache import cache

# 检查是否有相关的缓存键
cache_keys = [
    f'gpu_performance_MR-V100',
    f'gpu_rankings_cache',
    'gpu_performance_cache_all'
]

for key in cache_keys:
    cached_value = cache.get(key)
    if cached_value is not None:
        print(f"  发现缓存 {key}: {cached_value}")
    else:
        print(f"  缓存 {key}: 无数据")

print("\n=== 调试完成 ===")
print("如果发现聚合结果不正确，可能需要检查GPUDevice.to_dict()方法的具体实现。")
