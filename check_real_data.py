import os
import sys
import django

# 设置Django环境
sys.path.append('spug_api')
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'spug.settings')
django.setup()

from apps.model_storage.models import AverageStats, GPUDevice, PerformanceTestData

print("=== 检查真实数据 ===")

print("\n=== AverageStats表数据 ===")
print(f"总记录数: {AverageStats.objects.count()}")
for stats in AverageStats.objects.all():
    print(f"模型: {stats.model_name}, 吞吐量: {stats.avg_output_token_throughput}, TTFT: {stats.avg_ttft}, 数据量: {stats.data_count}")

print("\n=== GPUDevice表数据 ===")
print(f"总记录数: {GPUDevice.objects.count()}")
for gpu in GPUDevice.objects.all():
    print(f"GPU: {gpu.name}, 厂商: {gpu.vendor}, 吞吐量: {gpu.tokens_per_second}, TTFT: {gpu.ttft_ms}")

print("\n=== 搜索K100相关数据 ===")
k100_gpus = GPUDevice.objects.filter(name__icontains='K100')
print(f"K100相关GPU数量: {k100_gpus.count()}")
for gpu in k100_gpus:
    print(f"GPU: {gpu.name}, 厂商: {gpu.vendor}, 吞吐量: {gpu.tokens_per_second}, TTFT: {gpu.ttft_ms}")

print("\n=== PerformanceTestData表数据 ===")
print(f"总记录数: {PerformanceTestData.objects.count()}")
for data in PerformanceTestData.objects.all()[:5]:  # 只显示前5条
    print(f"ID: {data.id}, 模型: {data.model_name}, 机器: {data.machine_model}, 吞吐量: {data.output_token_throughput}, TTFT: {data.avg_ttft}")

print("\n=== 高吞吐量数据 (>100 tokens/s) ===")
high_throughput = PerformanceTestData.objects.filter(output_token_throughput__gt=100)
print(f"高吞吐量数据数量: {high_throughput.count()}")
for data in high_throughput:
    print(f"模型: {data.model_name}, 机器: {data.machine_model}, 吞吐量: {data.output_token_throughput}, TTFT: {data.avg_ttft}")

print("\n=== 所有PerformanceTestData详细信息 ===")
for data in PerformanceTestData.objects.all():
    print(f"ID: {data.id}, 模型: {data.model_name}, 机器: {data.machine_model}, 吞吐量: {data.output_token_throughput}")