import React, { useState, useEffect } from 'react';
import { Select, Spin, Empty, Badge } from 'antd';
import { TrophyOutlined, ThunderboltOutlined, ClockCircleOutlined } from '@ant-design/icons';
import { http } from 'libs';
import styles from './index.module.css';

const { Option } = Select;

export default function GpuRankings() {
  const [data, setData] = useState(null);
  const [loading, setLoading] = useState(false);
  const [rankingType, setRankingType] = useState('output_token_throughput');
  const [scope, setScope] = useState('all');
  const [groupBy, setGroupBy] = useState('overall');

  useEffect(() => {
    fetchRankings();
  }, [rankingType, scope, groupBy]);

  const fetchRankings = async () => {
    setLoading(true);
    try {
      const response = await http.get('/api/model-storage/gpu-rankings/', {
        params: {
          token: 1,
          ranking_type: rankingType,
          scope: scope,
          group_by: groupBy
        }
      });
      setData(response);
    } catch (error) {
      console.error('获取GPU排行榜数据失败:', error);
    } finally {
      setLoading(false);
    }
  };

  const formatValue = (value) => {
    if (value === null || value === undefined) return 'N/A';
    if (typeof value === 'number') {
      return value.toFixed(2);
    }
    return value;
  };

  const getRankingTitle = () => {
    switch (rankingType) {
      case 'output_token_throughput':
        return '输出Token吞吐量排行';
      case 'avg_ttft':
        return '平均首字延时(TTFT)排行';
      default:
        return 'GPU性能排行';
    }
  };

  const getRankingUnit = () => {
    switch (rankingType) {
      case 'output_token_throughput':
        return 'tokens/s';
      case 'avg_ttft':
        return 'ms';
      default:
        return '';
    }
  };

  const renderRankingItem = (item, index) => {
    const rank = index + 1;
    const rankClass = rank === 1 ? 'rank1' : rank === 2 ? 'rank2' : rank === 3 ? 'rank3' : '';
    
    return (
      <div key={`${item.gpu_model}-${index}`} className={`${styles.rankingItem} ${styles[rankClass]}`}>
        <div className={styles.rankNumber}>{rank}</div>
        <div className={styles.gpuInfo}>
          <div className={styles.gpuName}>{item.gpu_model}</div>
          <div className={styles.gpuVendor}>
            {item.vendor}
            {item.is_domestic && <span className={styles.domesticBadge}>国产</span>}
          </div>
          <div className={styles.gpuSpecs}>
            基于{item.test_count}个测试任务的{rankingType === 'output_token_throughput' ? '最佳' : '平均'}数据
          </div>
        </div>
        <div className={styles.performanceValue}>
          <div className={styles.valuePrimary}>
            {formatValue(rankingType === 'output_token_throughput' ? item.best_output_token_throughput : item.best_ttft_ms)}
            <span className={styles.valueUnit}>{getRankingUnit()}</span>
          </div>
          <div className={styles.valueSecondary}>
            {rankingType === 'output_token_throughput' ? '最佳值' : '平均TTFT'}
          </div>
        </div>
      </div>
    );
  };

  const renderVendorSection = (vendor, gpus) => {
    const vendorIcons = {
      'Meta': '🔥',
      '昆仑芯': '🔥', 
      '壁仞科技': '⚡',
      '摩尔线程': '💫',
      'NVIDIA': '🚀',
      'AMD': '⭐'
    };
    
    return (
      <div key={vendor} className={styles.vendorSection}>
        <div className={styles.vendorHeader}>
          <div className={styles.vendorLogo}>{vendorIcons[vendor] || '🏢'}</div>
          <div className={styles.vendorName}>{vendor}</div>
          <div className={styles.vendorStats}>
            {gpus.length}个型号 | {gpus.reduce((sum, gpu) => sum + gpu.test_count, 0)}个测试任务
          </div>
        </div>
        <div className={styles.rankingList}>
          {gpus.map((gpu, index) => renderRankingItem(gpu, index))}
        </div>
      </div>
    );
  };

  const groupedByVendor = data?.rankings ? 
    data.rankings.reduce((acc, gpu) => {
      if (!acc[gpu.vendor]) acc[gpu.vendor] = [];
      acc[gpu.vendor].push(gpu);
      return acc;
    }, {}) : {};

  return (
    <div className={styles.container}>
      {/* 页面标题 */}
      <div className={styles.header}>
        <h1>GPU性能排行榜</h1>
        <p>基于真实测试数据的GPU性能评估与排行</p>
        <div className={styles.rankingDescription}>
          <strong>排行依据：</strong>取每个GPU下面最高性能的模型数据作为GPU参赛依据
        </div>
      </div>

      {/* 控制器 */}
      <div className={styles.controls}>
        <div className={styles.controlGroup}>
          <label>排行类型：</label>
          <Select
            value={rankingType}
            onChange={setRankingType}
            style={{ minWidth: 180 }}
          >
            <Option value="output_token_throughput">
              <ThunderboltOutlined /> 输出Token吞吐量
            </Option>
            <Option value="avg_ttft">
              <ClockCircleOutlined /> 平均TTFT
            </Option>
          </Select>
        </div>
        
        <div className={styles.controlGroup}>
          <label>范围：</label>
          <Select
            value={scope}
            onChange={setScope}
            style={{ minWidth: 120 }}
          >
            <Option value="all">全部GPU</Option>
            <Option value="domestic">国产GPU</Option>
          </Select>
        </div>
        
        <div className={styles.controlGroup}>
          <label>分组方式：</label>
          <Select
            value={groupBy}
            onChange={setGroupBy}
            style={{ minWidth: 120 }}
          >
            <Option value="overall">总体排行</Option>
            <Option value="vendor">按厂商分组</Option>
          </Select>
        </div>
      </div>

      {loading ? (
        <div className={styles.loading}>
          <div className={styles.loadingSpinner}></div>
          <div>正在加载排行榜数据...</div>
        </div>
      ) : data && data.rankings && data.rankings.length > 0 ? (
        groupBy === 'overall' ? (
          <div className={styles.rankingsContainer}>
            {/* 主排行榜 */}
            <div className={styles.rankingPanel}>
              <div className={styles.panelHeader}>
                <div className={styles.panelIcon}>
                  {rankingType === 'output_token_throughput' ? <ThunderboltOutlined /> : <ClockCircleOutlined />}
                </div>
                <div className={styles.panelTitle}>{getRankingTitle()}</div>
              </div>
              
              <div className={styles.metricTabs}>
                <button className={`${styles.metricTab} ${styles.active}`}>
                  综合排行
                </button>
              </div>

              <div className={styles.rankingList}>
                {data.rankings.map((item, index) => renderRankingItem(item, index))}
              </div>
            </div>

            {/* 副排行榜（另一个指标） */}
            <div className={styles.rankingPanel}>
              <div className={styles.panelHeader}>
                <div className={styles.panelIcon}>
                  {rankingType === 'output_token_throughput' ? <ClockCircleOutlined /> : <ThunderboltOutlined />}
                </div>
                <div className={styles.panelTitle}>
                  {rankingType === 'output_token_throughput' ? '首字延时(TTFT)排行' : '输出吞吐量排行'}
                </div>
              </div>
              
              <div className={styles.metricTabs}>
                <button className={`${styles.metricTab} ${styles.active}`}>
                  综合排行
                </button>
              </div>

              <div className={styles.rankingList}>
                {data.rankings.map((item, index) => {
                  const altItem = {
                    ...item,
                    // 交换显示的指标
                    displayValue: rankingType === 'output_token_throughput' ? item.best_ttft_ms : item.best_output_token_throughput,
                    displayUnit: rankingType === 'output_token_throughput' ? 'ms' : 'tok/s',
                    displayLabel: rankingType === 'output_token_throughput' ? '平均TTFT' : '最佳值'
                  };
                  
                  return (
                    <div key={`alt-${item.gpu_model}-${index}`} className={`${styles.rankingItem} ${index < 3 ? styles[`rank${index + 1}`] : ''}`}>
                      <div className={styles.rankNumber}>{index + 1}</div>
                      <div className={styles.gpuInfo}>
                        <div className={styles.gpuName}>{item.gpu_model}</div>
                        <div className={styles.gpuVendor}>
                          {item.vendor}
                          {item.is_domestic && <span className={styles.domesticBadge}>国产</span>}
                        </div>
                        <div className={styles.gpuSpecs}>
                          基于{item.test_count}个测试任务的平均数据
                        </div>
                      </div>
                      <div className={styles.performanceValue}>
                        <div className={styles.valuePrimary}>
                          {formatValue(altItem.displayValue)}
                          <span className={styles.valueUnit}>{altItem.displayUnit}</span>
                        </div>
                        <div className={styles.valueSecondary}>
                          {altItem.displayLabel}
                        </div>
                      </div>
                    </div>
                  );
                })}
              </div>
            </div>
          </div>
        ) : (
          /* 厂商分组展示 */
          <div className={styles.rankingPanel}>
            <div className={styles.panelHeader}>
              <div className={styles.panelIcon}>🏢</div>
              <div className={styles.panelTitle}>厂商内排行</div>
            </div>
            {Object.entries(groupedByVendor).map(([vendor, gpus]) => 
              renderVendorSection(vendor, gpus)
            )}
          </div>
        )
      ) : (
        <div className={styles.emptyState}>
          <Empty 
            description="暂无排行榜数据"
            image={Empty.PRESENTED_IMAGE_SIMPLE}
          />
        </div>
      )}
    </div>
  );
}