#!/usr/bin/env python
import os
import sys
import django

# 设置Django环境
sys.path.append('spug_api')
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'spug.settings')
django.setup()

from apps.model_storage.models import GPUDevice, TestTask, PerformanceTestData

print("=== 验证修复结果 ===")

# 1. 验证MR-V100的数据
print("\n1. 验证MR-V100的数据:")
mr_v100_gpu = GPUDevice.objects.filter(name='MR-V100').first()
if mr_v100_gpu:
    print(f"MR-V100 GPU信息:")
    print(f"  ID: {mr_v100_gpu.id}")
    print(f"  厂商: {mr_v100_gpu.vendor}")
    
    # 获取to_dict()结果
    gpu_dict = mr_v100_gpu.to_dict()
    print(f"  性能数据 (to_dict()):")
    print(f"    tokens_per_second: {gpu_dict['tokens_per_second']}")
    print(f"    ttft_ms: {gpu_dict['ttft_ms']}")
    print(f"    tested_models_count: {gpu_dict['tested_models_count']}")
    print(f"    tested_models_auto: {gpu_dict['tested_models_auto']}")
    
    # 检查关联的TestTask
    test_tasks = TestTask.objects.filter(gpu_model='MR-V100')
    print(f"  关联的TestTask数量: {test_tasks.count()}")
    for task in test_tasks:
        print(f"    任务ID {task.id}: {task.model_name}, tokens/s={task.tokens_per_second}, ttft_ms={task.ttft_ms}")
    
    # 检查关联的PerformanceTestData
    perf_data = PerformanceTestData.objects.filter(gpu_device=mr_v100_gpu)
    print(f"  关联的PerformanceTestData数量: {perf_data.count()}")
    for data in perf_data:
        print(f"    记录ID {data.id}: {data.model_name}, tokens/s={data.output_token_throughput}, ttft_ms={data.avg_ttft}")

# 2. 验证其他GPU的数据
print("\n2. 验证其他有性能数据的GPU:")
gpus_with_data = []
for gpu in GPUDevice.objects.all():
    gpu_dict = gpu.to_dict()
    if gpu_dict['tokens_per_second'] > 0 or gpu_dict['ttft_ms'] > 0:
        gpus_with_data.append((gpu, gpu_dict))

print(f"有性能数据的GPU数量: {len(gpus_with_data)}")
for gpu, gpu_dict in gpus_with_data:
    print(f"  {gpu.name} ({gpu.vendor}): tokens/s={gpu_dict['tokens_per_second']}, ttft_ms={gpu_dict['ttft_ms']}")

# 3. 验证GPU排行榜数据
print("\n3. 模拟GPU排行榜计算:")
rankings = []
for gpu in GPUDevice.objects.all():
    gpu_dict = gpu.to_dict()
    if gpu_dict['tokens_per_second'] > 0:
        rankings.append({
            'gpu_model': gpu.name,
            'vendor': gpu.vendor,
            'best_output_token_throughput': gpu_dict['tokens_per_second'],
            'best_ttft_ms': gpu_dict['ttft_ms'],
            'test_count': gpu_dict['tested_models_count']
        })

# 按吞吐量排序
rankings.sort(key=lambda x: x['best_output_token_throughput'], reverse=True)

print(f"GPU排行榜 (按吞吐量排序):")
for i, ranking in enumerate(rankings[:10], 1):
    print(f"  {i}. {ranking['gpu_model']} ({ranking['vendor']})")
    print(f"     吞吐量: {ranking['best_output_token_throughput']} tokens/s")
    print(f"     TTFT: {ranking['best_ttft_ms']} ms")
    print(f"     测试模型数: {ranking['test_count']}")

print("\n=== 验证完成 ===")
print("✅ 数据修复成功！现在GPU设备的性能数据应该能正确显示了。")
