/**
 * AI智能分析设置
 */
import React, { useState, useEffect } from 'react';
import { 
  Card, 
  Form, 
  Input, 
  Button, 
  Switch, 
  message, 
  Space, 
  Divider,
  Alert,
  Tag,
  Spin,
  Modal,
  Typography
} from 'antd';
import {
  RobotOutlined,
  CheckCircleOutlined,
  ExclamationCircleOutlined,
  ExperimentOutlined,
  SettingOutlined
} from '@ant-design/icons';
import { http } from 'libs';

const { Title, Paragraph, Text } = Typography;

function AISetting() {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [testing, setTesting] = useState(false);
  const [aiStatus, setAiStatus] = useState(null);
  const [testModalVisible, setTestModalVisible] = useState(false);
  const [testResult, setTestResult] = useState(null);

  // 加载AI配置
  useEffect(() => {
    loadAIConfig();
    checkAIStatus();
  }, []);

  const loadAIConfig = async () => {
    try {
      // 从后端加载AI配置
      const response = await http.get('/api/ai/config/');
      form.setFieldsValue({
        ai_enabled: response.ai_enabled,
        api_base: response.api_base,
        api_key: response.api_key,
        model_name: response.model_name,
        max_tokens: response.max_tokens,
        temperature: response.temperature,
        cache_enabled: response.cache_enabled,
        cache_ttl: response.cache_ttl
      });
    } catch (error) {
      message.error('加载AI配置失败');
      // 如果加载失败，使用默认值
      form.setFieldsValue({
        ai_enabled: false,
        api_base: 'https://api-inference.modelscope.cn/v1',
        model_name: 'qwen/Qwen2.5-72B-Instruct',
        max_tokens: 4000,
        temperature: 0.7,
        cache_enabled: true,
        cache_ttl: 3600
      });
    }
  };

  const checkAIStatus = async () => {
    try {
      // 检查AI服务状态
      const response = await http.get('/api/ai/status/');
      setAiStatus(response);
    } catch (error) {
      setAiStatus({ available: false, error: '无法连接到AI服务' });
    }
  };

  const handleSave = async () => {
    try {
      const values = await form.validateFields();
      setLoading(true);
      
      // 保存AI配置到后端
      await http.post('/api/ai/config/', values);
      message.success('AI配置保存成功');
      
      // 重新检查状态
      checkAIStatus();
    } catch (error) {
      message.error('保存配置失败');
    } finally {
      setLoading(false);
    }
  };

  const handleTest = async () => {
    setTesting(true);
    setTestModalVisible(true);
    setTestResult(null);

    try {
      // 测试AI分析功能
      const testLog = `2025-07-27 12:30:15 INFO Application started successfully
2025-07-27 12:30:16 INFO Processing request: GET /api/users
2025-07-27 12:30:16 INFO Response time: 125ms
2025-07-27 12:30:17 ERROR Database connection timeout after 5000ms
2025-07-27 12:30:18 INFO Database connection restored`;

      const response = await http.post('/api/ai/test/', {
        content: testLog,
        filename: 'test.log'
      });

      setTestResult(response);
      message.success('AI测试完成');
    } catch (error) {
      setTestResult({
        success: false,
        error: error.message || '测试失败'
      });
      message.error('AI测试失败');
    } finally {
      setTesting(false);
    }
  };

  const renderStatusTag = () => {
    if (!aiStatus) {
      return <Tag color="default">检查中...</Tag>;
    }
    
    if (aiStatus.available) {
      return (
        <Tag color="success" icon={<CheckCircleOutlined />}>
          服务正常
        </Tag>
      );
    } else {
      return (
        <Tag color="error" icon={<ExclamationCircleOutlined />}>
          服务异常
        </Tag>
      );
    }
  };

  return (
    <div style={{ padding: '24px' }}>
      <Title level={3}>
        <RobotOutlined style={{ marginRight: '8px' }} />
        AI智能分析设置
      </Title>
      
      <Alert
        message="AI智能分析功能说明"
        description="AI智能分析可以自动分析日志内容，提供性能评估、异常检测、改进建议和深度洞察。启用此功能需要配置AI服务接口。"
        type="info"
        showIcon
        style={{ marginBottom: '24px' }}
      />

      <Card 
        title={
          <Space>
            <SettingOutlined />
            <span>基础配置</span>
            {renderStatusTag()}
          </Space>
        }
        extra={
          <Space>
            <Button
              icon={<ExperimentOutlined />}
              onClick={handleTest}
              loading={testing}
            >
              测试连接
            </Button>
            <Button 
              type="primary" 
              onClick={handleSave}
              loading={loading}
            >
              保存配置
            </Button>
          </Space>
        }
      >
        <Form
          form={form}
          layout="vertical"
          initialValues={{
            ai_enabled: true,
            api_base: 'https://api-inference.modelscope.cn/v1',
            model_name: 'qwen/Qwen2.5-72B-Instruct',
            max_tokens: 4000,
            temperature: 0.7,
            cache_enabled: true,
            cache_ttl: 3600
          }}
        >
          <Form.Item
            name="ai_enabled"
            label="启用AI智能分析"
            valuePropName="checked"
          >
            <Switch />
          </Form.Item>

          <Divider />

          <Form.Item
            name="api_base"
            label="API基础地址"
            rules={[{ required: true, message: '请输入API基础地址' }]}
          >
            <Input placeholder="https://api-inference.modelscope.cn/v1" />
          </Form.Item>

          <Form.Item
            name="api_key"
            label="API密钥"
            rules={[{ required: true, message: '请输入API密钥' }]}
          >
            <Input.Password placeholder="请输入API密钥" />
          </Form.Item>

          <Form.Item
            name="model_name"
            label="模型名称"
            rules={[{ required: true, message: '请输入模型名称' }]}
          >
            <Input placeholder="qwen/Qwen2.5-72B-Instruct" />
          </Form.Item>

          <Form.Item
            name="max_tokens"
            label="最大Token数"
            rules={[{ required: true, message: '请输入最大Token数' }]}
          >
            <Input type="number" placeholder="4000" />
          </Form.Item>

          <Form.Item
            name="temperature"
            label="温度参数"
            rules={[{ required: true, message: '请输入温度参数' }]}
          >
            <Input type="number" step="0.1" placeholder="0.7" />
          </Form.Item>

          <Divider />

          <Form.Item
            name="cache_enabled"
            label="启用结果缓存"
            valuePropName="checked"
          >
            <Switch />
          </Form.Item>

          <Form.Item
            name="cache_ttl"
            label="缓存过期时间(秒)"
            rules={[{ required: true, message: '请输入缓存过期时间' }]}
          >
            <Input type="number" placeholder="3600" />
          </Form.Item>
        </Form>
      </Card>

      {/* AI测试结果弹窗 */}
      <Modal
        title={
          <Space>
            <ExperimentOutlined />
            <span>AI功能测试结果</span>
          </Space>
        }
        visible={testModalVisible}
        onCancel={() => setTestModalVisible(false)}
        footer={[
          <Button key="close" onClick={() => setTestModalVisible(false)}>
            关闭
          </Button>
        ]}
        width={800}
      >
        {testing ? (
          <div style={{ textAlign: 'center', padding: '40px' }}>
            <Spin size="large" />
            <div style={{ marginTop: '16px' }}>正在测试AI分析功能...</div>
          </div>
        ) : testResult ? (
          <div>
            {testResult.success ? (
              <div>
                <Alert
                  message="测试成功"
                  description="AI服务连接正常，分析功能工作正常"
                  type="success"
                  showIcon
                  style={{ marginBottom: '16px' }}
                />
                
                {testResult.ai_analysis && (
                  <div>
                    <Title level={5}>分析结果示例：</Title>
                    <div style={{ background: '#f5f5f5', padding: '12px', borderRadius: '6px' }}>
                      <div><strong>性能评分：</strong>{(testResult.ai_analysis.performance_assessment && testResult.ai_analysis.performance_assessment.score) || 'N/A'}/100</div>
                      <div><strong>检测异常：</strong>{(testResult.ai_analysis.anomalies && testResult.ai_analysis.anomalies.length) || 0} 个</div>
                      <div><strong>改进建议：</strong>{(testResult.ai_analysis.recommendations && testResult.ai_analysis.recommendations.length) || 0} 条</div>
                    </div>
                  </div>
                )}
              </div>
            ) : (
              <Alert
                message="测试失败"
                description={testResult.error || '未知错误'}
                type="error"
                showIcon
              />
            )}
          </div>
        ) : null}
      </Modal>
    </div>
  );
}

export default AISetting;
