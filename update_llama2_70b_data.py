#!/usr/bin/env python
import os
import sys
import django

# 设置Django环境
sys.path.append('spug_api')
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'spug.settings')
django.setup()

from apps.model_storage.models import TestTask, GPUDevice

print("=== 更新Llama2-70B的性能数据 ===")

# 1. 找到Llama2-70B的TestTask
llama2_70b_task = TestTask.objects.filter(gpu_model='MR-V100', model_name='Llama2-70B').first()

if llama2_70b_task:
    print(f"找到Llama2-70B任务: ID={llama2_70b_task.id}")
    print(f"当前性能数据: tokens/s={llama2_70b_task.tokens_per_second}, ttft_ms={llama2_70b_task.ttft_ms}")
    
    # 更新为您截图中显示的数据
    llama2_70b_task.tokens_per_second = 999.0  # 更高的吞吐量
    llama2_70b_task.ttft_ms = 888.0  # 虽然这个TTFT比较高，但我们先按您的数据更新
    llama2_70b_task.save()
    
    print(f"已更新Llama2-70B性能数据: tokens/s=999.0, ttft_ms=888.0")
    
    # 检查MR-V100 GPU的聚合结果
    mr_v100_gpu = GPUDevice.objects.filter(name='MR-V100').first()
    if mr_v100_gpu:
        gpu_dict = mr_v100_gpu.to_dict()
        print(f"\n更新后MR-V100的聚合性能:")
        print(f"  tokens_per_second: {gpu_dict['tokens_per_second']} (应该是999.0)")
        print(f"  ttft_ms: {gpu_dict['ttft_ms']} (应该是200.0，因为200 < 888)")
        
        # 验证聚合逻辑
        if gpu_dict['tokens_per_second'] == 999.0:
            print("  ✅ 吞吐量聚合正确：取到了最高值999.0")
        else:
            print(f"  ❌ 吞吐量聚合错误：期望999.0，实际{gpu_dict['tokens_per_second']}")
            
        if gpu_dict['ttft_ms'] == 200.0:
            print("  ✅ TTFT聚合正确：取到了最低值200.0")
        else:
            print(f"  ❌ TTFT聚合错误：期望200.0，实际{gpu_dict['ttft_ms']}")
    
else:
    print("未找到Llama2-70B任务")

# 2. 同时更新Llama2-7B的数据，让它有更好的TTFT
llama2_7b_task = TestTask.objects.filter(gpu_model='MR-V100', model_name='Llama2-7B').first()
if llama2_7b_task:
    print(f"\n找到Llama2-7B任务: ID={llama2_7b_task.id}")
    
    # 给它一个更低的TTFT，但较低的吞吐量
    llama2_7b_task.tokens_per_second = 88.0  # 较低的吞吐量
    llama2_7b_task.ttft_ms = 77.0  # 更低的TTFT（更好）
    llama2_7b_task.save()
    
    print(f"已更新Llama2-7B性能数据: tokens/s=88.0, ttft_ms=77.0")
    
    # 再次检查聚合结果
    mr_v100_gpu = GPUDevice.objects.filter(name='MR-V100').first()
    if mr_v100_gpu:
        gpu_dict = mr_v100_gpu.to_dict()
        print(f"\n最终MR-V100的聚合性能:")
        print(f"  tokens_per_second: {gpu_dict['tokens_per_second']} (应该是999.0，来自Llama2-70B)")
        print(f"  ttft_ms: {gpu_dict['ttft_ms']} (应该是77.0，来自Llama2-7B)")
        
        # 验证最终聚合逻辑
        if gpu_dict['tokens_per_second'] == 999.0:
            print("  ✅ 最终吞吐量聚合正确：取到了最高值999.0")
        else:
            print(f"  ❌ 最终吞吐量聚合错误：期望999.0，实际{gpu_dict['tokens_per_second']}")
            
        if gpu_dict['ttft_ms'] == 77.0:
            print("  ✅ 最终TTFT聚合正确：取到了最低值77.0")
        else:
            print(f"  ❌ 最终TTFT聚合错误：期望77.0，实际{gpu_dict['ttft_ms']}")

print("\n=== 更新完成 ===")
print("现在MR-V100应该显示最高的吞吐量和最低的TTFT时间。")
print("请刷新前端页面查看效果。")
