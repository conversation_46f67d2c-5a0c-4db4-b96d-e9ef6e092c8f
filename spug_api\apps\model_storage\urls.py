from django.urls import path
from . import views

urlpatterns = [
    # 服务器监控
    path('server-metrics/', views.ServerMetricsView.as_view(), name='server_metrics'),
    
    # Git风格文件对比功能
    path('file-compare/', views.FileCompareView.as_view(), name='file_compare'),
    path('check-differences/', views.CheckDifferencesView.as_view(), name='check_differences'),
    path('sync-single-file/', views.SyncSingleFileView.as_view(), name='sync_single_file'),
    path('batch-sync/', views.BatchSyncView.as_view(), name='batch_sync'),
    
    # 性能优化API
    path('lazy-load-tree/', views.LazyLoadTreeView.as_view(), name='lazy_load_tree'),
    path('async-scan/', views.AsyncScanView.as_view(), name='async_scan'),
    path('remote-lazy-load/', views.RemoteTreeDetailView.as_view(), name='remote_lazy_load'),
    
    # 新增：详细对比相关API
    path('remote-tree-detail/', views.RemoteTreeDetailView.as_view(), name='remote_tree_detail'),
    
    # 原有API（保持兼容性）
    path('file-tree/', views.FileTreeView.as_view(), name='file_tree'),
    path('release-plans/', views.ReleasePlanView.as_view(), name='release_plans'),
    path('release-plans/<int:pk>/', views.ReleasePlanView.as_view()),
    path('svn-compare/', views.SvnCompareView.as_view(), name='svn_compare'),
    path('check-missing/', views.CheckMissingView.as_view(), name='check_missing'),
    path('local-full-tree/', views.LocalFullTreeView.as_view()),
    path('remote-full-tree/', views.RemoteFullTreeView.as_view()),
    
    # ============= 新版发布计划相关API =============
    # 发布计划管理
    path('new-release-plans/', views.NewReleasePlanView.as_view(), name='new_release_plans'),
    path('new-release-plans/<int:pk>/', views.NewReleasePlanView.as_view(), name='new_release_plan_detail'),

    # 测试任务管理
    path('test-tasks/', views.TestTaskView.as_view(), name='test_tasks'),
    path('test-tasks/<int:pk>/', views.TestTaskView.as_view(), name='test_task_detail'),
    path('test-tasks/<int:task_id>/step-status/', views.TestTaskStepStatusView.as_view(), name='test_task_step_status'),
    path('import-tasks/', views.ImportTestTaskView.as_view(), name='import_tasks'),
    
    # 风险预警管理
    path('risk-alerts/', views.RiskAlertView.as_view(), name='risk_alerts'),
    path('risk-alerts/<int:pk>/', views.RiskAlertView.as_view()),
    
    # 甘特图数据
    path('gantt-data/', views.GanttDataView.as_view(), name='gantt_data'),
    
    # 统计数据
    path('statistics/', views.StatisticsView.as_view(), name='statistics'),

    # 监控概览统计数据
    path('dashboard-statistics/', views.DashboardStatisticsView.as_view(), name='dashboard_statistics'),
    
    # 周报导出
    path('weekly-report/', views.WeeklyReportView.as_view(), name='weekly_report'),
    
    # 文档统计功能
    path('doc-statistics/', views.DocStatisticsView.as_view(), name='doc_statistics'),

    # GPU 管理
    path('gpus/', views.GPUView.as_view(), name='gpus'),
    path('gpus/<int:pk>/', views.GPUView.as_view(), name='gpu_detail'),

    # 模型数据管理
    path('model-data/', views.ModelDataView.as_view(), name='model_data'),
    path('model-data/<int:pk>/', views.ModelDataView.as_view(), name='model_data_detail'),

    # 性能测试数据管理
    path('performance-test-data/', views.PerformanceTestDataView.as_view(), name='performance_test_data'),
    path('performance-test-data/<int:pk>/', views.PerformanceTestDataView.as_view(), name='performance_test_data_detail'),
    path('performance-test-data/batch/', views.PerformanceTestDataBatchView.as_view(), name='performance_test_data_batch'),

    # 模型性能数据管理（新版 - 一个模型一条记录）
    path('model-performance-data/', views.ModelPerformanceDataView.as_view(), name='model_performance_data'),
    path('model-performance-data/<int:pk>/', views.ModelPerformanceDataView.as_view(), name='model_performance_data_detail'),
    path('model-performance-data/batch/', views.ModelPerformanceDataBatchView.as_view(), name='model_performance_data_batch'),

    # 用户搜索 - 用于任务分配
    path('users/search/', views.UserSearchView.as_view(), name='user_search'),

    # 任务分配
    path('tasks/assign/', views.TaskAssignView.as_view(), name='task_assign'),

    # 企业微信配置管理
    path('wechat/config/', views.WeChatConfigView.as_view(), name='wechat_config'),

    # 测试计划统计
    path('test-plan-statistics/', views.TestPlanStatisticsView.as_view(), name='test_plan_statistics'),

    # ============= Word模板管理相关API =============
    # Word模板管理
    path('word-templates/', views.WordTemplateView.as_view(), name='word_templates'),
    path('word-templates/<int:pk>/', views.WordTemplateView.as_view(), name='word_template_detail'),
    path('word-templates/reimport/', views.WordTemplateReimportView.as_view(), name='word_template_reimport'),
    path('word-templates/generate/', views.WordTemplateGenerateView.as_view(), name='word_template_generate'),

    # 文档实例管理
    path('document-instances/', views.DocumentInstanceView.as_view(), name='document_instances'),
    path('document-instances/<int:pk>/', views.DocumentInstanceView.as_view(), name='document_instance_detail'),

    # 模板变量解析
    path('template-variables/parse/', views.TemplateVariableParseView.as_view(), name='template_variable_parse'),

    # 文档相关API
    path('documents/generate/', views.DocumentGenerateView.as_view(), name='document_generate'),
    # 文档下载API
    path('documents/download/', views.DocumentDownloadView.as_view(), name='document_download'),
    path('documents/download-temp/', views.DocumentTempDownloadView.as_view(), name='document_temp_download'),
    
    # GPU排行榜
    path('gpu-rankings/', views.GPURankingView.as_view(), name='gpu_rankings'),

    # GPU性能数据刷新
    path('gpu-performance-refresh/', views.GPUPerformanceRefreshView.as_view(), name='gpu_performance_refresh'),

    # 平均值统计
    path('average-stats/', views.AverageStatsView.as_view(), name='average_stats'),
]