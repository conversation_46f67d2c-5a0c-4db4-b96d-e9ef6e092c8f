#!/usr/bin/env python
import os
import sys
import django

# 设置Django环境
sys.path.append('spug_api')
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'spug.settings')
django.setup()

from apps.model_storage.models import TestTask, GPUDevice, PerformanceTestData
from apps.model_storage.signals import trigger_gpu_performance_refresh
import time

print("=== 测试GPU性能数据自动刷新机制 ===")

# 1. 获取MR-V100的当前性能数据
print("\n1. 获取MR-V100的当前性能数据:")
mr_v100_gpu = GPUDevice.objects.filter(name='MR-V100').first()
if mr_v100_gpu:
    gpu_dict_before = mr_v100_gpu.to_dict()
    print(f"更新前: tokens/s={gpu_dict_before['tokens_per_second']}, ttft_ms={gpu_dict_before['ttft_ms']}")
else:
    print("未找到MR-V100 GPU")
    exit(1)

# 2. 更新TestTask的性能数据
print("\n2. 更新TestTask的性能数据:")
test_task = TestTask.objects.filter(gpu_model='MR-V100', model_name='DeepSeek-R1满血推理').first()
if test_task:
    print(f"找到测试任务: ID={test_task.id}, 当前tokens/s={test_task.tokens_per_second}, ttft_ms={test_task.ttft_ms}")
    
    # 更新为更高的性能数据
    new_tokens_per_second = 120.0  # 比原来的88更高
    new_ttft_ms = 300.0  # 比原来的424更低（更好）
    
    test_task.tokens_per_second = new_tokens_per_second
    test_task.ttft_ms = new_ttft_ms
    test_task.save()  # 这里应该触发信号
    
    print(f"已更新测试任务: tokens/s={new_tokens_per_second}, ttft_ms={new_ttft_ms}")
else:
    print("未找到相关测试任务")
    exit(1)

# 3. 等待一下让信号处理完成
print("\n3. 等待信号处理完成...")
time.sleep(1)

# 4. 重新获取GPU性能数据，检查是否自动更新
print("\n4. 检查GPU性能数据是否自动更新:")
mr_v100_gpu.refresh_from_db()  # 刷新数据库数据
gpu_dict_after = mr_v100_gpu.to_dict()  # 重新计算性能数据
print(f"更新后: tokens/s={gpu_dict_after['tokens_per_second']}, ttft_ms={gpu_dict_after['ttft_ms']}")

# 5. 比较更新前后的数据
print("\n5. 比较更新前后的数据:")
tokens_changed = gpu_dict_before['tokens_per_second'] != gpu_dict_after['tokens_per_second']
ttft_changed = gpu_dict_before['ttft_ms'] != gpu_dict_after['ttft_ms']

print(f"tokens/s 是否变化: {tokens_changed} ({gpu_dict_before['tokens_per_second']} -> {gpu_dict_after['tokens_per_second']})")
print(f"ttft_ms 是否变化: {ttft_changed} ({gpu_dict_before['ttft_ms']} -> {gpu_dict_after['ttft_ms']})")

if tokens_changed or ttft_changed:
    print("✅ 自动刷新机制工作正常！")
else:
    print("❌ 自动刷新机制可能有问题，数据没有更新")

# 6. 测试手动刷新功能
print("\n6. 测试手动刷新功能:")
success = trigger_gpu_performance_refresh(['MR-V100'])
if success:
    print("✅ 手动刷新功能工作正常")
else:
    print("❌ 手动刷新功能有问题")

# 7. 创建一个新的PerformanceTestData记录测试
print("\n7. 测试PerformanceTestData更新触发刷新:")
try:
    # 创建一个新的性能测试数据
    new_perf_data = PerformanceTestData.objects.create(
        model_name='DeepSeek-R1满血推理',
        filename='test_auto_refresh.json',
        output_token_throughput=150.0,  # 更高的吞吐量
        avg_ttft=250.0,  # 更低的TTFT
        machine_model='MR-V100',
        gpu_device=mr_v100_gpu
    )
    print(f"创建了新的PerformanceTestData: ID={new_perf_data.id}")
    
    # 等待信号处理
    time.sleep(1)
    
    # 重新检查GPU性能数据
    gpu_dict_final = mr_v100_gpu.to_dict()
    print(f"创建PerformanceTestData后: tokens/s={gpu_dict_final['tokens_per_second']}, ttft_ms={gpu_dict_final['ttft_ms']}")
    
    # 清理测试数据
    new_perf_data.delete()
    print("已清理测试数据")
    
except Exception as e:
    print(f"测试PerformanceTestData更新失败: {e}")

print("\n=== 测试完成 ===")
print("如果看到'自动刷新机制工作正常'，说明信号机制已经生效！")
print("现在当您更新模型测试数据时，GPU页面和排行榜应该会自动反映最新的性能数据。")
